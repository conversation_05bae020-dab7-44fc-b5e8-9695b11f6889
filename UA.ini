import os
from fake_useragent import UserAgent
from datetime import datetime


def generate_unique_ua(num_ua):
    """
    生成指定数量的不重复用户代理字符串
    """
    ua = UserAgent()
    unique_ua = set()

    # 安全计数器防止无限循环
    max_attempts = num_ua * 10
    attempts = 0

    while len(unique_ua) < num_ua and attempts < max_attempts:
        try:
            # 生成随机UA并添加到集合
            new_ua = ua.random
            unique_ua.add(new_ua)
        except Exception as e:
            print(f"生成UA时出错: {e}")
        attempts += 1

    if len(unique_ua) < num_ua:
        print(f"警告: 只生成了 {len(unique_ua)} 个不重复UA，未达到请求数量")

    return list(unique_ua)


def save_ua_to_file(ua_list, file_path):
    """
    将UA列表保存到文件，每行一个（纯Python列表格式）
    """
    try:
        with open(file_path, 'w') as f:
            # 不再包含变量声明，直接输出UA列表内容
            for agent in ua_list:
                f.write(f"'{agent}',\n")
        print(f"成功保存 {len(ua_list)} 个UA到 {file_path}")
    except Exception as e:
        print(f"保存文件时出错: {e}")


def generate_ua_by_category(category, num_ua):
    ua = UserAgent()
    unique_ua = set()
    max_attempts = num_ua * 20
    attempts = 0

    while len(unique_ua) < num_ua and attempts < max_attempts:
        try:
            new_ua = ua.random
            if category == "Windows Chrome" and "Windows" in new_ua and "Chrome" in new_ua and "Edg" not in new_ua:
                unique_ua.add(new_ua)
            elif category == "Windows Firefox" and "Windows" in new_ua and "Firefox" in new_ua:
                unique_ua.add(new_ua)
            elif category == "Windows Edge" and "Windows" in new_ua and "Edg" in new_ua:
                unique_ua.add(new_ua)
            elif category == "Mac Chrome" and "Macintosh" in new_ua and "Chrome" in new_ua:
                unique_ua.add(new_ua)
            elif category == "Mac Safari" and "Macintosh" in new_ua and "Safari" in new_ua and "Chrome" not in new_ua:
                unique_ua.add(new_ua)
            elif category == "Android Chrome" and "Android" in new_ua and "Chrome" in new_ua:
                unique_ua.add(new_ua)
            elif category == "iOS Safari" and "iPhone" in new_ua and "Safari" in new_ua:
                unique_ua.add(new_ua)
        except Exception as e:
            print(f"生成UA时出错: {e}")
        attempts += 1

    if len(unique_ua) < num_ua:
        print(f"警告: {category} 只生成了 {len(unique_ua)} 个，未达到请求数量")
    return list(unique_ua)


def main():
    # 选择生成模式
    print("请选择生成模式：1-随机生成，2-按分类生成")
    while True:
        try:
            GEN_MODE = int(input("输入1或2：").strip())
            if GEN_MODE in [1, 2]:
                break
            else:
                print("请输入1或2！")
        except Exception:
            print("请输入数字1或2！")

    all_ua = []
    if GEN_MODE == 1:
        # 随机生成
        while True:
            try:
                NUM_UA = int(input("请输入要生成的UA数量（100-1000）：").strip())
                if 100 <= NUM_UA <= 1000:
                    break
                else:
                    print("数量必须在100-1000之间！")
            except Exception:
                print("请输入有效数字！")
        print(f"正在随机生成 {NUM_UA} 个唯一用户代理...")
        all_ua = generate_unique_ua(NUM_UA)
    elif GEN_MODE == 2:
        # 分类生成
        default_categories = {
            "Windows Chrome": 20,
            "Windows Firefox": 20,
            "Windows Edge": 20,
            "Mac Chrome": 20,
            "Mac Safari": 20,
            "Android Chrome": 20,
            "iOS Safari": 20
        }
        UA_CATEGORIES = {}
        print("请为每个分类输入要生成的UA数量（回车默认20）：")
        for cat, default_num in default_categories.items():
            while True:
                val = input(f"{cat} 数量（默认{default_num}）：").strip()
                if val == '':
                    UA_CATEGORIES[cat] = default_num
                    break
                try:
                    num = int(val)
                    if num >= 0:  # 允许为0
                        UA_CATEGORIES[cat] = num
                        break
                    else:
                        print("请输入0或更大的数字！")
                except Exception:
                    print("请输入有效数字或直接回车！")
        for category, num in UA_CATEGORIES.items():
            if num == 0:
                continue  # 跳过数量为0的分类
            print(f"正在生成 {category} 的UA，共 {num} 个...")
            ua_list = generate_ua_by_category(category, num)
            all_ua.extend(ua_list)
    else:
        print("错误: 请选择正确的生成模式（1或2）")
        return

    # 获取当前日期时间到秒
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"UA-{timestamp}.txt"
    output_path = os.path.join(r"C:\Users\<USER>\Downloads", filename)
    save_ua_to_file(all_ua, output_path)


if __name__ == "__main__":
    main()