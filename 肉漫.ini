import os
import json
import time
import random
import requests
import threading
import shutil
import re
from bs4 import BeautifulSoup
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urljoin
import img2pdf
from smb.SMBConnection import SMBConnection
from PIL import Image
import io
import PyPDF2
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)


# ================== 辅助函数 ==================
def sanitize_filename(filename):
    """清理文件名中不符合Windows和Debian系统要求的字符"""
    # 替换Windows和Linux不允许的字符
    invalid_chars = r'[<>:"/\\|?*\x00-\x1F]'
    sanitized = re.sub(invalid_chars, '', filename)
    # 处理特殊情况：文件名开头或结尾的空格和点
    sanitized = sanitized.strip(' .')
    # 确保文件名不为空
    if not sanitized:
        sanitized = "unnamed"
    return sanitized


# ================== 配置区域 ==================
CONFIG = {
    'BASE_DIR': os.path.join(r'D:\ComicsDownloads', '肉漫天堂下载'),
    'THREADS': {
        'max_workers': 2,
        'comic_workers': 2,
        'chapter_workers': 2,
        'image_workers': 2
    },
    'RETRY': {
        'max_retries': 3,
        'retry_delay': 5
    },
    'DELAY': (1, 3),
    'HEADERS': {
        'Referer': 'https://rmtt7.com/',
        'Accept-Encoding': 'gzip, deflate, br'
    },
    'NAS_CONFIG': {
        'enabled': True,
        'server_ip': '**************',
        'server_name': 'NAS',
        'share_name': '小说',
        'username': 'admin',
        'password': 'ly120220',
        'target_dir': '/漫画/肉漫天堂',
        'client_name': 'ComicDownloader'
    },
    'CATEGORY_MAP': {
        '韩漫': '肉漫天堂韩漫文件夹',
        '日漫': '肉漫天堂日漫文件夹',
        '3D漫画': '肉漫天堂3D漫画文件夹',
        '真人': '肉漫天堂真人文件夹',
        '短篇': '肉漫天堂短篇文件夹'
    },
    'CATEGORY_PREFIX_MAP': {
        '韩漫': '韩漫',
        '日漫': '日漫',
        '3D漫画': '3D',
        '真人': '真人',
        '短篇': '短篇',
        '未知分类': ''
    },
    'C_MODE_URLS': {
        'C0': 'https://rmtt7.com/comics-cata/all/ob/time/st/all',
        'C1': 'https://rmtt7.com/comics-cata/%E9%9F%A9%E6%BC%AB/ob/time/st/all',
        'C2': 'https://rmtt7.com/comics-cata/%E6%97%A5%E6%BC%AB/ob/time/st/all',
        'C3': 'https://rmtt7.com/comics-cata/3D%E6%BC%AB%E7%94%BB/ob/time/st/all',
        'C4': 'https://rmtt7.com/comics-cata/%E7%9C%9F%E4%BA%BA/ob/time/st/all',
        'C5': 'https://rmtt7.com/comics-cata/%E7%9F%AD%E7%AF%87/ob/time/st/all'
    }
}

USER_AGENTS = [
    # Windows Chrome
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.0.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",

    # Firefox
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.1) Gecko/20100101 Firefox/125.1",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.1) Gecko/20100101 Firefox/125.1",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:125.2) Gecko/20100101 Firefox/125.2",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:125.1) Gecko/20100101 Firefox/125.1",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",

    # Mac Safari
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.2.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.1 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.1.0 Safari/537.36",

    # Windows Edge
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.1.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.1.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.1.2.0 Safari/537.36",

    # Linux Chrome
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.1.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.2.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.0.1 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.1 Safari/537.36",

    # Mac Chrome
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.0.1 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
]


# ================== 辅助类定义 ==================
class UserAgentPool:
    def __init__(self, ua_list):
        self.ua_list = ua_list
        self.ua_usage = {ua: 0 for ua in ua_list}
        self.lock = threading.Lock()

    def get_ua(self):
        with self.lock:
            sorted_ua = sorted(self.ua_usage.items(), key=lambda x: x[1])
            selected = sorted_ua[0][0]
            self.ua_usage[selected] += 1
            return selected


class DownloadLogger:
    def __init__(self, base_dir):
        self.base_dir = base_dir
        self.log_dir = os.path.join(base_dir, '肉漫天堂日志')
        os.makedirs(self.log_dir, exist_ok=True)
        # 全量对比日志锁
        self.global_log_lock = threading.RLock()
        # 初始化全量对比日志
        self.global_log_path = os.path.join(self.log_dir, '肉漫天堂全量对比日志.txt')
        if not os.path.exists(self.global_log_path):
            with open(self.global_log_path, 'w', encoding='utf-8') as f:
                f.write("{}")

    def log_error(self, message):
        log_path = os.path.join(self.log_dir, 'error.log')
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {message}\n")

    def read_log_file(self, log_path):
        """尝试用不同编码读取日志文件，并处理新旧格式兼容性"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5']
        for encoding in encodings:
            try:
                with open(log_path, 'r', encoding=encoding) as f:
                    return json.load(f)
            except (UnicodeDecodeError, json.JSONDecodeError):
                continue
        return None

    def read_global_log(self):
        """读取全量对比日志"""
        with self.global_log_lock:
            try:
                with open(self.global_log_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}

    def update_global_log(self, comic_id, data):
        """更新全量对比日志"""
        with self.global_log_lock:
            global_log = self.read_global_log()
            global_log[comic_id] = data
            try:
                with open(self.global_log_path, 'w', encoding='utf-8') as f:
                    json.dump(global_log, f, ensure_ascii=False, indent=2)
            except Exception as e:
                self.log_error(f"更新全量对比日志失败: {str(e)}")
                print(f"{Fore.RED}更新全量对比日志失败: {str(e)}{Style.RESET_ALL}")


class NASClient:
    def __init__(self, config):
        self.conn = SMBConnection(
            config['username'],
            config['password'],
            config['client_name'],
            config['server_name'],
            use_ntlm_v2=True
        )
        self.conn.connect(config['server_ip'], 139)
        self.share_name = config['share_name']
        self.target_dir = config['target_dir']
        # NAS操作锁
        self.lock = threading.Lock()

    def create_directory(self, path):
        try:
            current_path = ''
            for part in path.strip('/').split('/'):
                current_path = f"{current_path}/{part}" if current_path else part
                if not self.directory_exists(current_path):
                    self.conn.createDirectory(self.share_name, current_path)
            return True
        except Exception as e:
            print(f"{Fore.RED}目录创建失败: {path} - {str(e)}{Style.RESET_ALL}")
            return False

    def directory_exists(self, path):
        try:
            current_path = ''
            for part in path.strip('/').split('/'):
                current_path = f"{current_path}/{part}" if current_path else part
                parent_dir = os.path.dirname(current_path) or '/'
                if not any(
                        f.filename == part and f.isDirectory for f in self.conn.listPath(self.share_name, parent_dir)):
                    return False
            return True
        except:
            return False

    def upload_file(self, local_path, remote_path):
        with self.lock:  # 加锁保证顺序上传
            remote_dir = os.path.dirname(remote_path)
            try:
                self.create_directory(remote_dir)
            except:
                pass

            try:
                with open(local_path, 'rb') as file_obj:
                    self.conn.storeFile(self.share_name, remote_path, file_obj)
                print(f"{Fore.GREEN}已上传: {remote_path}{Style.RESET_ALL}")
                return True
            except Exception as e:
                print(f"{Fore.RED}上传失败: {os.path.basename(local_path)} - {str(e)}{Style.RESET_ALL}")
                return False

    def download_file(self, remote_path, local_path):
        with self.lock:  # 加锁保证顺序下载
            try:
                with open(local_path, 'wb') as file_obj:
                    self.conn.retrieveFile(self.share_name, remote_path, file_obj)
                print(f"{Fore.GREEN}已下载: {os.path.basename(local_path)}{Style.RESET_ALL}")
                return True
            except Exception as e:
                print(f"{Fore.RED}下载失败: {os.path.basename(local_path)} - {str(e)}{Style.RESET_ALL}")
                return False


class DirectoryManager:
    @staticmethod
    def init_directories(base_dir):
        print("\n正在初始化本地目录结构...")
        dirs = [
            '肉漫天堂TEMP',
            '肉漫天堂ComicPDFs',
            '肉漫天堂合并PDF',
            '肉漫天堂封面',
            '肉漫天堂NAS中间件',  # 新增中间件目录
            os.path.join('肉漫天堂日志', '肉漫天堂ID日志'),
            os.path.join('肉漫天堂日志', '肉漫天堂对比日志')
        ]
        for d in dirs:
            full_path = os.path.join(base_dir, d)
            if not os.path.exists(full_path):
                os.makedirs(full_path, exist_ok=True)
                print(f"{Fore.GREEN}已创建目录: {full_path}{Style.RESET_ALL}")
            else:
                print(f"{Fore.CYAN}已确认目录: {full_path}{Style.RESET_ALL}")

    @staticmethod
    def clean_temp_files(base_dir, comic_info, clean_merged_pdf=False):
        """清理临时文件，提供清理合并PDF的选项"""
        try:
            # 清理TEMP文件夹
            temp_dir = os.path.join(
                base_dir,
                '肉漫天堂TEMP',
                f"{comic_info['title']}-{comic_info['id']}"
            )
            if os.path.exists(temp_dir):
                print(f"{Fore.CYAN}清理临时文件: {temp_dir}{Style.RESET_ALL}")
                shutil.rmtree(temp_dir)

            # 清理章节PDF文件
            if clean_merged_pdf:
                # 清理单章节PDF文件
                chapter_pdfs_dir = os.path.join(
                    base_dir,
                    '肉漫天堂ComicPDFs',
                    f"{comic_info['title']}-{comic_info['id']}"
                )
                if os.path.exists(chapter_pdfs_dir):
                    print(f"{Fore.CYAN}清理章节PDF文件: {chapter_pdfs_dir}{Style.RESET_ALL}")
                    shutil.rmtree(chapter_pdfs_dir)

                # 清理合并PDF文件
                category = comic_info['category']
                category_prefix = CONFIG['CATEGORY_PREFIX_MAP'].get(category, '')
                merged_pdf_path = os.path.join(
                    base_dir,
                    '肉漫天堂合并PDF',
                    f"{category_prefix}-{comic_info['title']}-(1-{comic_info['last_chapter']}话)-肉漫天堂-{comic_info['id']}.pdf"
                )
                if os.path.exists(merged_pdf_path):
                    print(f"{Fore.CYAN}清理合并PDF: {os.path.basename(merged_pdf_path)}{Style.RESET_ALL}")
                    os.remove(merged_pdf_path)

            # 清理中间件目录中的旧PDF
            nas_middleware_dir = os.path.join(base_dir, '肉漫天堂NAS中间件')
            for file in os.listdir(nas_middleware_dir):
                if file.startswith(f"{comic_info['id']}-"):
                    file_path = os.path.join(nas_middleware_dir, file)
                    os.remove(file_path)
                    print(f"{Fore.CYAN}清理中间件文件: {file}{Style.RESET_ALL}")

        except Exception as e:
            print(f"{Fore.YELLOW}清理临时文件失败: {str(e)}{Style.RESET_ALL}")


# ================== 核心下载类 ==================
class ComicDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.ua_pool = UserAgentPool(USER_AGENTS)
        self.logger = DownloadLogger(CONFIG['BASE_DIR'])
        self.nas = NASClient(CONFIG['NAS_CONFIG']) if CONFIG['NAS_CONFIG']['enabled'] else None
        self.running = True
        self.lock = threading.Lock()

    def main_flow(self):
        self.show_mode_help()
        DirectoryManager.init_directories(CONFIG['BASE_DIR'])
        self.test_nas_connection()

        try:
            mode = self.get_mode_selection()
            if mode.startswith('C'):
                self.handle_mode_c(mode)
            else:
                {
                    'A': self.handle_mode_a,
                    'B': self.handle_mode_b
                }[mode]()
        except KeyboardInterrupt:
            self.running = False
            print(f"\n{Fore.YELLOW}用户中断，正在保存日志...{Style.RESET_ALL}")

    def test_nas_connection(self):
        if self.nas:
            print("\n测试NAS连接...")
            try:
                self.nas.conn.listShares()
                print(f"{Fore.GREEN}NAS连接成功{Style.RESET_ALL}")
                self.init_nas_directories()
            except Exception as e:
                print(f"{Fore.RED}NAS连接失败: {str(e)}{Style.RESET_ALL}")
                self.nas = None

    def init_nas_directories(self):
        print("\n正在验证NAS目录结构...")
        base_path = CONFIG['NAS_CONFIG']['target_dir']
        retry_count = 0
        while retry_count < 3:
            if self.nas.directory_exists(base_path):
                print(f"{Fore.CYAN}基础目录已存在: {base_path}{Style.RESET_ALL}")
                break
            else:
                if self.nas.create_directory(base_path):
                    print(f"{Fore.GREEN}已创建基础目录: {base_path}{Style.RESET_ALL}")
                    break
                else:
                    retry_count += 1
                    print(f"{Fore.YELLOW}基础目录创建失败，正在重试({retry_count}/3)...{Style.RESET_ALL}")
                    time.sleep(2)
        else:
            print(f"{Fore.RED}基础目录创建失败，请检查NAS权限设置{Style.RESET_ALL}")
            return
        for code, folder in CONFIG['CATEGORY_MAP'].items():
            full_path = f"{base_path}/{folder}"
            if self.nas.directory_exists(full_path):
                print(f"{Fore.CYAN}分类目录已存在: {full_path}{Style.RESET_ALL}")
            else:
                if self.nas.create_directory(full_path):
                    print(f"{Fore.GREEN}已创建分类目录: {full_path}{Style.RESET_ALL}")
                else:
                    print(f"{Fore.YELLOW}分类目录创建失败: {full_path} (不影响程序运行){Style.RESET_ALL}")

    def show_mode_help(self):
        print("\n=== 模式说明 ===")
        print("A) 手动输入BOOKID下载")
        print("B) 更新页批量下载")
        print("C0) 分类页主页下载")
        print("C1) 分类页韩漫下载")
        print("C2) 分类页日漫下载")
        print("C3) 分类页3D漫画下载")
        print("C4) 分类页真人下载")
        print("C5) 分类页短篇下载\n")

    def get_mode_selection(self):
        while True:
            mode = input("请选择模式(A/B/C0-C5): ").upper()
            if mode in ('A', 'B') or (mode.startswith('C') and len(mode) == 2 and mode[1] in '012345'):
                return mode
            print(f"{Fore.RED}无效输入，请重新选择{Style.RESET_ALL}")

    def handle_mode_a(self):
        print(f"\n{Fore.CYAN}=== 模式A：手动输入BOOKID下载 ==={Style.RESET_ALL}")
        book_ids = [x.strip() for x in input("输入BOOKID(多个用逗号分隔): ").split(',')]
        # 新增：漫画检查进度条与对比显示
        valid_ids = []
        from tqdm import tqdm
        print(f"共输入 {len(book_ids)} 个漫画：")
        for book_id in tqdm(book_ids, desc="📋 漫画检查", unit="本", colour='yellow'):
            try:
                result = self._fetch_comic_detail_for_a(book_id)
                if not result:
                    continue
                comic_name = result['title']
                website_chapters = result['latest_chapter']
                print(f"[{book_id}] 发现 {website_chapters} 个章节")
                local_chapters = 0
                global_log = self.logger.read_global_log()
                if str(book_id) in global_log:
                    local_chapters = global_log[str(book_id)].get('local_chapters', 0)
                    comic_name = global_log[str(book_id)].get('comic_name', comic_name)
                print(f"[{book_id}] 对比日志信息: 本地章节={local_chapters}, 网站章节={website_chapters}")
                if local_chapters == 0:
                    print(f" - [需下载] {comic_name}+(1-{website_chapters}话)-肉漫天堂({book_id}) - 首次下载")
                    if website_chapters > 0:
                        valid_ids.append(result)
                elif website_chapters > local_chapters:
                    print(f" - [需更新] {comic_name}+({local_chapters+1}-{website_chapters}话)-肉漫天堂({book_id}) - 有新章节")
                    valid_ids.append(result)
                else:
                    print(f" - [已跳过] {comic_name}+(1-{website_chapters}话)-肉漫天堂({book_id}) - 无需更新")
                print(f"🆕 新增下载任务: ID {book_id}") if result in valid_ids else None
            except Exception as e:
                print(f"[{book_id}] 检查异常: {e}")
        comics_data = valid_ids
        with tqdm(total=len(comics_data), desc="📖 正在抓取每个漫画的详细信息...", ncols=80, colour='cyan') as pbar:
            with ThreadPoolExecutor(max_workers=CONFIG['THREADS']['max_workers']) as executor:
                futures = [executor.submit(self._fetch_comic_detail_for_a, comic['id']) for comic in comics_data]
                for future in futures:
                    result = future.result()
                    if result:
                        comics_data.append(result)
                    pbar.update(1)
        # 对比全量日志，筛选需要下载的漫画
        to_download = []
        global_log = self.logger.read_global_log()

        for comic in comics_data:
            comic_id = comic['id']
            comic_global_data = global_log.get(comic_id, {})
            website_chapters = comic['latest_chapter']
            local_chapters = comic_global_data.get('local_chapters', 0)

            log_name = comic['log_name'].replace('_', '-')  # 替换下划线为短横线

            print(f"{Fore.CYAN}检查全量对比日志: {log_name}{Style.RESET_ALL}")
            if comic_id in global_log:
                if website_chapters == local_chapters:
                    print(f"{Fore.YELLOW}{log_name}   无更新已跳过 ({local_chapters}话){Style.RESET_ALL}")
                elif website_chapters > local_chapters:
                    print(
                        f"{Fore.GREEN}{log_name}   有更新需全量下载 ({local_chapters} → {website_chapters}话){Style.RESET_ALL}")
                    to_download.append(comic)
                else:
                    print(
                        f"{Fore.GREEN}{log_name}   章节异常需全量下载 ({local_chapters} → {website_chapters}话){Style.RESET_ALL}")
                    to_download.append(comic)
            else:
                print(f"{Fore.GREEN}{log_name}   首次下载{Style.RESET_ALL}")
                to_download.append(comic)

        # 多线程处理每个漫画
        with ThreadPoolExecutor(max_workers=CONFIG['THREADS']['comic_workers']) as executor:
            futures = [executor.submit(self.process_comic, comic) for comic in to_download]
            with tqdm(total=len(to_download), desc="漫画下载进度", ncols=80) as pbar:
                for future in futures:
                    future.result()
                    pbar.update(1)

    def handle_mode_b(self):
        print(f"\n{Fore.CYAN}=== 模式B：更新页批量下载 ==={Style.RESET_ALL}")
        print("正在抓取更新页...")
        comics_data = self._fetch_update_page_data()
        if not comics_data:
            print(f"{Fore.RED}没有找到可下载的漫画{Style.RESET_ALL}")
            return
        valid_ids = []
        from tqdm import tqdm
        print(f"共获取到 {len(comics_data)} 个漫画：")
        for comic in tqdm(comics_data, desc="📋 漫画检查", unit="本", colour='yellow'):
            try:
                book_id = comic['id']
                comic_name = comic['title']
                result = self._fetch_comic_detail_for_b(comic)
                if not result:
                    continue
                website_chapters = result['latest_chapter']
                print(f"[{book_id}] 发现 {website_chapters} 个章节")
                local_chapters = 0
                global_log = self.logger.read_global_log()
                if str(book_id) in global_log:
                    local_chapters = global_log[str(book_id)].get('local_chapters', 0)
                    comic_name = global_log[str(book_id)].get('comic_name', comic_name)
                print(f"[{book_id}] 对比日志信息: 本地章节={local_chapters}, 网站章节={website_chapters}")
                if local_chapters == 0:
                    print(f" - [需下载] {comic_name}+(1-{website_chapters}话)-肉漫天堂({book_id}) - 首次下载")
                    if website_chapters > 0:
                        valid_ids.append(result)
                elif website_chapters > local_chapters:
                    print(f" - [需更新] {comic_name}+({local_chapters+1}-{website_chapters}话)-肉漫天堂({book_id}) - 有新章节")
                    valid_ids.append(result)
                else:
                    print(f" - [已跳过] {comic_name}+(1-{website_chapters}话)-肉漫天堂({book_id}) - 无需更新")
                print(f"🆕 新增下载任务: ID {book_id}") if result in valid_ids else None
            except Exception as e:
                print(f"[{comic.get('id', '?')}] 检查异常: {e}")
        comics_data = valid_ids
        results = []
        with tqdm(total=len(comics_data), desc="📖 正在抓取每个漫画的详细信息...", ncols=80, colour='cyan') as pbar:
            with ThreadPoolExecutor(max_workers=CONFIG['THREADS']['max_workers']) as executor:
                futures = [executor.submit(self._fetch_comic_detail_for_b, comic) for comic in comics_data]
                for future in futures:
                    result = future.result()
                    if result:
                        results.append(result)
                    pbar.update(1)
        # 对比全量日志，筛选需要下载的漫画
        to_download = []
        global_log = self.logger.read_global_log()

        for comic in results:
            comic_id = comic['id']
            comic_global_data = global_log.get(comic_id, {})
            website_chapters = comic['latest_chapter']
            local_chapters = comic_global_data.get('local_chapters', 0)

            log_name = comic['log_name'].replace('_', '-')  # 替换下划线为短横线

            print(f"{Fore.CYAN}检查全量对比日志: {log_name}{Style.RESET_ALL}")
            if comic_id in global_log:
                if website_chapters == local_chapters:
                    print(f"{Fore.YELLOW}{log_name}   无更新已跳过 ({local_chapters}话){Style.RESET_ALL}")
                elif website_chapters > local_chapters:
                    print(
                        f"{Fore.GREEN}{log_name}   有更新需全量下载 ({local_chapters} → {website_chapters}话){Style.RESET_ALL}")
                    to_download.append(comic)
                else:
                    print(
                        f"{Fore.GREEN}{log_name}   章节异常需全量下载 ({local_chapters} → {website_chapters}话){Style.RESET_ALL}")
                    to_download.append(comic)
            else:
                print(f"{Fore.GREEN}{log_name}   首次下载{Style.RESET_ALL}")
                to_download.append(comic)

        # 多线程处理每个漫画
        with ThreadPoolExecutor(max_workers=CONFIG['THREADS']['comic_workers']) as executor:
            futures = [executor.submit(self.process_comic, comic) for comic in to_download]
            with tqdm(total=len(to_download), desc="漫画下载进度", ncols=80) as pbar:
                for future in futures:
                    future.result()
                    pbar.update(1)

    # ================== 模式C分类页批量下载 ==================
    def handle_mode_c(self, mode):
        print(f"\n{Fore.CYAN}=== 模式{mode}：分类页批量下载 ==={Style.RESET_ALL}")
        base_url = CONFIG['C_MODE_URLS'][mode]
        comics_data = []

        # 输入起始页和结束页
        while True:
            try:
                start_page = int(input("请输入起始页数字（>=1）：").strip())
                if start_page < 1:
                    print(f"{Fore.RED}起始页必须>=1{Style.RESET_ALL}")
                    continue
                break
            except:
                print(f"{Fore.RED}请输入有效数字{Style.RESET_ALL}")
        while True:
            try:
                end_page = int(input("请输入结束页数字（0表示抓取到最后一页）：").strip())
                if end_page < 0:
                    print(f"{Fore.RED}结束页不能为负数{Style.RESET_ALL}")
                    continue
                break
            except:
                print(f"{Fore.RED}请输入有效数字{Style.RESET_ALL}")

        # 1. 获取总页数
        first_page = self.make_request(base_url)
        soup = BeautifulSoup(first_page.text, 'html.parser')
        # 获取总页数
        page_ul = soup.select_one('ul.page')
        max_page = 1
        if page_ul:
            page_links = page_ul.find_all('a')
            for a in page_links:
                if a.text.isdigit():
                    max_page = max(max_page, int(a.text))
                elif a.get('title') == '尾页':
                    import re
                    m = re.search(r'/page/(\d+)', a['href'])
                    if m:
                        max_page = int(m.group(1))
        print(f"{Fore.CYAN}共{max_page}页{Style.RESET_ALL}")

        # 计算实际抓取范围
        if end_page == 0:
            real_end = max_page
        else:
            real_end = min(end_page, max_page)
        real_start = min(start_page, real_end)

        # 处理特殊情况并打印提示
        if start_page == end_page and end_page != 0:
            real_start = real_end = start_page
            print(f"{Fore.CYAN}共{max_page}页  抓取当前页-页码{real_start}{Style.RESET_ALL}")
        elif end_page == 0:
            print(
                f"{Fore.CYAN}共{max_page}页  抓取第{real_start}页至第{real_end}页  共计{real_end - real_start + 1}页{Style.RESET_ALL}")
        else:
            print(
                f"{Fore.CYAN}共{max_page}页  抓取第{real_start}页至第{real_end}页  共计{real_end - real_start + 1}页{Style.RESET_ALL}")

        # 2. 分页抓取所有ID
        all_items = []
        with tqdm(total=real_end - real_start + 1, desc="正在抓取所有ID...", ncols=80) as pbar:
            for page in range(real_start, real_end + 1):
                if page == 1:
                    page_url = base_url
                else:
                    if base_url.endswith('/'):
                        page_url = base_url + f'page/{page}'
                    else:
                        page_url = base_url + f'/page/{page}'
                try:
                    resp = self.make_request(page_url)
                    soup = BeautifulSoup(resp.text, 'html.parser')
                    items = soup.select('li.vodlist_item')
                    for item in items:
                        link = item.select_one('a[href*="/comics-detail/"]')
                        href = link.get('href', '')
                        book_id = self.parse_book_id(href)
                        if not book_id:
                            continue
                        title = link.get('title', '').strip()
                        if not title:
                            title = item.select_one('center a[title]').get('title', '').strip()
                        all_items.append({'id': book_id, 'title': title})
                except Exception as e:
                    self.logger.log_error(f"C模式分页解析失败: {page_url} - {str(e)}")
                    print(f"{Fore.RED}解析页面失败: {page_url} - {str(e)}{Style.RESET_ALL}")
                pbar.update(1)

        if not all_items:
            print(f"{Fore.RED}没有找到可下载的漫画{Style.RESET_ALL}")
            return

        # 3. 进度条抓取每个漫画详细信息
        results = []
        with tqdm(total=len(all_items), desc="正在抓取每个漫画的详细信息...", ncols=80) as pbar:
            with ThreadPoolExecutor(max_workers=CONFIG['THREADS']['max_workers']) as executor:
                futures = [executor.submit(self._fetch_comic_detail_for_b, comic) for comic in all_items]
                for future in futures:
                    result = future.result()
                    if result:
                        results.append(result)
                    pbar.update(1)

        # 4. 对比全量日志，筛选需要下载的漫画
        to_download = []
        global_log = self.logger.read_global_log()

        for comic in results:
            comic_id = comic['id']
            comic_global_data = global_log.get(comic_id, {})
            website_chapters = comic['latest_chapter']
            local_chapters = comic_global_data.get('local_chapters', 0)

            log_name = comic['log_name'].replace('_', '-')  # 替换下划线为短横线

            print(f"{Fore.CYAN}检查全量对比日志: {log_name}{Style.RESET_ALL}")
            if comic_id in global_log:
                if website_chapters == local_chapters:
                    print(f"{Fore.YELLOW}{log_name}   无更新已跳过 ({local_chapters}话){Style.RESET_ALL}")
                elif website_chapters > local_chapters:
                    print(
                        f"{Fore.GREEN}{log_name}   有更新需全量下载 ({local_chapters} → {website_chapters}话){Style.RESET_ALL}")
                    to_download.append(comic)
                else:
                    print(
                        f"{Fore.GREEN}{log_name}   章节异常需全量下载 ({local_chapters} → {website_chapters}话){Style.RESET_ALL}")
                    to_download.append(comic)
            else:
                print(f"{Fore.GREEN}{log_name}   首次下载{Style.RESET_ALL}")
                to_download.append(comic)

        # 多线程处理每个漫画
        with ThreadPoolExecutor(max_workers=CONFIG['THREADS']['comic_workers']) as executor:
            futures = [executor.submit(self.process_comic, comic) for comic in to_download]
            with tqdm(total=len(to_download), desc="漫画下载进度", ncols=80) as pbar:
                for future in futures:
                    future.result()
                    pbar.update(1)

    # ================== 其余代码未动 ==================
    def _fetch_comic_detail_for_a(self, comic_id):
        try:
            url = f"https://rmtt7.com/comics-detail/{comic_id}"
            response = self.make_request(url)
            if not response:
                return None
            soup = BeautifulSoup(response.text, 'html.parser')
            title_tag = soup.find('h1', class_='title')
            title = title_tag.text.strip() if title_tag else f"漫画{comic_id}"
            # 清理标题中的特殊字符
            title = sanitize_filename(title)
            category_tag = soup.select_one('a[href^="/comics-cata/"] span')
            category = category_tag.text.strip() if category_tag else "未知分类"
            cover_url = None
            cover_a = soup.select_one('.vodlist_thumb')
            if cover_a and cover_a.has_attr('data-original'):
                cover_url = cover_a['data-original']
            elif cover_a and cover_a.has_attr('style'):
                import re
                m = re.search(r'url\([\'"]?(.+?)[\'"]?\)', cover_a['style'])
                if m:
                    cover_url = m.group(1)
            chapter_list = []
            chapter_ul = soup.select('div.playlist_full ul.content_playlist li')
            for li in chapter_ul:
                a = li.find('a')
                if a:
                    chapter_url = urljoin("https://rmtt7.com", a['href'])
                    # 清理章节标题中的特殊字符
                    chapter_title = sanitize_filename(a.text.strip())
                    chapter_list.append({
                        'title': chapter_title,
                        'url': chapter_url
                    })
            first_chap = '1' if chapter_list else '0'
            last_chap = str(len(chapter_list)) if chapter_list else '0'
            log_name = f"{title}+({first_chap}-{last_chap}话)-肉漫天堂({comic_id})-{category}"
            return {
                'id': comic_id,
                'title': title.replace('_', '-'),  # 替换下划线为短横线
                'category': category,
                'chapters': chapter_list,
                'latest_chapter': len(chapter_list),
                'first_chapter': first_chap,
                'last_chapter': last_chap,
                'log_name': log_name.replace('_', '-'),  # 替换下划线为短横线
                'cover_url': cover_url
            }
        except Exception as e:
            self.logger.log_error(f"A模式详情页解析失败: {comic_id} - {str(e)}")
            print(f"{Fore.RED}A模式详情页解析失败: {comic_id} - {str(e)}{Style.RESET_ALL}")
            return None

    def _fetch_update_page_data(self):
        try:
            url = "https://rmtt7.com/update-manga"
            response = self.make_request(url)
            if not response:
                return []
            soup = BeautifulSoup(response.text, 'html.parser')
            comics_data = []
            for item in soup.select('li.vodlist_item'):
                try:
                    link = item.select_one('a[href*="/comics-detail/"], a[href*="/comic/"]')
                    href = link.get('href', '')
                    book_id = self.parse_book_id(href)
                    if not book_id:
                        continue
                    title = item.select_one('.vodlist_title').text.strip()
                    # 清理标题中的特殊字符
                    title = sanitize_filename(title)
                    comics_data.append({
                        'id': book_id,
                        'title': title.replace('_', '-')  # 替换下划线为短横线
                    })
                except Exception as e:
                    self.logger.log_error(f"解析失败: {str(e)}")
                    print(f"{Fore.RED}更新页条目解析失败: {str(e)}{Style.RESET_ALL}")
                    continue
            return comics_data
        except Exception as e:
            self.logger.log_error(f"更新页解析失败: {str(e)}")
            print(f"{Fore.RED}更新页解析失败: {str(e)}{Style.RESET_ALL}")
            return []

    def _fetch_comic_detail_for_b(self, comic):
        try:
            comic_id = comic['id']
            url = f"https://rmtt7.com/comics-detail/{comic_id}"
            response = self.make_request(url)
            if not response:
                return None
            soup = BeautifulSoup(response.text, 'html.parser')
            title_tag = soup.find('h1', class_='title')
            title = title_tag.text.strip() if title_tag else comic.get('title', f"漫画{comic_id}")
            # 清理标题中的特殊字符
            title = sanitize_filename(title)
            category_tag = soup.select_one('a[href^="/comics-cata/"] span')
            category = category_tag.text.strip() if category_tag else "未知分类"
            cover_url = None
            cover_a = soup.select_one('.vodlist_thumb')
            if cover_a and cover_a.has_attr('data-original'):
                cover_url = cover_a['data-original']
            elif cover_a and cover_a.has_attr('style'):
                import re
                m = re.search(r'url\([\'"]?(.+?)[\'"]?\)', cover_a['style'])
                if m:
                    cover_url = m.group(1)
            chapter_list = []
            chapter_ul = soup.select('div.playlist_full ul.content_playlist li')
            for li in chapter_ul:
                a = li.find('a')
                if a:
                    chapter_url = urljoin("https://rmtt7.com", a['href'])
                    # 清理章节标题中的特殊字符
                    chapter_title = sanitize_filename(a.text.strip())
                    chapter_list.append({
                        'title': chapter_title.replace('_', '-'),  # 替换下划线为短横线
                        'url': chapter_url
                    })
            first_chap = '1' if chapter_list else '0'
            last_chap = str(len(chapter_list)) if chapter_list else '0'
            log_name = f"{title}+({first_chap}-{last_chap}话)-肉漫天堂({comic_id})-{category}"
            return {
                'id': comic_id,
                'title': title.replace('_', '-'),  # 替换下划线为短横线
                'category': category,
                'chapters': chapter_list,
                'latest_chapter': len(chapter_list),
                'first_chapter': first_chap,
                'last_chapter': last_chap,
                'log_name': log_name.replace('_', '-'),  # 替换下划线为短横线
                'cover_url': cover_url
            }
        except Exception as e:
            self.logger.log_error(f"B模式详情页解析失败: {comic['id']} - {str(e)}")
            print(f"{Fore.RED}B模式详情页解析失败: {comic['id']} - {str(e)}{Style.RESET_ALL}")
            return None

    def parse_book_id(self, href):
        if '/comics-detail/' in href:
            return href.split('/')[-1].split('?')[0]
        elif '/comic/' in href:
            return href.split('/')[-1]
        return None

    def process_comic(self, comic_data, pbar=None):
        if not self.running:
            if pbar: pbar.update(1)
            return
        comic_id = comic_data['id']
        log_name = comic_data['log_name']
        category = comic_data['category']
        cover_url = comic_data.get('cover_url')
        try:
            print(f"\n[开始下载] {log_name}")
            full_info = self.fetch_comic_info(comic_id, cover_url)
            if not full_info:
                print(f"{Fore.RED}[警告] 无法获取完整信息 ID: {comic_id}{Style.RESET_ALL}")
                if pbar: pbar.update(1)
                return

            # 检查全量对比日志
            global_log = self.logger.read_global_log()
            comic_global_data = global_log.get(comic_id, {})
            website_chapters = full_info['latest_chapter']
            local_chapters = comic_global_data.get('local_chapters', 0)

            # 拆分上传至NAS及PDF合并逻辑，首次下载和增量更新分支
            if local_chapters == 0:
                # ===== 首次下载分支 =====
                print(f"{Fore.CYAN}[首次下载] 进入首次下载分支{Style.RESET_ALL}")
                self.download_comic(full_info)
                merged_pdf_path = self.generate_merged_pdf_first(full_info)
                # 打印PDF信息
                self.print_pdf_info(merged_pdf_path, full_info['id'], '首次下载')
                # 上传到NAS
                upload_success = self.upload_to_nas_first(full_info, category, merged_pdf_path)
                # 更新日志
                self.update_comic_log(full_info, log_name, category, website_chapters, local_chapters, merged_pdf_path)
                # 清理临时文件
                if upload_success:
                    print(f"{Fore.GREEN}上传成功，清理临时文件{Style.RESET_ALL}")
                    DirectoryManager.clean_temp_files(CONFIG['BASE_DIR'], full_info, clean_merged_pdf=True)
                else:
                    print(f"{Fore.YELLOW}上传失败，保留临时文件以便后续重试{Style.RESET_ALL}")
            elif website_chapters > local_chapters:
                # ===== 增量更新分支 =====
                print(f"{Fore.CYAN}[增量更新] 进入增量更新分支{Style.RESET_ALL}")
                # 只下载新增章节
                full_info['chapters'] = full_info['chapters'][local_chapters:]
                full_info['first_chapter'] = str(int(full_info['first_chapter']) + local_chapters)
                self.download_comic(full_info)
                merged_pdf_path, old_pdf_path = self.generate_merged_pdf_increment(full_info, local_chapters)
                # 打印PDF信息
                self.print_pdf_info(old_pdf_path, full_info['id'], '旧PDF')
                self.print_pdf_info(merged_pdf_path, full_info['id'], '增量合并')
                # 上传到NAS
                upload_success = self.upload_to_nas_increment(full_info, category, merged_pdf_path, old_pdf_path)
                # 更新日志
                self.update_comic_log(full_info, log_name, category, website_chapters, local_chapters, merged_pdf_path)
                # 清理临时文件
                if upload_success:
                    print(f"{Fore.GREEN}上传成功，清理临时文件{Style.RESET_ALL}")
                    DirectoryManager.clean_temp_files(CONFIG['BASE_DIR'], full_info, clean_merged_pdf=True)
                else:
                    print(f"{Fore.YELLOW}上传失败，保留临时文件以便后续重试{Style.RESET_ALL}")
            else:
                print(f"{Fore.YELLOW}无需更新，已跳过{Style.RESET_ALL}")
            print(f"{Fore.GREEN}[完成] {log_name} 处理完成{Style.RESET_ALL}")
        except Exception as e:
            self.logger.log_error(f"处理失败: {log_name} - {str(e)}")
            print(f"{Fore.RED}[错误] 处理失败: {str(e)}{Style.RESET_ALL}")
        if pbar: pbar.update(1)

    def fetch_comic_info(self, comic_id, cover_url=None):
        try:
            url = f"https://rmtt7.com/comics-detail/{comic_id}"
            response = self.make_request(url)
            if not response:
                return None
            soup = BeautifulSoup(response.text, 'html.parser')
            title_tag = soup.find('h1', class_='title')
            title = title_tag.text.strip() if title_tag else f"漫画{comic_id}"
            # 清理标题中的特殊字符
            title = sanitize_filename(title)
            category_tag = soup.select_one('a[href^="/comics-cata/"] span')
            category = category_tag.text.strip() if category_tag else "未知分类"
            if not cover_url:
                cover_a = soup.select_one('.vodlist_thumb')
                if cover_a and cover_a.has_attr('data-original'):
                    cover_url = cover_a['data-original']
                elif cover_a and cover_a.has_attr('style'):
                    import re
                    m = re.search(r'url\([\'"]?(.+?)[\'"]?\)', cover_a['style'])
                    if m:
                        cover_url = m.group(1)

            # 检查全量日志判断是否需要下载封面
            global_log = self.logger.read_global_log()
            need_cover = comic_id not in global_log

            cover_path = self.download_cover(comic_id, title, cover_url) if cover_url and need_cover else None

            chapters = []
            chapter_ul = soup.select('div.playlist_full ul.content_playlist li')
            for li in chapter_ul:
                a = li.find('a')
                if a:
                    chapter_url = urljoin("https://rmtt7.com", a['href'])
                    chapter_id = a['href'].split('/')[-1]
                    # 清理章节标题中的特殊字符
                    chapter_title = sanitize_filename(a.text.strip())
                    chapters.append({
                        'id': chapter_id,
                        'number': str(len(chapters) + 1),
                        'title': chapter_title.replace('_', '-'),  # 替换下划线为短横线
                        'url': chapter_url
                    })
            first_chap = '1' if chapters else '0'
            last_chap = str(len(chapters)) if chapters else '0'
            return {
                'id': comic_id,
                'title': title.replace('_', '-'),  # 替换下划线为短横线
                'category': category,
                'chapters': chapters,
                'latest_chapter': len(chapters),
                'first_chapter': first_chap,
                'last_chapter': last_chap,
                'cover_path': cover_path
            }
        except Exception as e:
            self.logger.log_error(f"获取信息失败 ID:{comic_id} - {str(e)}")
            print(f"{Fore.RED}获取信息失败 ID:{comic_id} - {str(e)}{Style.RESET_ALL}")
            return None

    def parse_chapter_images(self, chapter_url):
        try:
            response = self.make_request(chapter_url)
            if not response:
                return []
            soup = BeautifulSoup(response.text, 'html.parser')
            imgs = soup.select('div.container img.lazyload')
            img_urls = []
            for img in imgs:
                if img.has_attr('data-original'):
                    img_urls.append(img['data-original'])
                elif img.has_attr('src') and img['src'].startswith('http'):
                    img_urls.append(img['src'])
            return img_urls
        except Exception as e:
            self.logger.log_error(f"章节图片解析失败: {chapter_url} - {str(e)}")
            print(f"{Fore.RED}章节图片解析失败: {chapter_url} - {str(e)}{Style.RESET_ALL}")
            return []

    def download_cover(self, comic_id, title, url):
        save_dir = os.path.join(CONFIG['BASE_DIR'], '肉漫天堂封面')
        os.makedirs(save_dir, exist_ok=True)
        # 确保title已经经过sanitize_filename处理
        save_path = os.path.join(save_dir, f"{title}-{comic_id}-封面.jpg")  # 替换下划线为短横线
        if os.path.exists(save_path):
            print(f"{Fore.YELLOW}封面已存在: {os.path.basename(save_path)}{Style.RESET_ALL}")
            return save_path
        try:
            print(f"{Fore.GREEN}下载封面: {os.path.basename(save_path)}{Style.RESET_ALL}")
            response = self.make_request(url)
            img = Image.open(io.BytesIO(response.content))
            if img.format == 'WEBP':
                rgb_img = img.convert('RGB')
                rgb_img.save(save_path, 'JPEG', quality=95)
            else:
                img.save(save_path)

            # 立即生成封面PDF
            self.generate_cover_pdf(save_path, title, comic_id)

            return save_path
        except Exception as e:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            self.logger.log_error(f"封面保存失败: {url} - {str(e)}")
            print(f"{Fore.RED}封面格式处理失败，已保存原始数据: {url} - {str(e)}{Style.RESET_ALL}")

            # 尝试生成封面PDF
            self.generate_cover_pdf(save_path, title, comic_id)

            return save_path

    def generate_cover_pdf(self, cover_path, title, comic_id):
        """生成封面PDF文件"""
        try:
            # 确保title已经经过sanitize_filename处理
            output_dir = os.path.join(
                CONFIG['BASE_DIR'],
                '肉漫天堂ComicPDFs',
                f"{title}-{comic_id}"  # 替换下划线为短横线
            )
            os.makedirs(output_dir, exist_ok=True)
            cover_pdf = os.path.join(output_dir, f"{title}-{comic_id}-封面.pdf")  # 替换下划线为短横线

            if os.path.exists(cover_pdf):
                print(f"{Fore.YELLOW}封面PDF已存在: {os.path.basename(cover_pdf)}{Style.RESET_ALL}")
                return

            print(f"{Fore.GREEN}生成封面PDF: {os.path.basename(cover_pdf)}{Style.RESET_ALL}")
            with open(cover_pdf, 'wb') as f:
                f.write(img2pdf.convert([cover_path]))
        except Exception as e:
            self.logger.log_error(f"封面PDF生成失败: {str(e)}")
            print(f"{Fore.RED}封面PDF生成失败: {str(e)}{Style.RESET_ALL}")

    def download_comic(self, comic_info):
        print(f"\n{Fore.GREEN}[开始下载漫画] {comic_info['title']}{Style.RESET_ALL}")
        # 使用线程池同时下载多个章节
        with ThreadPoolExecutor(max_workers=CONFIG['THREADS']['chapter_workers']) as executor:
            futures = [executor.submit(self.download_chapter, comic_info, chapter) for chapter in
                       comic_info['chapters']]
            with tqdm(total=len(comic_info['chapters']), desc=f"{comic_info['title']} 章节下载", ncols=80) as pbar:
                for future in futures:
                    future.result()
                    pbar.update(1)

    def download_chapter(self, comic_info, chapter):
        print(f"{Fore.CYAN}下载章节: {chapter['title']}{Style.RESET_ALL}")
        try:
            base_path = os.path.join(
                CONFIG['BASE_DIR'],
                '肉漫天堂TEMP',
                f"{comic_info['title']}-{comic_info['id']}",
                f"{chapter['title']}-{chapter['id']}"
            )
            os.makedirs(base_path, exist_ok=True)
            images = self.parse_chapter_images(chapter['url'])

            existing_images = [f for f in os.listdir(base_path) if f.endswith('.jpg')]
            if len(existing_images) == len(images):
                print(f"{Fore.YELLOW}章节 {chapter['title']} 已下载完成，跳过{Style.RESET_ALL}")
                self.generate_chapter_pdf(comic_info, chapter, base_path)
                return
            elif len(existing_images) > 0:
                print(f"{Fore.YELLOW}章节 {chapter['title']} 已下载 {len(existing_images)}/{len(images)} 张图片，继续下载剩余图片{Style.RESET_ALL}")

            skipped_images = set()
            failed_images = set()
            details = []
            with ThreadPoolExecutor(max_workers=CONFIG['THREADS']['image_workers']) as executor:
                futures = []
                for idx, img_url in enumerate(images, 1):
                    img_name = f"{idx:03d}.jpg"
                    img_path = os.path.join(base_path, img_name)
                    if os.path.exists(img_path) and os.path.getsize(img_path) > 0:
                        continue
                    futures.append((idx, img_url, img_path))
                future_map = {executor.submit(self.download_image, img_url, img_path, idx, chapter['title']): (idx, img_url) for idx, img_url, img_path in futures}
                from tqdm import tqdm
                with tqdm(total=len(futures), desc=chapter['title'], leave=False, ncols=80) as pbar:
                    for future in future_map:
                        idx, img_url = future_map[future]
                        result, reason = future.result()
                        if result == 'skip':
                            skipped_images.add(idx)
                            details.append({'idx': idx, 'url': img_url, 'reason': reason})
                            print(f"[图片跳过]{reason} 章节 {chapter['title']} 第{idx}张图片 URL: {img_url}")
                        elif result == 'fail':
                            failed_images.add(idx)
                            details.append({'idx': idx, 'url': img_url, 'reason': reason})
                            print(f"[图片失败]{reason} 章节 {chapter['title']} 第{idx}张图片 URL: {img_url}")
                        pbar.update(1)
            total_images = len(images)
            failed_count = len(failed_images)
            if total_images > 0 and failed_count / total_images > 0.05:
                print(f"{Fore.RED}放弃图片数占比超过5%，放弃章节: {chapter['title']}{Style.RESET_ALL}")
                for detail in details:
                    print(f"[放弃图片] 章节{chapter['title']} 图片{detail['idx']} URL: {detail['url']} 原因: {detail['reason']}")
                return
            self.generate_chapter_pdf(comic_info, chapter, base_path)
        except Exception as e:
            self.logger.log_error(f"章节下载失败: {chapter['title']} - {str(e)}")
            print(f"{Fore.RED}章节下载失败: {chapter['title']} - {str(e)}{Style.RESET_ALL}")

    def download_image(self, img_url, img_path, idx, chapter_title):
        """下载单张图片，返回('ok', None)表示成功，('skip', 原因)表示跳过，('fail', 原因)表示失败"""
        min_file_size = 1024
        min_width = 200
        min_height = 300
        max_retries = 5
        retry_count = 0
        while retry_count < max_retries:
            try:
                response = self.make_request(img_url)
                img_bytes = response.content
                if self.is_blank_image(img_bytes):
                    reason = '空白图'
                    print(f"[图片跳过][空白] 章节 {chapter_title} 第{idx}张图片 链接: {img_url}")
                    return 'skip', reason
                with open(img_path, 'wb') as f:
                    f.write(img_bytes)
                # 体积校验
                if os.path.getsize(img_path) < min_file_size:
                    reason = '体积过小'
                    print(f"[图片跳过][体积过小] 章节 {chapter_title} 第{idx}张图片 链接: {img_url}")
                    self.safe_remove(img_path)
                    return 'skip', reason
                # 尺寸校验
                try:
                    with Image.open(img_path) as img:
                        if img.width < min_width or img.height < min_height:
                            reason = '尺寸过小'
                            print(f"[图片跳过][尺寸过小] 章节 {chapter_title} 第{idx}张图片 链接: {img_url}")
                            self.safe_remove(img_path)
                            return 'skip', reason
                except Exception:
                    reason = '无法识别'
                    print(f"[图片跳过][无法识别] 章节 {chapter_title} 第{idx}张图片 链接: {img_url}")
                    self.safe_remove(img_path)
                    return 'skip', reason
                # 重复校验（可选，略）
                return 'ok', None
            except Exception as e:
                retry_count += 1
                print(f"[图片重试] 章节 {chapter_title} 第{idx}张图片 第{retry_count}次: {e}")
                time.sleep(random.uniform(5, 25))
        reason = '下载失败'
        print(f"[图片放弃] 章节 {chapter_title} 第{idx}张图片 链接: {img_url}")
        self.safe_remove(img_path)
        return 'fail', reason

    def generate_chapter_pdf(self, comic_info, chapter, chapter_dir):
        """生成单个章节的PDF"""
        # 确保title和chapter['title']已经经过sanitize_filename处理
        output_dir = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂ComicPDFs',
            f"{comic_info['title']}-{comic_info['id']}"  # 替换下划线为短横线
        )
        os.makedirs(output_dir, exist_ok=True)
        output_pdf = os.path.join(output_dir, f"{chapter['title']}.pdf")

        if os.path.exists(output_pdf):
            print(f"{Fore.YELLOW}章节PDF已存在: {chapter['title']}.pdf{Style.RESET_ALL}")
            return

        try:
            images = sorted([
                os.path.join(chapter_dir, img)
                for img in os.listdir(chapter_dir)
                if img.endswith('.jpg')
            ])

            if not images:
                print(f"{Fore.RED}章节 {chapter['title']} 没有图片，无法生成PDF{Style.RESET_ALL}")
                return

            print(f"{Fore.GREEN}生成章节PDF: {chapter['title']}.pdf{Style.RESET_ALL}")
            with open(output_pdf, 'wb') as f:
                f.write(img2pdf.convert(images))
        except Exception as e:
            self.logger.log_error(f"PDF生成失败: {chapter['title']} - {str(e)}")
            print(f"{Fore.RED}PDF生成失败: {chapter['title']} - {str(e)}{Style.RESET_ALL}")

    def generate_pdfs(self, comic_info):
        print(f"\n{Fore.GREEN}生成PDF文件: {comic_info['title']}{Style.RESET_ALL}")
        # 确保title已经经过sanitize_filename处理
        temp_dir = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂TEMP',
            f"{comic_info['title']}-{comic_info['id']}"  # 替换下划线为短横线
        )
        output_dir = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂ComicPDFs',
            f"{comic_info['title']}-{comic_info['id']}"  # 替换下划线为短横线
        )
        os.makedirs(output_dir, exist_ok=True)

        # 使用线程池并行生成PDF
        with ThreadPoolExecutor(max_workers=CONFIG['THREADS']['chapter_workers']) as executor:
            futures = []
            for chapter in comic_info['chapters']:
                # 确保chapter['title']已经经过sanitize_filename处理
                chapter_dir = os.path.join(temp_dir, f"{chapter['title']}-{chapter['id']}")  # 替换下划线为短横线
                output_pdf = os.path.join(output_dir, f"{chapter['title']}.pdf")
                if not os.path.exists(output_pdf):
                    futures.append(executor.submit(self.generate_chapter_pdf, comic_info, chapter, chapter_dir))

            with tqdm(total=len(futures), desc=f"生成章节PDF", ncols=80) as pbar:
                for future in futures:
                    future.result()
                    pbar.update(1)

        # 确保封面PDF也已生成
        cover_pdf = os.path.join(output_dir, f"{comic_info['title']}-{comic_info['id']}-封面.pdf")  # 替换下划线为短横线
        if not os.path.exists(cover_pdf) and comic_info['cover_path']:
            self.generate_cover_pdf(comic_info['cover_path'], comic_info['title'], comic_info['id'])

    def generate_merged_pdf(self, comic_info, incremental=False):
        """生成合并所有章节的完整PDF（支持增量合并）"""
        print(f"{Fore.GREEN}生成合并PDF: {comic_info['title']}{Style.RESET_ALL}")
        category = comic_info['category']
        category_prefix = CONFIG['CATEGORY_PREFIX_MAP'].get(category, '')

        # 确保title已经经过sanitize_filename处理
        chapters_dir = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂ComicPDFs',
            f"{comic_info['title']}-{comic_info['id']}"  # 替换下划线为短横线
        )

        # 生成合并PDF的文件名，确保所有部分都已清理
        # 修改：合并PDF名称应该包含1到最后一章的范围，而不是只包含新增章节
        merged_pdf_name = f"{category_prefix}-{comic_info['title']}-(1-{comic_info['last_chapter']}话)-肉漫天堂-{comic_info['id']}.pdf"
        merged_pdf_path = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂合并PDF',
            merged_pdf_name
        )

        if os.path.exists(merged_pdf_path) and not incremental:
            print(f"{Fore.YELLOW}合并PDF已存在: {merged_pdf_name}{Style.RESET_ALL}")
            return merged_pdf_path

        try:
            # 获取所有需要合并的PDF文件
            pdf_files = []

            # 增量合并：从NAS下载旧PDF
            if incremental:
                # 获取旧PDF文件名
                global_log = self.logger.read_global_log()
                old_pdf_file = global_log.get(comic_info['id'], {}).get('pdf_file', '')

                if old_pdf_file:
                    # 下载旧PDF到中间件目录
                    nas_middleware_dir = os.path.join(CONFIG['BASE_DIR'], '肉漫天堂NAS中间件')
                    os.makedirs(nas_middleware_dir, exist_ok=True)
                    local_old_pdf = os.path.join(nas_middleware_dir,
                                                 f"{comic_info['id']}-{old_pdf_file}")

                    # 确定NAS上的远程路径
                    folder_name = CONFIG['CATEGORY_MAP'].get(comic_info['category'], '肉漫天堂主页')
                    remote_dir = os.path.join(
                        CONFIG['NAS_CONFIG']['target_dir'],
                        folder_name
                    ).replace('\\', '/')
                    remote_old_pdf = os.path.join(remote_dir, old_pdf_file).replace('\\', '/')

                    # 下载旧PDF
                    if self.nas and self.nas.download_file(remote_old_pdf, local_old_pdf):
                        pdf_files.append(local_old_pdf)
                        print(f"{Fore.GREEN}已下载旧PDF用于增量合并{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.RED}下载旧PDF失败，转为全量合并{Style.RESET_ALL}")
                        incremental = False

            # 非增量合并或增量合并失败时，添加封面
            if not incremental:
                # 首先加入封面PDF
                cover_pdf = os.path.join(chapters_dir, f"{comic_info['title']}-{comic_info['id']}-封面.pdf")
                if os.path.exists(cover_pdf):
                    pdf_files.append(cover_pdf)
                else:
                    print(f"{Fore.YELLOW}未找到封面PDF{Style.RESET_ALL}")

            # 然后按顺序添加章节PDF
            for chapter in sorted(comic_info['chapters'], key=lambda x: int(x['number'])):
                # 确保chapter['title']已经经过sanitize_filename处理
                chapter_pdf = os.path.join(chapters_dir, f"{chapter['title']}.pdf")
                if os.path.exists(chapter_pdf):
                    pdf_files.append(chapter_pdf)
                else:
                    print(f"{Fore.YELLOW}未找到章节PDF: {chapter['title']}.pdf，跳过{Style.RESET_ALL}")

            if not pdf_files:
                print(f"{Fore.RED}没有找到任何PDF文件，无法生成合并PDF{Style.RESET_ALL}")
                return None

            print(f"{Fore.GREEN}开始合并 {len(pdf_files)} 个PDF文件{Style.RESET_ALL}")
            merger = PyPDF2.PdfMerger()

            for pdf in pdf_files:
                try:
                    merger.append(pdf)
                except Exception as e:
                    print(f"{Fore.RED}合并PDF时出错: {os.path.basename(pdf)} - {str(e)}{Style.RESET_ALL}")
                    self.logger.log_error(f"合并PDF时出错: {pdf} - {str(e)}")

            os.makedirs(os.path.dirname(merged_pdf_path), exist_ok=True)
            merger.write(merged_pdf_path)
            merger.close()

            print(f"{Fore.GREEN}合并PDF完成: {merged_pdf_name}{Style.RESET_ALL}")
            return merged_pdf_path
        except Exception as e:
            self.logger.log_error(f"合并PDF失败: {comic_info['title']} - {str(e)}")
            print(f"{Fore.RED}合并PDF失败: {comic_info['title']} - {str(e)}{Style.RESET_ALL}")
            return None

    def upload_to_nas(self, comic_info, category):
        """
        上传合并PDF到NAS，添加重试机制，并返回上传是否成功

        Args:
            comic_info: 漫画信息字典
            category: 漫画分类

        Returns:
            bool: 上传是否成功
        """
        if not self.nas:
            print(f"{Fore.YELLOW}NAS未启用，跳过上传{Style.RESET_ALL}")
            return False

        # 上传合并PDF
        # 确保title已经经过sanitize_filename处理
        merged_pdf_path = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂合并PDF',
            f"{CONFIG['CATEGORY_PREFIX_MAP'].get(category, '')}-{comic_info['title']}-(1-{comic_info['last_chapter']}话)-肉漫天堂-{comic_info['id']}.pdf"
        )

        if os.path.exists(merged_pdf_path):
            # 使用漫画实际分类来决定上传路径
            folder_name = CONFIG['CATEGORY_MAP'].get(comic_info['category'], '肉漫天堂主页')
            remote_dir = os.path.join(
                CONFIG['NAS_CONFIG']['target_dir'],
                folder_name
            ).replace('\\', '/')

            remote_path = os.path.join(remote_dir, os.path.basename(merged_pdf_path)).replace('\\', '/')
            print(f"{Fore.CYAN}上传合并PDF到NAS: {os.path.basename(merged_pdf_path)}{Style.RESET_ALL}")

            # 删除NAS上的旧PDF（如果存在）
            global_log = self.logger.read_global_log()
            old_pdf_file = global_log.get(comic_info['id'], {}).get('pdf_file', '')
            if old_pdf_file:
                old_remote_path = os.path.join(remote_dir, old_pdf_file).replace('\\', '/')
                try:
                    self.nas.conn.deleteFiles(self.nas.share_name, old_remote_path)
                    print(f"{Fore.CYAN}已删除NAS上的旧PDF: {old_pdf_file}{Style.RESET_ALL}")
                except Exception as e:
                    print(f"{Fore.YELLOW}删除NAS上的旧PDF失败: {str(e)}{Style.RESET_ALL}")

            # 添加重试机制
            max_retries = 3
            retry_delay = 5
            for retry in range(max_retries):
                try:
                    if retry > 0:
                        print(f"{Fore.YELLOW}上传重试 ({retry + 1}/{max_retries})...{Style.RESET_ALL}")

                    result = self.nas.upload_file(merged_pdf_path, remote_path)
                    if result:
                        # 验证上传文件是否成功
                        if self.verify_file_upload(merged_pdf_path, remote_path):
                            print(f"{Fore.GREEN}合并PDF上传成功并验证通过{Style.RESET_ALL}")
                            return True
                        else:
                            print(f"{Fore.RED}上传验证失败，文件可能不完整{Style.RESET_ALL}")
                            self.logger.log_error(f"上传验证失败: {os.path.basename(merged_pdf_path)}")
                            # 继续尝试下一次重试
                    else:
                        print(f"{Fore.RED}上传合并PDF失败{Style.RESET_ALL}")
                        self.logger.log_error(f"上传失败: {os.path.basename(merged_pdf_path)}")
                except Exception as e:
                    print(f"{Fore.RED}上传出错: {str(e)}{Style.RESET_ALL}")
                    self.logger.log_error(f"上传出错: {os.path.basename(merged_pdf_path)} - {str(e)}")

                # 如果重试次数未达到最大，则等待后重试
                if retry < max_retries - 1:
                    print(f"{Fore.YELLOW}等待 {retry_delay} 秒后重试...{Style.RESET_ALL}")
                    time.sleep(retry_delay)
                    # 递增重试延迟，避免频繁请求
                    retry_delay += 2

            print(f"{Fore.RED}上传失败，已达最大重试次数，保留本地文件{Style.RESET_ALL}")
            return False
        else:
            print(f"{Fore.YELLOW}合并PDF不存在，无法上传{Style.RESET_ALL}")
            return False

    def update_comic_log(self, comic_info, log_name, category, website_chapters, local_chapters, pdf_file):
        # 更新单漫画日志
        log_data = {
            'id': comic_info['id'],
            'title': comic_info['title'],
            'category': category,
            'latest_chapter': comic_info['latest_chapter'],  # 改为latest_chapter以保持一致
            'first_chapter': comic_info['first_chapter'],
            'last_chapter': comic_info['last_chapter'],
            'update_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'pdf_file': os.path.basename(pdf_file) if pdf_file else ''
        }
        log_dir = os.path.join(CONFIG['BASE_DIR'], '肉漫天堂日志', '肉漫天堂对比日志')
        os.makedirs(log_dir, exist_ok=True)
        log_path = os.path.join(log_dir, f"{log_name}.txt")  # 改为TXT格式
        try:
            with open(log_path, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            print(f"{Fore.GREEN}已更新日志: {log_name}{Style.RESET_ALL}")
        except Exception as e:
            self.logger.log_error(f"更新日志失败: {log_name} - {str(e)}")
            print(f"{Fore.RED}更新日志失败: {log_name} - {str(e)}{Style.RESET_ALL}")

        # 更新全量对比日志
        global_data = {
            'comic_name': comic_info['title'],
            'website_chapters': website_chapters,
            'local_chapters': website_chapters,  # 更新后本地章节数等于网站章节数
            'last_updated': time.strftime('%Y-%m-%dT%H:%M:%S'),
            'pdf_file': os.path.basename(pdf_file) if pdf_file else ''
        }
        self.logger.update_global_log(comic_info['id'], global_data)
        print(f"{Fore.GREEN}已更新全量对比日志: {comic_info['id']}{Style.RESET_ALL}")

    def make_request(self, url, retry=0):
        try:
            headers = {'User-Agent': self.ua_pool.get_ua()}
            headers.update(CONFIG['HEADERS'])
            time.sleep(random.uniform(*CONFIG['DELAY']))
            response = self.session.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            return response
        except requests.exceptions.Timeout:
            if retry < CONFIG['RETRY']['max_retries']:
                print(f"{Fore.YELLOW}请求超时: {url} | 重试第{retry + 1}次{Style.RESET_ALL}")
                time.sleep(CONFIG['RETRY']['retry_delay'])
                return self.make_request(url, retry + 1)
            else:
                print(f"{Fore.RED}请求超时: {url} | 已达最大重试次数{Style.RESET_ALL}")
                raise Exception(f"请求超时: {url} | 已达最大重试次数")
        except requests.exceptions.ConnectionError:
            if retry < CONFIG['RETRY']['max_retries']:
                print(f"{Fore.YELLOW}连接错误: {url} | 重试第{retry + 1}次{Style.RESET_ALL}")
                time.sleep(CONFIG['RETRY']['retry_delay'])
                return self.make_request(url, retry + 1)
            else:
                print(f"{Fore.RED}连接错误: {url} | 已达最大重试次数 - 请检查网络连接{Style.RESET_ALL}")
                raise Exception(f"连接错误: {url} | 请检查网络连接")
        except requests.exceptions.HTTPError as e:
            if retry < CONFIG['RETRY']['max_retries']:
                print(
                    f"{Fore.YELLOW}HTTP错误: {url} | 状态码: {e.response.status_code} | 重试第{retry + 1}次{Style.RESET_ALL}")
                time.sleep(CONFIG['RETRY']['retry_delay'])
                return self.make_request(url, retry + 1)
            else:
                print(f"{Fore.RED}HTTP错误: {url} | 状态码: {e.response.status_code}{Style.RESET_ALL}")
                raise Exception(f"HTTP错误: {url} | 状态码: {e.response.status_code}")
        except Exception as e:
            if retry < CONFIG['RETRY']['max_retries']:
                print(f"{Fore.YELLOW}请求失败: {url} | 错误: {str(e)} | 重试第{retry + 1}次{Style.RESET_ALL}")
                time.sleep(CONFIG['RETRY']['retry_delay'])
                return self.make_request(url, retry + 1)
            else:
                print(f"{Fore.RED}请求失败: {url} | 错误: {str(e)}{Style.RESET_ALL}")
                raise Exception(f"请求失败: {url} | 错误: {str(e)}")

    def verify_file_upload(self, local_path, remote_path):
        """
        验证上传文件的完整性

        Args:
            local_path: 本地文件路径
            remote_path: 远程文件路径

        Returns:
            bool: 验证是否通过
        """
        try:
            # 获取本地文件大小
            local_size = os.path.getsize(local_path)

            # 获取远程文件信息
            file_obj = self.nas.conn.getAttributes(self.nas.share_name, remote_path)
            remote_size = file_obj.file_size

            # 比较文件大小
            if abs(local_size - remote_size) <= 100:  # 允许100字节的误差
                return True
            else:
                print(f"{Fore.YELLOW}文件大小不匹配: 本地 {local_size} 字节, 远程 {remote_size} 字节{Style.RESET_ALL}")
                self.logger.log_error(f"验证失败: 文件大小不匹配 - 本地:{local_size}字节, 远程:{remote_size}字节")
                return False
        except Exception as e:
            print(f"{Fore.RED}验证上传文件失败: {str(e)}{Style.RESET_ALL}")
            self.logger.log_error(f"验证上传文件失败: {remote_path} - {str(e)}")
            return False

    def is_blank_image(self, img_bytes):
        try:
            img = Image.open(io.BytesIO(img_bytes)).convert('RGB')
            extrema = img.getextrema()
            if all([e[0] == e[1] for e in extrema]):
                return True
            if len(img_bytes) < 1024:
                return True
            return False
        except Exception:
            return True

    # ===== 新增：首次下载PDF合并 =====
    def generate_merged_pdf_first(self, comic_info):
        print(f"{Fore.GREEN}首次下载：生成合并PDF: {comic_info['title']}{Style.RESET_ALL}")
        category = comic_info['category']
        category_prefix = CONFIG['CATEGORY_PREFIX_MAP'].get(category, '')
        chapters_dir = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂ComicPDFs',
            f"{comic_info['title']}-{comic_info['id']}"
        )
        merged_pdf_name = f"{category_prefix}-{comic_info['title']}-(1-{comic_info['last_chapter']}话)-肉漫天堂-{comic_info['id']}.pdf"
        merged_pdf_path = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂合并PDF',
            merged_pdf_name
        )
        # 合并封面+所有章节PDF
        pdf_files = []
        cover_pdf = os.path.join(chapters_dir, f"{comic_info['title']}-{comic_info['id']}-封面.pdf")
        if os.path.exists(cover_pdf):
            pdf_files.append(cover_pdf)
        for chapter in sorted(comic_info['chapters'], key=lambda x: int(x['number'])):
            chapter_pdf = os.path.join(chapters_dir, f"{chapter['title']}.pdf")
            if os.path.exists(chapter_pdf):
                pdf_files.append(chapter_pdf)
        if not pdf_files:
            print(f"{Fore.RED}没有找到任何PDF文件，无法生成合并PDF{Style.RESET_ALL}")
            return None
        from PyPDF2 import PdfMerger
        merger = PdfMerger()
        for pdf in pdf_files:
            merger.append(pdf)
        os.makedirs(os.path.dirname(merged_pdf_path), exist_ok=True)
        merger.write(merged_pdf_path)
        merger.close()
        # 书签处理（首次下载）
        self.write_bookmarks_first(merged_pdf_path, comic_info)
        return merged_pdf_path

    # ===== 新增：增量更新PDF合并 =====
    def generate_merged_pdf_increment(self, comic_info, local_chapters):
        print(f"{Fore.GREEN}增量更新：生成合并PDF: {comic_info['title']}{Style.RESET_ALL}")
        category = comic_info['category']
        category_prefix = CONFIG['CATEGORY_PREFIX_MAP'].get(category, '')
        chapters_dir = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂ComicPDFs',
            f"{comic_info['title']}-{comic_info['id']}"
        )
        merged_pdf_name = f"{category_prefix}-{comic_info['title']}-(1-{comic_info['last_chapter']}话)-肉漫天堂-{comic_info['id']}.pdf"
        merged_pdf_path = os.path.join(
            CONFIG['BASE_DIR'],
            '肉漫天堂合并PDF',
            merged_pdf_name
        )
        # 下载旧PDF，保存到nas_temp_dir/bookid/旧PDF文件名
        global_log = self.logger.read_global_log()
        old_pdf_file = global_log.get(comic_info['id'], {}).get('pdf_file', '')
        old_pdf_path = None
        if old_pdf_file:
            nas_middleware_dir = os.path.join(CONFIG['BASE_DIR'], '肉漫天堂NAS中间件', str(comic_info['id']))
            os.makedirs(nas_middleware_dir, exist_ok=True)
            old_pdf_path = os.path.join(nas_middleware_dir, old_pdf_file)
            folder_name = CONFIG['CATEGORY_MAP'].get(comic_info['category'], '肉漫天堂主页')
            remote_dir = os.path.join(CONFIG['NAS_CONFIG']['target_dir'], folder_name).replace('\\', '/')
            remote_old_pdf = os.path.join(remote_dir, old_pdf_file).replace('\\', '/')
            if self.nas and not os.path.exists(old_pdf_path):
                self.nas.download_file(remote_old_pdf, old_pdf_path)
        # 合并旧PDF+新增章节PDF
        pdf_files = []
        if old_pdf_path and os.path.exists(old_pdf_path):
            pdf_files.append(old_pdf_path)
        for chapter in sorted(comic_info['chapters'], key=lambda x: int(x['number'])):
            chapter_pdf = os.path.join(chapters_dir, f"{chapter['title']}.pdf")
            if os.path.exists(chapter_pdf):
                pdf_files.append(chapter_pdf)
        if not pdf_files:
            print(f"{Fore.RED}没有找到任何PDF文件，无法生成合并PDF{Style.RESET_ALL}")
            return None, old_pdf_path
        from PyPDF2 import PdfMerger
        merger = PdfMerger()
        for pdf in pdf_files:
            merger.append(pdf)
        os.makedirs(os.path.dirname(merged_pdf_path), exist_ok=True)
        merger.write(merged_pdf_path)
        merger.close()
        # 书签处理（增量更新）
        self.write_bookmarks_increment(merged_pdf_path, old_pdf_path, comic_info)
        return merged_pdf_path, old_pdf_path

    # ===== 新增：首次下载书签写入 =====
    def write_bookmarks_first(self, merged_pdf_path, comic_info):
        try:
            from PyPDF2 import PdfReader, PdfWriter
            reader = PdfReader(merged_pdf_path)
            writer = PdfWriter()
            for page in reader.pages:
                writer.add_page(page)
            # 写入书签
            page_idx = 1  # 封面为第0页
            for chapter in comic_info['chapters']:
                writer.add_outline_item(chapter['title'], page_idx)
                page_idx += 1
            with open(merged_pdf_path, 'wb') as f:
                writer.write(f)
        except Exception as e:
            print(f"{Fore.RED}首次下载写入书签失败: {str(e)}{Style.RESET_ALL}")

    # ===== 新增：增量更新书签写入 =====
    def write_bookmarks_increment(self, merged_pdf_path, old_pdf_path, comic_info):
        try:
            from PyPDF2 import PdfReader, PdfWriter
            writer = PdfWriter()
            # 先写入旧PDF内容和书签
            if old_pdf_path and os.path.exists(old_pdf_path):
                old_reader = PdfReader(old_pdf_path)
                for page in old_reader.pages:
                    writer.add_page(page)
                # 复制旧书签
                if hasattr(old_reader, 'outline'):
                    for item in old_reader.outline:
                        if isinstance(item, dict) and 'title' in item and 'page_number' in item:
                            writer.add_outline_item(item['title'], item['page_number'])
            # 再写入新增章节内容和书签
            new_reader = PdfReader(merged_pdf_path)
            old_page_count = 0
            if old_pdf_path and os.path.exists(old_pdf_path):
                old_page_count = len(PdfReader(old_pdf_path).pages)
            for idx, chapter in enumerate(comic_info['chapters']):
                page_idx = old_page_count + idx
                writer.add_outline_item(chapter['title'], page_idx)
            for page in new_reader.pages[old_page_count:]:
                writer.add_page(page)
            with open(merged_pdf_path, 'wb') as f:
                writer.write(f)
        except Exception as e:
            print(f"{Fore.RED}增量更新写入书签失败: {str(e)}{Style.RESET_ALL}")

    # ===== 新增：首次下载上传NAS =====
    def upload_to_nas_first(self, comic_info, category, merged_pdf_path):
        if not self.nas:
            print(f"{Fore.YELLOW}NAS未启用，跳过上传{Style.RESET_ALL}")
            return False
        folder_name = CONFIG['CATEGORY_MAP'].get(comic_info['category'], '肉漫天堂主页')
        remote_dir = os.path.join(CONFIG['NAS_CONFIG']['target_dir'], folder_name).replace('\\', '/')
        remote_path = os.path.join(remote_dir, os.path.basename(merged_pdf_path)).replace('\\', '/')
        print(f"{Fore.CYAN}首次下载上传合并PDF到NAS: {os.path.basename(merged_pdf_path)}{Style.RESET_ALL}")
        result = self.nas.upload_file(merged_pdf_path, remote_path)
        if result:
            # 校验：NAS文件体积与本地一致
            try:
                local_size = os.path.getsize(merged_pdf_path)
                attrs = self.nas.conn.getAttributes(self.nas.share_name, remote_path)
                nas_size = attrs.file_size
                if nas_size == local_size:
                    print(f"{Fore.GREEN}合并PDF上传成功并验证通过{Style.RESET_ALL}")
                    self.print_pdf_info(merged_pdf_path, comic_info['id'], '首次下载')
                    return True
                else:
                    print(f"{Fore.RED}上传验证失败: 本地体积={local_size}, NAS体积={nas_size}{Style.RESET_ALL}")
            except Exception as e:
                print(f"{Fore.RED}NAS校验异常: {str(e)}{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}上传合并PDF失败{Style.RESET_ALL}")
        return False

    # ===== 新增：增量更新上传NAS =====
    def upload_to_nas_increment(self, comic_info, category, merged_pdf_path, old_pdf_path):
        if not self.nas:
            print(f"{Fore.YELLOW}NAS未启用，跳过上传{Style.RESET_ALL}")
            return False
        folder_name = CONFIG['CATEGORY_MAP'].get(comic_info['category'], '肉漫天堂主页')
        remote_dir = os.path.join(CONFIG['NAS_CONFIG']['target_dir'], folder_name).replace('\\', '/')
        remote_path = os.path.join(remote_dir, os.path.basename(merged_pdf_path)).replace('\\', '/')
        print(f"{Fore.CYAN}增量更新上传合并PDF到NAS: {os.path.basename(merged_pdf_path)}{Style.RESET_ALL}")
        result = self.nas.upload_file(merged_pdf_path, remote_path)
        if result:
            # 校验：NAS文件体积大于旧PDF体积，且页数=旧PDF页数+新增章节数
            try:
                from PyPDF2 import PdfReader
                new_reader = PdfReader(merged_pdf_path)
                new_pages = len(new_reader.pages)
                new_size = os.path.getsize(merged_pdf_path)
                attrs = self.nas.conn.getAttributes(self.nas.share_name, remote_path)
                nas_size = attrs.file_size
                old_pages = 0
                old_size = 0
                if old_pdf_path and os.path.exists(old_pdf_path):
                    old_reader = PdfReader(old_pdf_path)
                    old_pages = len(old_reader.pages)
                    old_size = os.path.getsize(old_pdf_path)
                add_pages = new_pages - old_pages
                add_size = nas_size - old_size
                self.print_pdf_info(merged_pdf_path, comic_info['id'], '增量更新', old_pdf_path=old_pdf_path)
                if add_pages > 0 and nas_size > old_size:
                    print(f"{Fore.GREEN}增量合并PDF上传成功并通过页数/体积校验{Style.RESET_ALL}")
                    return True
                else:
                    print(f"{Fore.RED}增量合并PDF校验失败: 新增页数={add_pages}, 新增体积={add_size}, NAS体积={nas_size}, 旧体积={old_size}{Style.RESET_ALL}")
            except Exception as e:
                print(f"{Fore.RED}增量合并PDF校验异常: {str(e)}{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}上传合并PDF失败{Style.RESET_ALL}")
        return False

    # ===== 新增：打印PDF信息 =====
    def print_pdf_info(self, pdf_path, bookid, tag, old_pdf_path=None):
        try:
            from PyPDF2 import PdfReader
            if not pdf_path or not os.path.exists(pdf_path):
                print(f"{Fore.YELLOW}{tag} PDF不存在: {pdf_path}{Style.RESET_ALL}")
                return
            reader = PdfReader(pdf_path)
            size = os.path.getsize(pdf_path)
            pages = len(reader.pages)
            bookmarks = 0
            if hasattr(reader, 'outline'):
                try:
                    bookmarks = len(reader.outline)
                except Exception:
                    bookmarks = 0
            print(f"{Fore.CYAN}[{tag}] PDF信息 - BOOKID:{bookid} 体积:{size}字节 页数:{pages} 书签数:{bookmarks}{Style.RESET_ALL}")
            if old_pdf_path and os.path.exists(old_pdf_path):
                old_reader = PdfReader(old_pdf_path)
                old_size = os.path.getsize(old_pdf_path)
                old_pages = len(old_reader.pages)
                old_bookmarks = 0
                if hasattr(old_reader, 'outline'):
                    try:
                        old_bookmarks = len(old_reader.outline)
                    except Exception:
                        old_bookmarks = 0
                print(f"{Fore.MAGENTA}[旧PDF] PDF信息 - BOOKID:{bookid} 体积:{old_size}字节 页数:{old_pages} 书签数:{old_bookmarks}{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}打印PDF信息失败: {str(e)}{Style.RESET_ALL}")

    def safe_remove(self, file_path, max_retry=3):
        import time, os
        for _ in range(max_retry):
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                return
            except Exception as e:
                time.sleep(0.2)
        print(f"[图片删除失败] {file_path}")


if __name__ == "__main__":
    downloader = ComicDownloader()
    downloader.main_flow()