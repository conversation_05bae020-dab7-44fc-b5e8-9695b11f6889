import smbclient
import re
import os
import logging
from openpyxl import Workbook
from openpyxl.styles import Alignment
from smbclient import scandir, remove
import datetime
from collections import defaultdict

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,  # 修改为INFO级别
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置信息
config = {
    'enabled': True,
    'server_ip': '**************',
    'server_name': 'D1581-fnos-91',
    'share_name': '小说',
    'username': 'admin',
    'password': 'ly120220',
    'target_dir': '漫画/韩漫自爬',
    'client_name': 'ComicDownloader'
}


def parse_file_info(filename):
    """解析文件名返回基础名称和结束页码"""
    # 只以'+'为分隔符，保留(无碼版)等信息
    base_name = filename.split('+')[0].strip()
    # 提取结束页码
    page_match = re.search(r'(\d+)-(\d+)(?:话)?[）)]?', filename)
    if page_match:
        return base_name, int(page_match.group(2))
    return base_name, None


def main():
    try:
        logger.info("开始处理NAS文件去重任务")

        # 初始化SMB连接
        logger.info(f"正在连接NAS服务器: {config['server_name']}")
        smbclient.register_session(
            server=config['server_name'],
            username=config['username'],
            password=config['password']
        )

        # 构建SMB路径
        smb_path = f"\\\\{config['server_name']}\\{config['share_name']}\\{config['target_dir'].replace('/', '\\')}"

        # 获取文件列表
        logger.info("正在扫描目标目录...")
        file_groups = defaultdict(list)
        file_count = 0
        deleted_count = 0  # 新增：统计删除数量
        for entry in scandir(smb_path):
            if entry.is_file():
                file_count += 1
                base, _ = parse_file_info(entry.name)
                # 获取文件最后修改时间
                mtime = entry.stat().st_mtime
                file_groups[base].append((entry.name, mtime))
        logger.info(f"扫描完成，共发现{file_count}个文件")

        # 处理重复文件
        logger.info("开始处理重复文件")
        log_data = []
        serial_num = 1
        processed_groups = 0

        for base_name, files in file_groups.items():
            if len(files) < 2:
                continue
            processed_groups += 1

            # files: [(filename, mtime)]
            files_with_page = []
            for filename, mtime in files:
                _, end_page = parse_file_info(filename)
                files_with_page.append((filename, mtime, end_page if end_page is not None else -1))

            # 先按end_page降序，再按mtime降序
            sorted_files = sorted(files_with_page, key=lambda x: (x[2], x[1]), reverse=True)

            # 找到最大end_page
            max_page = sorted_files[0][2]
            # 找到所有最大end_page的文件
            max_page_files = [f for f in sorted_files if f[2] == max_page]
            # 在这些中按mtime降序，保留第一个
            max_page_files_sorted = sorted(max_page_files, key=lambda x: x[1], reverse=True)
            keep_file = max_page_files_sorted[0][0]

            # 记录日志
            for filename, mtime, end_page in sorted_files:
                status = '已保留' if filename == keep_file else '已删除'
                log_data.append({
                    'serial': serial_num if filename == keep_file else '',
                    'name': filename,
                    'status': status,
                    'mtime': datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S'),
                    'end_page': end_page if end_page != -1 else ''
                })

            # 执行删除操作（只保留keep_file）
            for filename, _, _ in sorted_files:
                if filename != keep_file:
                    try:
                        logger.info(f"正在删除文件: {filename}")
                        remove(f"{smb_path}\\{filename}")
                        deleted_count += 1  # 新增：删除成功则计数
                    except Exception as e:
                        logger.error(f"删除失败 {filename}: {str(e)}")

            serial_num += 1
        logger.info(f"处理完成，共处理{processed_groups}个重复文件组")

        # 生成Excel报表
        logger.info("开始生成Excel报表")
        wb = Workbook()
        ws = wb.active
        ws.title = "重复文件处理结果"
        ws.append(["序号", "漫画名称", "状态", "最后修改时间", "最后章节数字"])

        # 设置列宽
        ws.column_dimensions['A'].width = 10
        ws.column_dimensions['B'].width = 80
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 22
        ws.column_dimensions['E'].width = 15

        # 填充数据
        for log in log_data:
            row = [log['serial'], log['name'], log['status'], log['mtime'], log['end_page']]
            ws.append(row)

        # 设置对齐方式
        for row in ws.iter_rows(min_row=2):
            row[0].alignment = Alignment(horizontal='center')
            row[2].alignment = Alignment(horizontal='center')
            row[3].alignment = Alignment(horizontal='center')
            row[4].alignment = Alignment(horizontal='center')

        # 生成文件名和路径
        date_str = datetime.datetime.now().strftime("%Y%m%d")
        output_dir = r'C:\Users\<USER>\Downloads'
        output_file = os.path.join(output_dir, f"D1581-韩漫自爬去重-漫小肆-{date_str}.xlsx")

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 保存文件
        wb.save(output_file)
        logger.info(f"报表已保存至: {output_file}")
        print(f"\n处理完成，结果已保存至：{output_file}")
        # 新增：统一输出统计信息
        print(f"扫描前文件总数={file_count}，删除总数={deleted_count}，删除后总数={file_count - deleted_count}")

    except Exception as e:
        logger.exception("程序运行出现异常:")
        raise


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
    except Exception as e:
        logger.error(f"程序异常终止: {str(e)}")