import re
import csv
import random
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0"
]

def extract_movie_data(html_content):
    """从HTML内容中提取电影ID和名称（适配新版豆瓣）"""
    soup = BeautifulSoup(html_content, 'html.parser')
    movies = []
    for a in soup.find_all('a', href=re.compile(r'/doubanapp/dispatch\\?uri=/movie/\\d+')):
        href = a.get('href')
        match = re.search(r'uri=/movie/(\\d+)', href)
        if not match:
            continue
        movie_id = match.group(1)
        title_elem = a.find('span', class_='drc-subject-info-title-text')
        if not title_elem:
            continue
        title = title_elem.get_text(strip=True)
        if not title:
            continue
        movies.append({'ID': movie_id, 'Title': title})
    unique_movies = []
    seen = set()
    for m in movies:
        if m['ID'] not in seen:
            unique_movies.append(m)
            seen.add(m['ID'])
    return unique_movies

def scrape_douban():
    # 设置Selenium和UA
    chrome_options = Options()
    chrome_options.add_argument(f'user-agent={random.choice(USER_AGENTS)}')
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--window-size=1920,1080')
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 10)
    try:
        print("打开豆瓣电影探索页面...")
        driver.get('https://movie.douban.com/explore')
        time.sleep(2)
        # 不断点击"加载更多"直到按钮消失
        while True:
            try:
                load_more = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '.subject-list-more button')))
                driver.execute_script("arguments[0].scrollIntoView();", load_more)
                time.sleep(0.5)
                load_more.click()
                print("点击一次加载更多...")
                time.sleep(1.5)
            except Exception:
                print("没有更多可加载的内容，或按钮已消失。")
                break
        html = driver.page_source
        movies = extract_movie_data(html)
        if not movies:
            print("警告: 未提取到任何电影信息")
            with open('debug_page.html', 'w', encoding='utf-8') as f:
                f.write(html)
            print("已保存页面内容到 debug_page.html 用于调试")
            try:
                os.startfile('debug_page.html')
            except Exception as e:
                print(f"自动打开debug_page.html失败: {e}")
            return
        file_path = 'C:/Users/<USER>/Downloads/豆瓣电影ID.csv'
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=['ID', 'Title'])
            writer.writeheader()
            writer.writerows(movies)
        print(f"成功提取并保存了 {len(movies)} 部电影的信息到 {file_path}")
    except Exception as e:
        print(f"抓取过程中出错: {str(e)}")
    finally:
        driver.quit()

if __name__ == '__main__':
    scrape_douban()