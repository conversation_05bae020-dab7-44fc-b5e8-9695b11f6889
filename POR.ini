import os
import subprocess
from datetime import datetime
import logging
import json
import re
import glob

# 配置基本参数
BASE_URL_TEMPLATE = "https://cn.pornhub.com/model/{actor_code}"
BASE_DOWNLOAD_DIR = r"D:\ComicsDownloads\pornhub"
LOG_DIR = os.path.join(BASE_DOWNLOAD_DIR, "logs")

def setup_logger(log_file):
    os.makedirs(LOG_DIR, exist_ok=True)
    logger = logging.getLogger("pornhub_downloader")
    logger.setLevel(logging.INFO)
    # 清除旧handler，防止多次添加
    if logger.hasHandlers():
        logger.handlers.clear()
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    return logger


def download_videos_by_actor(actor_code):
    base_url = BASE_URL_TEMPLATE.format(actor_code=actor_code)
    download_dir = os.path.join(BASE_DOWNLOAD_DIR, actor_code)
    os.makedirs(download_dir, exist_ok=True)
    log_file = os.path.join(LOG_DIR, f"{actor_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    logger = setup_logger(log_file)
    logger.info(f"开始下载演员: {actor_code}")
    video_output = os.path.join(download_dir, '%(title)s.%(ext)s')
    archive_txt = os.path.join(LOG_DIR, f"{actor_code}_archive.txt")
    cmd = (
        f'yt-dlp '
        f'--write-info-json '
        f'--write-thumbnail '
        f'--write-description '
        f'--write-annotations '
        f'--write-sub '
        f'--sub-lang all '
        f'--output "{video_output}" '
        f'--download-archive "{archive_txt}" '
        f'"{base_url}" '
        f'--verbose'
    )
    logger.info(f"执行命令: {cmd}")
    _run_yt_dlp(cmd, logger, actor_code)


def download_video_by_id(video_id):
    # pornhub单个视频链接格式
    video_url = f"https://cn.pornhub.com/view_video.php?viewkey={video_id}"
    download_dir = os.path.join(BASE_DOWNLOAD_DIR, "single_video")
    os.makedirs(download_dir, exist_ok=True)
    log_file = os.path.join(LOG_DIR, f"single_{video_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    logger = setup_logger(log_file)
    logger.info(f"开始下载视频ID: {video_id}")
    video_output = os.path.join(download_dir, '%(title)s.%(ext)s')
    archive_txt = os.path.join(LOG_DIR, "single_video_archive.txt")
    cmd = (
        f'yt-dlp '
        f'--write-info-json '
        f'--write-thumbnail '
        f'--write-description '
        f'--write-annotations '
        f'--write-sub '
        f'--sub-lang all '
        f'--output "{video_output}" '
        f'--download-archive "{archive_txt}" '
        f'"{video_url}" '
        f'--verbose'
    )
    logger.info(f"执行命令: {cmd}")
    _run_yt_dlp(cmd, logger, video_id)


def _run_yt_dlp(cmd, logger, tag):
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            shell=True,
            encoding='utf-8',
            errors='replace'
        )
        skip_count = 0
        new_count = 0
        for line in iter(process.stdout.readline, ''):
            line = line.strip()
            if not line:
                continue
            logger.info(line)
            if "[download] Skipping" in line or "already in archive" in line:
                skip_count += 1
            elif "[download] Downloading" in line or "Downloading video" in line:
                new_count += 1
        process.wait()
        total_videos = skip_count + new_count
        logger.info(f"下载完成! 总共处理 {total_videos} 个视频")
        logger.info(f"跳过 {skip_count} 个已下载视频")
        logger.info(f"下载 {new_count} 个新视频")
    except Exception as e:
        logger.error(f"下载过程中出现异常: {str(e)}")
        logger.exception("异常详情:")
    finally:
        logger.info(f"{tag} 下载任务结束")


def main():
    print("请选择下载方式：")
    print("1. 按演员名称下载全部视频")
    print("2. 按影片ID下载单个视频")
    choice = input("请输入 1 或 2：").strip()
    if choice == "1":
        actor = input("请输入演员名称（如 elle-lee）：").strip()
        download_videos_by_actor(actor)
    elif choice == "2":
        video_id = input("请输入影片ID（如 ph5e1f2b123456）：").strip()
        download_video_by_id(video_id)
    else:
        print("无效选择，请重新运行程序。")


if __name__ == "__main__":
    main()