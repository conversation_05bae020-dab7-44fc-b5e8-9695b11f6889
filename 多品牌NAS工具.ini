import socket
import threading
import re
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
import ipaddress
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
import uuid
import base64
from cryptography.fernet import Fernet
try:
    from smbprotocol.connection import Connection
    from smbprotocol.session import Session
    from smbprotocol.tree import TreeConnect
    from smbprotocol.open import Open, CreateDisposition, FileAttributes, FilePipePrinterAccessMask, ImpersonationLevel, ShareAccess, CreateOptions
except ImportError:
    pass  # 允许无smbprotocol时不报错
import tkinter.filedialog as filedialog
import ctypes
import ctypes.wintypes
import tkinter.simpledialog as simpledialog
import traceback
from PIL import Image, ImageTk
import locale
import time
import concurrent.futures


class FeiNiuScanner:
    def __init__(self):
        self.devices = []
        self.scanning = False
        self.callback = None
        self.total_ips = 0
        self.scanned_ips = 0
        self.progress_callback = None
        self.thread_count = DEVICE_SCAN_THREAD_COUNT
        self.ports_to_scan = [8000]
        self.devices_lock = threading.Lock()
        self.log_path = r'D:\飞牛NAS设备记录日志\\日志'
        self.log_file_feiniu = os.path.join(self.log_path, '飞牛设备信息_自组NAS.log')
        self.log_file_other = os.path.join(self.log_path, '飞牛设备信息_其他品牌.log')
        if not os.path.exists(self.log_path):
            os.makedirs(self.log_path)
        self.device_log_feiniu = self.load_device_log(self.log_file_feiniu)
        self.device_log_other = self.load_device_log(self.log_file_other)

    def get_local_ips(self):
        """获取本地所有IP地址（使用socket替代netifaces）"""
        ips = []
        try:
            # 获取本地主机名
            host_name = socket.gethostname()

            # 获取本机所有IP地址
            ip_list = socket.gethostbyname_ex(host_name)[2]

            # 获取子网掩码（使用系统命令）
            for ip in ip_list:
                if ip.startswith("127.") or ip.startswith("169.254."):
                    continue  # 跳过回环地址和链路本地地址

                # 尝试获取子网掩码（Windows和Linux/macOS方式不同）
                subnet_mask = self.get_subnet_mask(ip)
                if subnet_mask:
                    # 计算网络地址
                    network = self.calculate_network(ip, subnet_mask)
                    if network:
                        ips.append(network)
                else:
                    # 如果无法获取子网掩码，则假设是标准的/24子网
                    parts = ip.split('.')
                    ips.append(f"{parts[0]}.{parts[1]}.{parts[2]}.0/24")

        except Exception as e:
            print(f"获取本地IP地址时出错: {e}")

        # 如果以上方法失败，使用回退方法
        if not ips:
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                s.close()
                if local_ip:
                    parts = local_ip.split('.')
                    ips.append(f"{parts[0]}.{parts[1]}.{parts[2]}.0/24")
            except:
                pass

        return list(set(ips))  # 去重

    def get_subnet_mask(self, ip):
        """获取指定IP的子网掩码（使用系统命令）"""
        try:
            # Windows系统
            if sys.platform.startswith('win'):
                import subprocess
                result = subprocess.check_output(['ipconfig'], text=True)

                # 查找当前IP对应的子网掩码
                pattern = re.compile(
                    r"IPv4 Address.*?{}.*?Subnet Mask.*?(\d+\.\d+\.\d+\.\d+)".format(ip.replace('.', r'\.')),
                    re.DOTALL | re.IGNORECASE
                )
                match = pattern.search(result)
                if match:
                    return match.group(1)

            # Linux/macOS系统
            else:
                import netifaces
                for interface in netifaces.interfaces():
                    addrs = netifaces.ifaddresses(interface)
                    if netifaces.AF_INET in addrs:
                        for addr_info in addrs[netifaces.AF_INET]:
                            if addr_info['addr'] == ip:
                                return addr_info.get('netmask')

        except Exception:
            return None

        return None

    def calculate_network(self, ip, subnet_mask):
        """计算网络地址"""
        try:
            # 将IP和子网掩码转换为整数
            ip_int = int(ipaddress.IPv4Address(ip))
            mask_int = int(ipaddress.IPv4Address(subnet_mask))

            # 计算网络地址
            network_int = ip_int & mask_int
            network_ip = str(ipaddress.IPv4Address(network_int))

            # 计算CIDR前缀长度
            prefix = bin(mask_int).count("1")

            return f"{network_ip}/{prefix}"
        except Exception:
            return None

    def scan_network(self, callback, progress_callback=None, finished_callback=None):
        try:
            print(f"【DEBUG】scan_network 被调用，self.scanning={self.scanning}", flush=True)
            if self.scanning:
                self.devices = []
                self.callback = callback
                self.progress_callback = progress_callback
                self.finished_callback = finished_callback

                # 获取所有本地网络
                networks = self.get_local_ips()
                self.total_ips = 0
                self.scanned_ips = 0
                for network in networks:
                    net = ipaddress.ip_network(network, strict=False)
                    self.total_ips += (net.num_addresses - 2) * len(self.ports_to_scan)  # IP数*端口数

                # 创建线程扫描每个子网
                threads = []
                for network in networks:
                    thread = threading.Thread(target=self.scan_subnet, args=(network,))
                    thread.daemon = True
                    thread.start()
                    threads.append(thread)

                # 等待所有扫描完成
                for thread in threads:
                    thread.join()
                if self.finished_callback:
                    self.finished_callback()
            else:
                print(f"【DEBUG】scan_network 直接return, self.scanning={self.scanning}", flush=True)
        except Exception as e:
            print(f"【ERROR】scan_network异常: {e}", flush=True)

    def scan_subnet(self, network):
        """多线程并发扫描指定子网的端口"""
        try:
            net = ipaddress.ip_network(network, strict=False)
            tasks = []
            with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
                for ip in net.hosts():
                    ip_str = str(ip)
                    for port in self.ports_to_scan:
                        tasks.append(executor.submit(self.scan_ip_port, ip_str, port))
                for future in as_completed(tasks):
                    pass  # 不再在这里递增进度
        except Exception as e:
            print(f"扫描网络 {network} 时出错: {e}")

    def scan_ip_port(self, ip, port):
        print(f"[{threading.get_ident()}] 正在扫描 {ip}:{port}", flush=True)
        if self.check_port(ip, port):
            print(f"[{threading.get_ident()}] 端口开放: {ip}:{port}", flush=True)
            device_info = self.get_device_info(ip, port)
            if device_info:
                with self.devices_lock:
                    # 检查是否已存在相同IP和端口的设备，避免重复
                    exists = any(dev['ip'] == device_info['ip'] and str(dev['port']) == str(device_info['port']) for dev in self.devices)
                    if not exists:
                        self.devices.append(device_info)
                        if self.callback:
                            self.callback(self.devices)
        else:
            print(f"[{threading.get_ident()}] 端口关闭: {ip}:{port}", flush=True)
        # 进度递增和回调
        with self.devices_lock:
            self.scanned_ips += 1
        if self.progress_callback:
            self.progress_callback(self.scanned_ips, self.total_ips)

    def check_port(self, ip, port, timeout=0.5):
        """用HTTP请求判断端口是否开放"""
        try:
            url = f"http://{ip}:{port}"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
            response = requests.get(url, timeout=timeout, headers=headers)
            return response.status_code == 200
        except Exception as e:
            print(f"[{threading.get_ident()}] 检查端口 {ip}:{port} 失败: {e}", flush=True)
            return False

    def get_device_info(self, ip, port):
        """只要HTTP能访问就认为是飞牛设备"""
        try:
            url = f"http://{ip}:{port}"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
            response = requests.get(url, timeout=2, headers=headers)
            print(f"设备 {ip}:{port} 状态码: {response.status_code}，返回内容: {response.text[:100]}")
            if response.status_code == 200:
                info = {
                    'ip': ip,
                    'name': '飞牛NAS' if port == 8000 else '',
                    'cpu': '',
                    'memory': '未知',
                    'firmware': '未知',
                    'status': '在线',
                    'port': port,
                    'last_seen': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                log_dict, _ = self.get_log_dict_and_file(port)
                log = log_dict.get(ip)
                if log:
                    info['name'] = log.get('name', info['name'])
                    if port == 8000:
                        info['cpu'] = log.get('cpu', '')
                        info['model'] = log.get('model', '')
                return info
        except Exception as e:
            print(f"访问 {ip}:{port} 出错: {e}")
        return None

    def stop_scan(self):
        """停止扫描"""
        self.scanning = False

    def load_device_log(self, file_path):
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}

    def save_device_log(self, file_path, data):
        # 自动创建目录
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        # 保证data为字典
        if not isinstance(data, dict):
            data = {}
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f'写入日志失败: {e}')

    def get_log_dict_and_file(self, port):
        if int(port) == 8000:
            return self.device_log_feiniu, self.log_file_feiniu
        else:
            return self.device_log_other, self.log_file_other

    def get_unc_path(self, path):
        # 仅在Windows下有效
        if not path or not os.path.isabs(path):
            return path
        import ctypes
        import ctypes.wintypes
        buf = ctypes.create_unicode_buffer(1024)
        size = ctypes.wintypes.DWORD(1024)
        if ctypes.windll.mpr.WNetGetUniversalNameW(
            path,
            0x00000001,  # UNIVERSAL_NAME_INFO_LEVEL
            buf,
            ctypes.byref(size)
        ) == 0:
            unc = buf.value
            parts = unc.strip("\\").split("\\")
            if len(parts) >= 2:
                share = parts[1]
                subpath = "/".join(parts[2:]) if len(parts) > 2 else ""
                return share + ("/" + subpath if subpath else "")
        return path


class CredentialManager:
    def __init__(self, key_file, cred_file):
        self.key_file = key_file
        self.cred_file = cred_file
        self.key = self.load_key()
        self.fernet = Fernet(self.key)
        self.creds = self.load_creds()

    def load_key(self):
        if not os.path.exists(self.key_file):
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            return key
        with open(self.key_file, 'rb') as f:
            return f.read()

    def load_creds(self):
        if not os.path.exists(self.cred_file):
            return {}
        with open(self.cred_file, 'rb') as f:
            data = f.read()
            if not data:
                return {}
            try:
                return json.loads(self.fernet.decrypt(data).decode())
            except Exception:
                return {}

    def save_creds(self):
        with open(self.cred_file, 'wb') as f:
            f.write(self.fernet.encrypt(json.dumps(self.creds).encode()))

    def get_cred(self, ip):
        return self.creds.get(ip, {"user": "", "pass": ""})

    def set_cred(self, ip, user, passwd):
        self.creds[ip] = {"user": user, "pass": passwd}
        self.save_creds()


class SyncManager:
    def __init__(self, nas_ip, nas_share, nas_folder, nas_user, nas_pass, local_folder, log_dir, progress_callback=None):
        print(f"【SYNC】初始化: nas_ip={nas_ip}, nas_share=[{nas_share}], nas_folder=[{nas_folder}], user={nas_user}")
        self.nas_ip = nas_ip
        self.nas_share = nas_share
        self.nas_folder = nas_folder
        self.nas_user = nas_user
        self.nas_pass = nas_pass
        self.local_folder = local_folder
        self.log_dir = log_dir
        self.progress_callback = progress_callback
        self.nas_log_file = os.path.join(log_dir, f"NAS_{nas_ip.replace('.', '_')}.log")
        self.local_log_file = os.path.join(log_dir, f"本地_{os.path.basename(local_folder)}.log")
        self.nas_files = []
        self.local_files = []
        self.total_steps = 1
        self.current_step = 0

    def scan_local(self):
        file_list = []
        for root, dirs, files in os.walk(self.local_folder):
            for f in files:
                full_path = os.path.join(root, f)
                rel_path = os.path.relpath(full_path, self.local_folder)
                file_list.append({
                    "name": f,
                    "path": rel_path.replace("\\", "/"),
                    "size": os.path.getsize(full_path),
                    "type": "file"
                })
        self.local_files = file_list
        self.save_log(self.local_log_file, file_list)
        return file_list

    def smb_list_files(self, conn, share, path=""):
        file_list = []
        print(f"[SMB-递归] listPath: share={share}, path={path}")
        for f in conn.listPath(share, path):
            if f.filename in ('.', '..'):
                continue
            full_path = path + '/' + f.filename if path else f.filename
            if f.isDirectory:
                file_list += self.smb_list_files(conn, share, full_path)
            else:
                file_list.append({
                    "name": f.filename,
                    "path": full_path.lstrip('/'),
                    "size": f.file_size,
                    "type": "file"
                })
        return file_list

    def scan_smb(self):
        # 使用pysmb递归遍历NAS目录
        print(f"【SYNC】pysmb连接: //{self.nas_ip}/[{self.nas_share}]，子目录: [{self.nas_folder}]")
        file_list = []
        try:
            from smb.SMBConnection import SMBConnection
            conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
            conn.connect(self.nas_ip, 445)
            base_path = self.nas_folder.strip('/') if self.nas_folder else ''
            print(f"[SMB-递归] scan_smb调用smb_list_files, base_path={base_path}")
            file_list = self.smb_list_files(conn, self.nas_share, base_path)
            conn.close()
        except Exception as e:
            import traceback
            print(f"【SYNC】pysmb扫描失败: {e}")
            traceback.print_exc()
        self.nas_files = file_list
        self.save_log(self.nas_log_file, file_list)
        return file_list

    def save_log(self, log_path, data):
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def load_log(self, log_path):
        if not os.path.exists(log_path):
            return []
        with open(log_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def compare_logs(self):
        # 返回本地独有、NAS独有、都存在但大小不同
        nas_map = {f['path']: f for f in self.nas_files}
        local_map = {f['path']: f for f in self.local_files}
        only_nas = [f for p, f in nas_map.items() if p not in local_map]
        only_local = [f for p, f in local_map.items() if p not in nas_map]
        diff_size = [f for p, f in nas_map.items() if p in local_map and f['size'] != local_map[p]['size']]
        return only_nas, only_local, diff_size

    def sync(self, mode):
        # mode: 'NAS-本地', '本地-NAS', '双向同步(全保留)', '双向同步(NAS为准)', '双向同步(本地为准)'
        only_nas, only_local, diff_size = self.compare_logs()
        self.total_steps = len(only_nas) + len(only_local) + len(diff_size)
        self.current_step = 0
        def remote_path_with_folder(fpath):
            # 修复：防止重复目录和分隔符混乱，全部用/，且不重复nas_folder
            base = self.nas_folder.strip('/').replace('\\', '/').replace('\\', '/') if self.nas_folder else ''
            fpath = fpath.strip('/').replace('\\', '/').replace('\\', '/')
            # 如果fpath已经以base开头，避免重复
            if base and (fpath == base or fpath.startswith(base + '/')):
                remote_path = f'/{fpath}'
            elif base:
                remote_path = f'/{base}/{fpath}' if fpath else f'/{base}'
            else:
                remote_path = f'/{fpath}'
            # 再次合并多余的/
            while '//' in remote_path:
                remote_path = remote_path.replace('//', '/')
            return remote_path
        def get_relative_dirs(fpath):
            # 只递归创建nas_folder下的子目录
            dirs = fpath.strip('/').split('/')[:-1]
            return dirs
        if mode == 'NAS-本地(清空本地)':
            for f in self.local_files:
                try:
                    local_path = os.path.normpath(os.path.join(self.local_folder, f['path']))
                    if os.path.exists(local_path):
                        os.remove(local_path)
                        print(f"[DEBUG] 已删除本地文件: {local_path}")
                except Exception as e:
                    print(f"[DEBUG] 删除本地文件失败: {f['path']} {e}")
                self.update_progress(0)
            try:
                from smb.SMBConnection import SMBConnection
                print("[DEBUG] 尝试连接NAS进行NAS-本地(清空本地)同步")
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                for f in self.nas_files:
                    local_path = os.path.normpath(os.path.join(self.local_folder, f['path']))
                    parent_dir = os.path.dirname(local_path)
                    if not os.path.exists(parent_dir):
                        print(f"[ERROR] 目录不存在，跳过: {parent_dir}")
                        self.update_progress(0)
                        continue
                    try:
                        with open(local_path, 'wb') as fp:
                            print(f"[DEBUG] 正在下载: {f['path']} -> {local_path}")
                            try:
                                conn.retrieveFile(self.nas_share, remote_path_with_folder(f['path']), fp)
                                print(f"[DEBUG] 下载完成: {f['path']}")
                                self.update_progress(f['size'] if 'size' in f else 0)
                            except Exception as e:
                                print(f"[DEBUG] 下载失败: {f['path']} {e}")
                                self.update_progress(0)
                    except Exception as e:
                        print(f"[ERROR] 打开本地文件失败: {local_path} {e}")
                        self.update_progress(0)
                conn.close()
                print("[DEBUG] NAS-本地(清空本地)同步完成")
            except Exception as e:
                import traceback
                print(f"[DEBUG] NAS文件下载失败: {e}")
                traceback.print_exc()
        elif mode == 'NAS-本地(保留本地)':
            try:
                from smb.SMBConnection import SMBConnection
                print("[DEBUG] 尝试连接NAS进行NAS-本地(保留本地)同步")
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                for f in self.nas_files:
                    local_path = os.path.normpath(os.path.join(self.local_folder, f['path']))
                    parent_dir = os.path.dirname(local_path)
                    if not os.path.exists(parent_dir):
                        print(f"[ERROR] 目录不存在，跳过: {parent_dir}")
                        self.update_progress(0)
                        continue
                    try:
                        with open(local_path, 'wb') as fp:
                            print(f"[DEBUG] 正在下载: {f['path']} -> {local_path}")
                            try:
                                conn.retrieveFile(self.nas_share, remote_path_with_folder(f['path']), fp)
                                print(f"[DEBUG] 下载完成: {f['path']}")
                                self.update_progress(f['size'] if 'size' in f else 0)
                            except Exception as e:
                                print(f"[DEBUG] 下载失败: {f['path']} {e}")
                                self.update_progress(0)
                    except Exception as e:
                        print(f"[ERROR] 打开本地文件失败: {local_path} {e}")
                        self.update_progress(0)
                conn.close()
                print("[DEBUG] NAS-本地(保留本地)同步完成")
            except Exception as e:
                import traceback
                print(f"[DEBUG] NAS文件下载失败: {e}")
                traceback.print_exc()
        elif mode == '本地-NAS(清空NAS)':
            try:
                from smb.SMBConnection import SMBConnection
                print("[DEBUG] 尝试连接NAS进行本地-NAS(清空NAS)同步")
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                for f in self.nas_files:
                    try:
                        print(f"[DEBUG] 删除NAS文件: {f['path']}")
                        conn.deleteFiles(self.nas_share, remote_path_with_folder(f['path']))
                        self.update_progress(0)
                    except Exception as e:
                        print(f"[DEBUG] 删除NAS文件失败: {f['path']} {e}")
                        self.update_progress(0)
                for f in self.local_files:
                    try:
                        remote_path = remote_path_with_folder(f['path'])
                        local_file_path = os.path.normpath(os.path.join(self.local_folder, f['path']))
                        if not os.path.exists(local_file_path):
                            print(f"[ERROR] 本地文件不存在，跳过: {local_file_path}")
                            self.update_progress(0)
                            continue
                        with open(local_file_path, 'rb') as fp:
                            print(f"[DEBUG] 上传本地文件: {local_file_path} -> {remote_path}")
                            conn.storeFile(self.nas_share, remote_path, fp)
                        print(f"[DEBUG] 上传完成: {f['path']}")
                        self.update_progress(os.path.getsize(local_file_path))
                    except Exception as e:
                        print(f"[DEBUG] 上传失败: {f['path']} {e}")
                        self.update_progress(0)
                conn.close()
                print("[DEBUG] 本地-NAS(清空NAS)同步完成")
            except Exception as e:
                import traceback
                print(f"[DEBUG] 本地文件上传到NAS失败: {e}")
                traceback.print_exc()
        elif mode == '本地-NAS(保留NAS)':
            try:
                from smb.SMBConnection import SMBConnection
                print("[DEBUG] 尝试连接NAS进行本地-NAS(保留NAS)同步")
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                for f in self.local_files:
                    try:
                        remote_path = remote_path_with_folder(f['path'])
                        local_file_path = os.path.normpath(os.path.join(self.local_folder, f['path']))
                        if not os.path.exists(local_file_path):
                            print(f"[ERROR] 本地文件不存在，跳过: {local_file_path}")
                            self.update_progress(0)
                            continue
                        with open(local_file_path, 'rb') as fp:
                            print(f"[DEBUG] 上传本地文件: {local_file_path} -> {remote_path}")
                            conn.storeFile(self.nas_share, remote_path, fp)
                        print(f"[DEBUG] 上传完成: {f['path']}")
                        self.update_progress(os.path.getsize(local_file_path))
                    except Exception as e:
                        print(f"[DEBUG] 上传失败: {f['path']} {e}")
                        self.update_progress(0)
                conn.close()
                print("[DEBUG] 本地-NAS(保留NAS)同步完成")
            except Exception as e:
                import traceback
                print(f"[DEBUG] 本地文件上传到NAS失败: {e}")
                traceback.print_exc()
        elif mode == '双向同步(全保留)':
            try:
                from smb.SMBConnection import SMBConnection
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                # 本地独有和冲突文件都上传到NAS（覆盖）
                for f in only_local + diff_size:
                    try:
                        remote_path = remote_path_with_folder(f['path'])
                        local_file_path = os.path.normpath(os.path.join(self.local_folder, f['path']))
                        if not os.path.exists(local_file_path):
                            print(f"[ERROR] 本地文件不存在，跳过: {local_file_path}")
                            self.update_progress(0)
                            continue
                        with open(local_file_path, 'rb') as fp:
                            print(f"上传本地文件: {f['path']}")
                            conn.storeFile(self.nas_share, remote_path, fp)
                        self.update_progress(os.path.getsize(local_file_path))
                    except Exception as e:
                        print(f"上传失败: {f['path']} {e}")
                        self.update_progress(0)
                # NAS独有和冲突文件都下载到本地（覆盖）
                for f in only_nas + diff_size:
                    try:
                        # 路径处理修正版，增加调试
                        raw_fpath = f['path']
                        fpath = raw_fpath.replace('\\', '/').replace('\\', '/').lstrip('/')
                        nas_folder = self.nas_folder.strip('/') if self.nas_folder else ''
                        print(f"[DEBUG] 下载前路径处理: raw_fpath={raw_fpath}, nas_folder={nas_folder}, fpath初值={fpath}")
                        # 只在fpath以nas_folder或nas_folder/开头时去前缀
                        if nas_folder and (fpath == nas_folder or fpath.startswith(nas_folder + '/')):
                            fpath = fpath[len(nas_folder):].lstrip('/')
                            print(f"[DEBUG] 去前缀后fpath={fpath}")
                        local_path = os.path.normpath(os.path.join(self.local_folder, fpath))
                        parent_dir = os.path.dirname(local_path)
                        print(f"[DEBUG] 拼接本地路径: local_path={local_path}, parent_dir={parent_dir}")
                        if not os.path.exists(parent_dir):
                            print(f"[ERROR] 目录不存在，跳过: {parent_dir}")
                            self.update_progress(0)
                            continue
                        with open(local_path, 'wb') as fp:
                            print(f"下载NAS文件: {raw_fpath} -> {local_path}")
                            conn.retrieveFile(self.nas_share, remote_path_with_folder(raw_fpath), fp)
                        self.update_progress(f['size'] if 'size' in f else 0)
                    except Exception as e:
                        print(f"下载失败: {f['path']} {e}")
                        self.update_progress(0)
                conn.close()
            except Exception as e:
                import traceback
                print(f"双向同步(全保留)失败: {e}")
                traceback.print_exc()
        elif mode == '双向同步(NAS为准)':
            # 以NAS内容为准，全部下载到本地，本地原有信息不保留
            print("[双向同步(NAS为准)] 以NAS为准，执行NAS-本地同步逻辑")
            # self.sync('NAS-本地')
            pass
        elif mode == '双向同步(本地为准)':
            # 以本地内容为准，全部上传到NAS，NAS原有信息不保留
            print("[双向同步(本地为准)] 以本地为准，执行本地-NAS同步逻辑")
            # self.sync('本地-NAS')
            pass
        # ...
    def update_progress(self, file_size=0):
        self.current_step += 1
        if self.progress_callback:
            self.progress_callback(file_size)


class ScannerApp:
    def __init__(self, root):
        self.root = root
        # ====== 新增：设备表格列宽配置 ======
        self.column_widths = {
            "ip": 120,
            "brand": 80,
            "model": 90,
            "system": 70,
            "name": 150,
            "cpu": 220,
            "memory": 120,
            "firmware": 120,
            "status": 50,
            "port": 50,
            "last_seen": 180
        }
        # ====== 列宽配置结束 ======
        self.root.title("NAS统一管理工具")
        # 自动计算主窗口宽度
        total_width = sum(self.column_widths.values()) + 60  # 60为滚动条和边距冗余
        self.root.geometry(f"{total_width}x700")
        self.root.resizable(True, True)

        # 设置应用图标（可选）
        try:
            self.root.iconbitmap(r"D:\飞牛NAS设备记录日志\各品牌LOGO\favicon-ICO\飞牛-favicon.ico")  # 需准备图标文件
        except:
            pass

        # 创建标题栏
        header_frame = tk.Frame(root, bg="#87ceeb", height=60)
        header_frame.pack(fill=tk.X)

        # 创建图标和标题
        # 加载三个ico图片并拼接
        icon_paths = [
            r"D:\飞牛NAS设备记录日志\各品牌LOGO\favicon-ICO\qnap-favicon.png",               # 威联通
            r"D:\飞牛NAS设备记录日志\各品牌LOGO\favicon-ICO\飞牛-favicon.ico",              # 飞牛
            r"D:\飞牛NAS设备记录日志\各品牌LOGO\favicon-ICO\terra-master-favicon_48.ico"   # 铁威马
        ]
        icons = [Image.open(p).resize((32, 32)) for p in icon_paths]
        total_width = sum(i.width for i in icons)
        max_height = max(i.height for i in icons)
        combo_img = Image.new('RGBA', (total_width, max_height), (0,0,0,0))
        x = 0
        for img in icons:
            combo_img.paste(img, (x, 0), img if img.mode=='RGBA' else None)
            x += img.width
        self.header_icons = ImageTk.PhotoImage(combo_img)
        icon_label = tk.Label(header_frame, image=self.header_icons, bg="#87ceeb")
        icon_label.pack(side=tk.LEFT, padx=10)

        title_label = tk.Label(header_frame, text="NAS统一管理工具V1.0",
                               font=("微软雅黑", 14, "bold"), bg="#87ceeb", fg="white")
        title_label.pack(side=tk.LEFT, pady=15)

        # 添加说明标签到header_frame右侧
        info_label = tk.Label(
            header_frame,
            text="双击IP打开本设备管理页面",
            font=("微软雅黑", 14, "bold"),  # 与标题一致
            fg="white",
            bg="#87ceeb"
        )
        info_label.pack(side=tk.RIGHT, padx=20)

        # 创建控制面板
        control_frame = tk.Frame(root, padx=10, pady=10)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        # 添加刷新按钮（最左侧）
        refresh_button = tk.Button(
            control_frame,
            text="刷新设备列表",
            command=self.start_scan,  # 直接调用start_scan实现重新扫描
            bg="#3498db",
            fg="white",
            font=("微软雅黑", 10),
            padx=20,
            pady=5,
            width=12
        )
        refresh_button.pack(side=tk.LEFT, padx=5)

        # ====== 新增：NAS预读取进度条（红色框区域） ======
        self.nas_preload_frame = tk.Frame(control_frame, bg="#f5f5f5", height=44)
        self.nas_preload_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=8, pady=0)
        # 第一行：设备名+进度条+百分比
        self.nas_preload_row = tk.Frame(self.nas_preload_frame, bg="#f5f5f5")
        self.nas_preload_row.pack(side=tk.TOP, fill=tk.X)
        self.nas_preload_device_label = tk.Label(self.nas_preload_row, text="", bg="#f5f5f5", font=("微软雅黑", 10, "bold"), width=10, anchor='w', fg="#2c3e50")
        self.nas_preload_device_label.pack(side=tk.LEFT, padx=(6, 0), pady=4)
        self.nas_preload_progress = tk.DoubleVar(value=0)
        self.nas_preload_bar = ttk.Progressbar(self.nas_preload_row, orient="horizontal", mode="determinate", variable=self.nas_preload_progress, maximum=100)
        self.nas_preload_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=4, pady=4)
        self.nas_preload_percent_label = tk.Label(self.nas_preload_row, text="0%", bg="#f5f5f5", font=("微软雅黑", 10, "bold"), width=5, anchor='w', fg="#2c3e50")
        self.nas_preload_percent_label.pack(side=tk.LEFT, padx=(4, 6), pady=4)
        # 第二行：进度说明（只显示设备总数）
        self.nas_preload_label = tk.Label(self.nas_preload_frame, text="NAS文件夹预读取进度：0", bg="#f5f5f5", font=("微软雅黑", 10))
        self.nas_preload_label.pack(side=tk.TOP, anchor='w', padx=10, pady=(0, 4))
        # 新增：预读取状态Label
        self.nas_preload_status_label = tk.Label(
            self.nas_preload_frame, text="预读取进行中", bg="#f5f5f5", font=("微软雅黑", 10, "bold"), fg="#e67e22"
        )
        self.nas_preload_status_label.pack(side=tk.TOP, anchor='w', padx=10, pady=(0, 4))
        # ====== 新增结束 ======

        # 新增：打开全部飞牛管理页按钮
        self.open_feiniu_button = tk.Button(
            control_frame,
            text="打开全部飞牛管理页",
            command=self.open_all_feiniu_web,
            bg="#16a085",
            fg="white",
            font=("微软雅黑", 10),
            padx=20,
            pady=5,
            width=16
        )
        # 新增：打开全部威联通管理页按钮
        self.open_qnap_button = tk.Button(
            control_frame,
            text="打开全部威联通管理页",
            command=self.open_all_qnap_web,
            bg="#2980b9",
            fg="white",
            font=("微软雅黑", 10),
            padx=20,
            pady=5,
            width=16
        )
        # 新增：打开全部铁威马管理页按钮
        self.open_terra_button = tk.Button(
            control_frame,
            text="打开全部铁威马管理页",
            command=self.open_all_terra_web,
            bg="#8e44ad",
            fg="white",
            font=("微软雅黑", 10),
            padx=20,
            pady=5,
            width=16
        )
        # 新增：打开全部设备管理页按钮
        self.open_all_button = tk.Button(
            control_frame,
            text="打开全部设备管理页",
            command=self.open_all_devices_web,
            bg="#27ae60",
            fg="white",
            font=("微软雅黑", 10),
            padx=20,
            pady=5,
            width=16
        )
        # 先pack最右侧的"打开全部设备管理页"
        self.open_all_button.pack(side=tk.RIGHT, padx=5)
        # 再pack其左侧的三个按钮，顺序为飞牛、威联通、铁威马
        self.open_terra_button.pack(side=tk.RIGHT, padx=5)
        self.open_qnap_button.pack(side=tk.RIGHT, padx=5)
        self.open_feiniu_button.pack(side=tk.RIGHT, padx=5)

        # 创建设备表格
        table_frame = tk.Frame(root)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # 添加表头
        columns = ("ip", "brand", "model", "system", "name", "cpu", "memory", "firmware", "status", "port", "last_seen")
        column_names = {
            "ip": "IP地址",
            "brand": "品牌",
            "model": "型号",
            "system": "系统",
            "name": "设备名称",
            "cpu": "CPU",
            "memory": "内存",
            "firmware": "固件版本",
            "status": "状态",
            "port": "端口",
            "last_seen": "最后检测时间"
        }

        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", selectmode="browse")

        # 配置列标题和宽度（锁死）
        for col in columns:
            self.tree.heading(col, text=column_names[col], command=lambda c=col: self.on_column_click(c))  # 新增command绑定
            self.tree.column(col, width=self.column_widths.get(col, 120), anchor=tk.CENTER, stretch=False)
        # 让所有列都自适应拉伸
        for col in columns:
            self.tree.column(col, stretch=True)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.pack(fill=tk.BOTH, expand=True)

        # 配置表格样式
        style = ttk.Style()
        style.configure("Treeview",
                        font=("微软雅黑", 10),
                        rowheight=25,
                        borderwidth=0)
        style.configure("Treeview.Heading",
                        font=("微软雅黑", 10, "bold"),
                        background="#ecf0f1")

        # 绑定双击事件
        self.tree.bind("<Double-1>", self.on_double_click)
        # 新增：绑定右键菜单
        self.tree.bind("<Button-3>", self.on_right_click)

        # 创建状态栏
        self.status_frame = tk.Frame(root, height=25, bg="#ecf0f1")
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # 新增：进度条控件
        self.progressbar = ttk.Progressbar(self.status_frame, orient="horizontal", length=120, mode="determinate", maximum=100)
        self.progressbar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 5), pady=2)

        self.status_var = tk.StringVar()
        self.status_var.set("就绪 | 点击'开始扫描'搜索网络中的飞牛NAS设备")
        status_label = tk.Label(self.status_frame, textvariable=self.status_var,
                                bd=0, relief=tk.SUNKEN, anchor=tk.W, padx=10,
                                bg="#ecf0f1", fg="#2c3e50", font=("微软雅黑", 9))
        status_label.pack(side=tk.RIGHT, padx=(5, 10))

        # 创建扫描器
        self.scanner = FeiNiuScanner()
        # 新增：初始化凭证管理器，防止self.cred_mgr不存在报错
        self.cred_mgr = CredentialManager(
            key_file=os.path.join(self.scanner.log_path, 'cred.key'),
            cred_file=os.path.join(self.scanner.log_path, 'cred.json')
        )
        self.device_count = 0
        self.scan_progress = 0
        self.total_ips = 0
        self.scanned_ips = 0

        # 在设备表格下方添加同步功能栏
        self.sync_frame = tk.Frame(root, padx=10, pady=10, bg="#f5f5f5")
        self.sync_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        # --- 同步区账号密码行（原sync_row1）---
        self.sync_row1 = tk.Frame(self.sync_frame, bg="#f5f5f5")
        self.sync_row1.pack(fill=tk.X, pady=2)
        tk.Label(self.sync_row1, text="设备IP:", font=("微软雅黑", 9), bg="#f5f5f5").grid(row=0, column=0, padx=8, sticky='w')
        self.device_combo = ttk.Combobox(self.sync_row1, values=[], state="readonly", width=16)
        self.device_combo.grid(row=0, column=1, padx=8, sticky='w')
        self.device_combo.bind("<<ComboboxSelected>>", self.on_device_selected)
        tk.Label(self.sync_row1, text="用户名:", font=("微软雅黑", 9), bg="#f5f5f5").grid(row=0, column=2, padx=8, sticky='w')
        self.nas_user_entry = tk.Entry(self.sync_row1, width=12)
        self.nas_user_entry.grid(row=0, column=3, padx=8, sticky='w')
        tk.Label(self.sync_row1, text="密码:", font=("微软雅黑", 9), bg="#f5f5f5").grid(row=0, column=4, padx=8, sticky='w')
        self.nas_pass_entry = tk.Entry(self.sync_row1, width=12, show="*")
        self.nas_pass_entry.grid(row=0, column=5, padx=8, sticky='w')
        self.show_pass_var = tk.BooleanVar()
        tk.Checkbutton(self.sync_row1, text="显示密码", variable=self.show_pass_var, command=self.toggle_password, bg="#f5f5f5").grid(row=0, column=6, padx=8, sticky='w')
        # 新增：保存账号按钮
        self.save_account_btn = tk.Button(self.sync_row1, text="保存账号", command=self.save_account_and_preload, bg="#f5b041", font=("微软雅黑", 9))
        self.save_account_btn.grid(row=0, column=7, padx=8, sticky='w')
        # --- 多行任务区替换原sync_row2和sync_row3 ---
        self.sync_task_rows_frame = tk.Frame(self.sync_frame, bg="#f5f5f5")
        self.sync_task_rows_frame.pack(fill=tk.X, padx=0, pady=0)
        self.sync_tasks = []
        self.create_sync_task_row()  # 初始一行
        # 全部任务同步方式按钮
        self.sync_mode_menu = tk.Menu(self.sync_frame, tearoff=0)
        self.sync_modes = [
            ('NAS-本地(清空本地)', '清空本地后从NAS下载全部文件到本地'),
            ('NAS-本地(保留本地)', '保留本地后从NAS下载全部文件到本地'),
            ('本地-NAS(清空NAS)', '清空NAS后将本地所有文件上传到NAS'),
            ('本地-NAS(保留NAS)', '保留NAS后将本地所有文件上传到NAS'),
            ('双向同步(全保留)', '本地独有上传，NAS独有下载，冲突文件覆盖')
        ]
        # 同步按钮
        # self.sync_btn = tk.Button(self.sync_frame, text="同步", command=self.start_sync)
        # self.sync_btn.pack(side=tk.RIGHT, padx=8, pady=4)

        # 加载CPUMAP标准json，保留原始key
        self.cpu_map = {}
        cpumap_path = r'D:\飞牛NAS设备记录日志\数据库\cpumap标准json格式.txt'
        if os.path.exists(cpumap_path):
            try:
                with open(cpumap_path, 'r', encoding='utf-8') as f:
                    cpumap_json = json.load(f)
                for brand_group, cpu_dict in cpumap_json.items():
                    for alias, std_name in cpu_dict.items():
                        self.cpu_map[alias] = std_name
            except Exception as e:
                print(f"[CPUMAP] 加载失败: {e}")
        else:
            print(f"[CPUMAP] 未找到: {cpumap_path}")
        # 加载NAS型号信息文档
        self.nas_model_info = []
        nas_model_path = r'D:\飞牛NAS设备记录日志\数据库\NAS型号信息文档.txt'
        if os.path.exists(nas_model_path):
            try:
                with open(nas_model_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            self.nas_model_info.append(json.loads(line))
            except Exception as e:
                print(f"[NAS型号信息] 加载失败: {e}")
        else:
            print(f"[NAS型号信息] 未找到: {nas_model_path}")

        # 初始化品牌固件版本日志
        self.firmware_log_feiniu_path = os.path.join(self.scanner.log_path, '飞牛固件版本.json')
        self.firmware_log_qnap_path = os.path.join(self.scanner.log_path, '威联通固件版本.json')
        self.firmware_log_terra_path = os.path.join(self.scanner.log_path, '铁威马固件版本.json')
        self.firmware_log_feiniu = self.load_firmware_log(self.firmware_log_feiniu_path)
        self.firmware_log_qnap = self.load_firmware_log(self.firmware_log_qnap_path)
        self.firmware_log_terra = self.load_firmware_log(self.firmware_log_terra_path)
        # 程序启动时强制生成固件日志文件（如无则创建空文件）
        self.save_firmware_log(self.firmware_log_feiniu_path, self.firmware_log_feiniu)
        self.save_firmware_log(self.firmware_log_qnap_path, self.firmware_log_qnap)
        self.save_firmware_log(self.firmware_log_terra_path, self.firmware_log_terra)
        # 初始化品牌内存日志
        self.memory_log_feiniu_path = os.path.join(self.scanner.log_path, '飞牛内存.json')
        self.memory_log_qnap_path = os.path.join(self.scanner.log_path, '威联通内存.json')
        self.memory_log_terra_path = os.path.join(self.scanner.log_path, '铁威马内存.json')
        self.memory_log_feiniu = self.load_memory_log(self.memory_log_feiniu_path)
        self.memory_log_qnap = self.load_memory_log(self.memory_log_qnap_path)
        self.memory_log_terra = self.load_memory_log(self.memory_log_terra_path)
        # 程序启动时强制生成日志文件（如无则创建空文件）
        self.save_memory_log(self.memory_log_feiniu_path, self.memory_log_feiniu)
        self.save_memory_log(self.memory_log_qnap_path, self.memory_log_qnap)
        self.save_memory_log(self.memory_log_terra_path, self.memory_log_terra)

        # 弹窗默认宽高配置
        self.popup_default_width = 260  # 可根据需要调整
        self.popup_default_height = 280

        # 自动启动扫描
        self.root.after(100, self.start_scan)

        # --- 全部任务进度条与控制按钮区 ---
        self.all_task_control_frame = tk.Frame(self.sync_frame, bg="#f5f5f5", height=44)
        self.all_task_control_frame.pack(fill=tk.X, pady=0)
        # 全部任务进度条（自适应填充本行剩余宽度）
        self.all_task_progress_var = tk.DoubleVar(value=0)
        self.all_task_progressbar = ttk.Progressbar(self.all_task_control_frame, orient="horizontal", mode="determinate", maximum=100, variable=self.all_task_progress_var)
        self.all_task_progressbar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 12), pady=8)
        # 新增：同步状态Label
        self.all_task_status_label = tk.Label(self.all_task_control_frame, text="未开始", font=("微软雅黑", 10, "bold"), bg="#f5f5f5", fg="#e67e22", width=8, anchor='w')
        self.all_task_status_label.pack(side=tk.LEFT, padx=(8, 4))
        # 右侧按钮组整体靠右
        right_btns_frame = tk.Frame(self.all_task_control_frame, bg="#f5f5f5")
        right_btns_frame.pack(side=tk.RIGHT, fill=tk.Y)
        # 剩余数比值、速率、剩余时间（直接在按钮组最左侧创建和pack）
        self.all_task_info_var = tk.StringVar(value="0/0 0.00MB/s 00:00:00")
        self.all_task_info_label = tk.Label(right_btns_frame, textvariable=self.all_task_info_var, font=("微软雅黑", 10), bg="#f5f5f5")
        self.all_task_info_label.pack(side=tk.LEFT, padx=(8, 12))
        # 全部任务同步方式按钮
        self.all_sync_mode_btn = tk.Button(right_btns_frame, text="全部任务同步方式", command=self.show_all_sync_mode_menu, font=("微软雅黑", 10))
        self.all_sync_mode_btn.pack(side=tk.LEFT, padx=8, pady=6)
        # 修复：初始化全部任务同步方式菜单
        self.all_sync_mode_var = tk.StringVar(value="")
        self.all_sync_mode_menu = tk.Menu(self.sync_frame, tearoff=0)
        for mode, desc in self.sync_modes:
            self.all_sync_mode_menu.add_command(
                label=f"{mode}    {desc}",
                command=lambda m=mode: self.set_all_sync_mode(m)
            )
        # 开始同步按钮
        self.sync_btn = tk.Button(right_btns_frame, text="开始同步", command=self.start_sync, font=("微软雅黑", 10))
        self.sync_btn.pack(side=tk.LEFT, padx=8, pady=6)
        # 退出程序按钮（最右）
        self.exit_btn = tk.Button(right_btns_frame, text="退出程序", command=self.root.quit, font=("微软雅黑", 10))
        self.exit_btn.pack(side=tk.LEFT, padx=8, pady=6)
        # 注释：全部任务进度条，剩余数比值 实时速率 剩余时间，全部任务同步方式按键，开始同步按键，以及退出程序按键。

        # 初始化品牌设备名称日志
        self.name_log_feiniu_path = os.path.join(self.scanner.log_path, '飞牛设备名称.json')
        self.name_log_qnap_path = os.path.join(self.scanner.log_path, '威联通设备名称.json')
        self.name_log_terra_path = os.path.join(self.scanner.log_path, '铁威马设备名称.json')
        self.name_log_feiniu = self.load_name_log(self.name_log_feiniu_path)
        self.name_log_qnap = self.load_name_log(self.name_log_qnap_path)
        self.name_log_terra = self.load_name_log(self.name_log_terra_path)
        self.save_name_log(self.name_log_feiniu_path, self.name_log_feiniu)
        self.save_name_log(self.name_log_qnap_path, self.name_log_qnap)
        self.save_name_log(self.name_log_terra_path, self.name_log_terra)

        # ====== 新增：排序相关状态 ======
        self.sort_col = None  # 当前排序列
        self.sort_reverse = False  # 当前排序顺序
        # ====== 新增结束 ======
        # ====== 新增：排序用JSON加载 ======
        self.load_sort_json()
        # ====== 新增结束 ======

        # 启动NAS文件夹预读取
        print("[DEBUG] ScannerApp: 启动NAS文件夹预读取")
        self.nas_folder_preloader = NASFolderPreloader(self.scanner, self.cred_mgr, lambda: self.scanner.devices, self.update_nas_preload_progress)
        self.nas_folder_preloader.preload_all()

    def create_sync_task_row(self, nas_path='', local_path='', sync_mode=''):
        row = {}
        frame = tk.Frame(self.sync_task_rows_frame, bg="#f5f5f5")
        frame.pack(fill=tk.X, pady=2)
        # NAS路径
        tk.Label(frame, text="NAS路径:", font=("微软雅黑", 9), bg="#f5f5f5").pack(side=tk.LEFT, padx=4)
        nas_var = tk.StringVar(value=nas_path)
        nas_entry = tk.Entry(frame, textvariable=nas_var, state='readonly')
        nas_entry.pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)
        nas_btn = tk.Button(frame, text="选择NAS目录", command=lambda:self.choose_nas_folder_row(row))
        nas_btn.pack(side=tk.LEFT, padx=2)
        # 本地路径
        tk.Label(frame, text="本地路径:", font=("微软雅黑", 9), bg="#f5f5f5").pack(side=tk.LEFT, padx=4)
        local_var = tk.StringVar(value=local_path)
        local_entry = tk.Entry(frame, textvariable=local_var, state='readonly')
        local_entry.pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)
        local_btn = tk.Button(frame, text="选择本地目录", command=lambda:self.choose_local_folder_row(row))
        local_btn.pack(side=tk.LEFT, padx=2)
        # 同步方式
        sync_mode_var = tk.StringVar(value=sync_mode)
        sync_mode_btn = tk.Button(frame, text="同步方式", command=lambda:self.show_sync_mode_menu_row(row))
        sync_mode_btn.pack(side=tk.LEFT, padx=4)
        # + 或 - 按钮
        if len(self.sync_tasks) == 0 or self.sync_tasks[-1] is row:
            add_btn = tk.Button(frame, text="+", width=2, command=self.add_sync_task_row)
        else:
            add_btn = tk.Button(frame, text="-", width=2, command=lambda:self.remove_sync_task_row(row))
        add_btn.pack(side=tk.LEFT, padx=4)
        # 存储控件和变量
        row.update({
            'frame': frame,
            'nas_var': nas_var,
            'nas_entry': nas_entry,
            'nas_btn': nas_btn,
            'local_var': local_var,
            'local_entry': local_entry,
            'local_btn': local_btn,
            'sync_mode_var': sync_mode_var,
            'sync_mode_btn': sync_mode_btn,
            'add_btn': add_btn
        })
        self.sync_tasks.append(row)
        self.refresh_sync_task_buttons()
        # 新增：每加一行，主窗口高度增加一行高度
        self.adjust_window_height(inc=1)

    def add_sync_task_row(self):
        self.create_sync_task_row()

    def remove_sync_task_row(self, row):
        row['frame'].destroy()
        self.sync_tasks.remove(row)
        self.refresh_sync_task_buttons()
        # 新增：每减一行，主窗口高度减少一行高度
        self.adjust_window_height(inc=-1)

    def adjust_window_height(self, inc=1):
        # 每行高度（可根据实际UI调整）
        row_height = 40
        w = self.root.winfo_width()
        h = self.root.winfo_height()
        # 只有在窗口已显示时才调整
        if w > 1 and h > 1:
            new_h = max(400, h + inc * row_height)  # 最小高度400
            self.root.geometry(f"{w}x{new_h}")

    def refresh_sync_task_buttons(self):
        for i, row in enumerate(self.sync_tasks):
            if i == len(self.sync_tasks) - 1:
                row['add_btn'].config(text='+', command=self.add_sync_task_row)
            else:
                row['add_btn'].config(text='-', command=lambda r=row: self.remove_sync_task_row(r))

    def choose_nas_folder_row(self, row):
        import tkinter as tk
        from tkinter import ttk
        import json
        import os
        ip = self.device_combo.get()
        device_name = ''
        # 从设备列表查找设备名
        for dev in self.scanner.devices:
            if dev.get('ip') == ip:
                device_name = dev.get('name', '')
                break
        safe_name = ''.join(c for c in device_name if c.isalnum() or c in '-_').strip() or ip
        log_dir = r'D:\飞牛NAS设备记录日志\日志\NAS文件夹预读取'
        json_path = os.path.join(log_dir, f'{safe_name}.json')
        if not os.path.exists(json_path):
            tk.messagebox.showerror("错误", "请先在下拉菜单中选择设备IP")
            return
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            tk.messagebox.showerror("错误", f"读取日志失败: {e}")
            return
        share_list = data.get('共享列表', [])
        if not share_list:
            tk.messagebox.showerror("错误", "日志中无共享信息，请确认已成功预读取！")
            return
        # 弹窗窗口
        win = tk.Toplevel(self.root)
        win.title("选择NAS目录（预读取日志）")
        width, height = 420, 320
        win.geometry(f"{width}x{height}")
        win.transient(self.root)
        win.grab_set()
        # 右侧对齐主窗口
        def place_popup():
            if not win.winfo_exists():
                return
            self.root.update_idletasks()
            root_x = self.root.winfo_x()
            root_y = self.root.winfo_y()
            root_w = self.root.winfo_width()
            win.update_idletasks()
            win_h = win.winfo_height()
            x = root_x + root_w
            y = root_y
            win.geometry(f"{width}x{win_h}+{x}+{y}")
        place_popup()
        def on_root_configure(event):
            place_popup()
        bind_id = self.root.bind('<Configure>', on_root_configure)
        def on_close():
            try:
                self.root.unbind('<Configure>', bind_id)
            except Exception:
                pass
            win.grab_release()
            win.destroy()
        win.protocol("WM_DELETE_WINDOW", on_close)
        tk.Label(win, text="请选择NAS共享名和子文件夹（数据来源：本地日志）", font=("微软雅黑", 10)).pack(pady=10, anchor='w')
        combo_frame = tk.Frame(win)
        combo_frame.pack(side=tk.TOP, fill=tk.X, expand=True)
        btn_frame = tk.Frame(win)
        btn_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=18)
        combo_vars = []
        combo_boxes = []
        # 递归生成目录链
        def add_combo(level, options):
            frame = tk.Frame(combo_frame)
            frame.pack(fill=tk.X, pady=4)
            var = tk.StringVar()
            combo = ttk.Combobox(frame, values=options, textvariable=var, state="readonly", width=32)
            combo.pack(side=tk.LEFT, padx=10)
            combo_vars.append(var)
            combo_boxes.append(combo)
            def on_select(event=None):
                idx = combo_boxes.index(combo)
                while len(combo_boxes) > idx+1:
                    combo_boxes[-1].master.destroy()
                    combo_boxes.pop()
                    combo_vars.pop()
                selected = var.get()
                if not selected:
                    return
                # 第一层：共享名
                if idx == 0:
                    # 找到该共享的目录树
                    dirs = []
                    for s in share_list:
                        if s.get('共享名') == selected:
                            dirs = s.get('目录', [])
                            break
                    folder_names = [d['名称'] for d in dirs]
                    if folder_names:
                        add_combo(idx+1, folder_names)
                else:
                    # 递归到子目录
                    share = combo_vars[0].get()
                    dirs = share_list
                    # 先找到共享
                    for s in dirs:
                        if s.get('共享名') == share:
                            dirs = s.get('目录', [])
                            break
                    # 逐级深入
                    for i in range(1, idx+1):
                        folder = combo_vars[i].get()
                        found = None
                        for d in dirs:
                            if d['名称'] == folder:
                                found = d
                                break
                        if found:
                            dirs = found.get('子目录', [])
                        else:
                            dirs = []
                            break
                    folder_names = [d['名称'] for d in dirs]
                    if folder_names:
                        add_combo(idx+1, folder_names)
            combo.bind("<<ComboboxSelected>>", on_select)
        # 第一层：共享名
        share_names = [s.get('共享名') for s in share_list]
        add_combo(0, share_names)
        def on_ok():
            if not combo_vars or not combo_vars[0].get():
                tk.messagebox.showerror("错误", "请选择NAS共享名！")
                return
            share = combo_vars[0].get()
            subfolders = [v.get() for v in combo_vars[1:] if v.get()]
            if subfolders:
                path = share + '/' + '/'.join(subfolders)
            else:
                path = share
            row['nas_var'].set(path)
            try:
                self.root.unbind('<Configure>', bind_id)
            except Exception:
                pass
            win.grab_release()
            win.destroy()
        def on_cancel():
            try:
                self.root.unbind('<Configure>', bind_id)
            except Exception:
                pass
            win.grab_release()
            win.destroy()
        tk.Button(btn_frame, text="确定", command=on_ok, width=12).pack(side=tk.LEFT, padx=12)
        tk.Button(btn_frame, text="取消", command=on_cancel, width=12).pack(side=tk.LEFT, padx=12)

    def choose_local_folder_row(self, row):
        path = filedialog.askdirectory(title="请选择本地目录")
        if path:
            row['local_var'].set(path)

    def show_sync_mode_menu_row(self, row):
        menu = tk.Menu(self.sync_frame, tearoff=0)
        for mode, desc in self.sync_modes:
            menu.add_command(label=f"{mode}    {desc}", command=lambda m=mode: self.set_sync_mode_row(row, m))
        menu.post(row['sync_mode_btn'].winfo_rootx(), row['sync_mode_btn'].winfo_rooty() + row['sync_mode_btn'].winfo_height())

    def set_sync_mode_row(self, row, mode):
        row['sync_mode_var'].set(mode)
        row['sync_mode_btn'].config(text=mode)

    def show_all_sync_mode_menu(self):
        self.all_sync_mode_menu.post(self.all_sync_mode_btn.winfo_rootx(), self.all_sync_mode_btn.winfo_rooty() + self.all_sync_mode_btn.winfo_height())

    def set_all_sync_mode(self, mode):
        # 检查是否有任务已单独选择同步方式
        has_row_mode = any(row['sync_mode_var'].get().strip() for row in self.sync_tasks)
        if has_row_mode:
            if not messagebox.askokcancel("同步方式确认", "已存在单独选择的同步方式，继续将覆盖所有任务的同步方式，是否继续？"):
                self.all_sync_mode_menu.unpost()
                return
        self.all_sync_mode_var.set(mode)
        self.all_sync_mode_btn.config(text=f"全部任务同步方式: {mode}")
        # 覆盖所有任务的同步方式
        for row in self.sync_tasks:
            row['sync_mode_var'].set(mode)
            row['sync_mode_btn'].config(text=mode)
        self.all_sync_mode_menu.unpost()

    def update_all_task_progress(self, finished, total, bytes_done, start_time):
        # 更新总任务进度条和统计信息
        percent = int((finished / total) * 100) if total else 0
        self.all_task_progress_var.set(percent)
        # 速度
        elapsed = max(1, int(time.time() - start_time))
        speed = bytes_done / 1024 / 1024 / elapsed  # MB/s
        # 剩余时间
        remain = total - finished
        speed_file = finished / elapsed if elapsed > 0 else 0.0001
        remain_time = int(remain / speed_file) if speed_file > 0 else 0
        h, m, s = remain_time // 3600, (remain_time % 3600) // 60, remain_time % 60
        self.all_task_info_var.set(f"{finished}/{total} {speed:.2f}MB/s {h:02d}:{m:02d}:{s:02d}")

    def start_sync(self):
        import time
        tasks = []
        for row in self.sync_tasks:
            nas_path = row['nas_var'].get().strip()
            local_path = row['local_var'].get().strip()
            sync_mode = row['sync_mode_var'].get().strip()
            if not nas_path and not local_path:
                continue
            tasks.append({'nas_path': nas_path, 'local_path': local_path, 'sync_mode': sync_mode})
        all_mode = self.all_sync_mode_var.get().strip()
        has_row_mode = any(t['sync_mode'] for t in tasks)
        has_no_row_mode = any(not t['sync_mode'] for t in tasks)
        if all_mode and has_row_mode and has_no_row_mode:
            if not messagebox.askokcancel("同步方式确认", "部分任务已单独选择同步方式，是否只对未选择的任务应用'全部任务同步方式'?\n点击确定：未选的用全部任务同步方式，已选的用各自方式。\n点击取消：只用各自选择的方式，不用全部任务同步方式。"):
                all_mode = ''
        for t in tasks:
            if not t['sync_mode'] and all_mode:
                t['sync_mode'] = all_mode
        # 新增：同步开始时状态Label设为进行中
        self.all_task_status_label.config(text="进行中", fg="#e67e22")
        def run_sync():
            nas_ip = self.device_combo.get().strip()
            nas_user = self.nas_user_entry.get().strip()
            nas_pass = self.nas_pass_entry.get().strip()
            if not nas_ip or not nas_user or not nas_pass:
                messagebox.showerror("错误", "请填写NAS的IP、用户名和密码！")
                return
            total_files = 0
            for t in tasks:
                nas_path = t['nas_path'].strip().lstrip('/\\')
                local_folder = t['local_path']
                sync_mode = t['sync_mode']
                if not nas_path or not local_folder or not sync_mode:
                    continue
                parts = nas_path.split('/')
                nas_share = parts[0] if parts else ''
                nas_subfolder = '/'.join(parts[1:]) if len(parts) > 1 else ''
                sync_mgr = SyncManager(nas_ip, nas_share, nas_subfolder, nas_user, nas_pass, local_folder, self.scanner.log_path, None)
                sync_mgr.scan_local()
                sync_mgr.scan_smb()
                total_files += len(sync_mgr.nas_files) + len(sync_mgr.local_files)
            # 进度统计
            finished = 0
            bytes_done = 0
            start_time = time.time()
            def progress_callback(file_size):
                nonlocal finished, bytes_done
                finished += 1
                bytes_done += file_size
                self.root.after(0, self.update_all_task_progress, finished, total_files, bytes_done, start_time)
            # 重新同步，带进度回调
            for t in tasks:
                nas_path = t['nas_path'].strip().lstrip('/\\')
                local_folder = t['local_path']
                sync_mode = t['sync_mode']
                if not nas_path or not local_folder or not sync_mode:
                    continue
                parts = nas_path.split('/')
                nas_share = parts[0] if parts else ''
                nas_subfolder = '/'.join(parts[1:]) if len(parts) > 1 else ''
                sync_mgr = SyncManager(nas_ip, nas_share, nas_subfolder, nas_user, nas_pass, local_folder, self.scanner.log_path, progress_callback)
                sync_mgr.scan_local()
                sync_mgr.scan_smb()
                sync_mgr.sync(sync_mode)
            # 新增：同步全部完成后，状态Label设为完成（绿色）
            self.root.after(0, lambda: self.all_task_status_label.config(text="完成", fg="#27ae60"))
            print("全部同步任务完成")
        threading.Thread(target=run_sync, daemon=True).start()

    def start_scan(self):
        try:
            print("【DEBUG】start_scan 被调用", flush=True)
            self.status_var.set("扫描中... 0% | 正在搜索网络中的飞牛NAS设备")
            self.tree.delete(*self.tree.get_children())
            self.device_count = 0
            self.scan_progress = 0
            self.total_ips = 0
            self.scanned_ips = 0

            # 固定线程数和端口
            self.scanner.thread_count = 200
            self.scanner.ports_to_scan = [8000, 8181, 5000, 8080]
            print(f"【DEBUG】固定线程数: {self.scanner.thread_count}, 端口: {self.scanner.ports_to_scan}")
            self.scanner.scanning = True

            # 启动扫描线程，传递进度回调和完成回调
            threading.Thread(
                target=self.scanner.scan_network,
                args=(self.update_device_list, self.update_scan_progress_real, self.on_scan_finished),
                daemon=True
            ).start()
        except Exception as e:
            print(f"【ERROR】start_scan异常: {e}", flush=True)

    def update_scan_progress_real(self, scanned, total):
        percent = int((scanned / total) * 100) if total else 0
        self.scan_progress = percent
        self.progressbar['value'] = percent
        # 只显示表格设备数量
        self.status_var.set(f"扫描中... {self.scan_progress}% | 已发现 {self.device_count} 台设备")
        # 扫描完成后自动停止扫描
        if scanned >= total and self.scanner.scanning:
            self.scanner.scanning = False
            self.status_var.set(f"扫描完成 | 共找到 {self.device_count} 台设备 | 100%")
            self.progressbar['value'] = 100

    def update_device_list(self, devices):
        # 自动补全设备名称字段，确保dev['name']为设备名称列内容
        self.patch_device_fields()  # 修复品牌和系统排序无效问题
        for dev in devices:
            # 如果name为空，尝试从其它字段补全
            if not dev.get('name'):
                # 尝试从日志或其它字段获取设备名称
                if '设备名称' in dev:
                    dev['name'] = dev['设备名称']
                elif 'model' in dev:
                    dev['name'] = dev['model']
                else:
                    dev['name'] = ''
            print(f"[DEBUG] 设备名称补全: ip={dev.get('ip')}, name={dev.get('name')}")
        """更新设备列表"""
        # 每次都重新加载日志
        self.scanner.device_log_feiniu = self.scanner.load_device_log(self.scanner.log_file_feiniu)
        self.scanner.device_log_other = self.scanner.load_device_log(self.scanner.log_file_other)
        # 新增：每次刷新前用日志内容补全devices的memory字段
        self.patch_devices_memory_from_log()
        # 每个IP只显示一个设备，优先8000端口
        ip_to_device = {}
        for dev in devices:
            ip = dev.get('ip')
            port = int(dev.get('port', 0))
            if ip not in ip_to_device or (port == 8000):
                ip_to_device[ip] = dev
        filtered_devices = list(ip_to_device.values())
        self.device_count = len(filtered_devices)  # 只统计表格显示的设备数量
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        # 添加设备到表格
        for device in filtered_devices:
            port = int(device.get('port', 0))
            log_dict = self.scanner.device_log_feiniu if port == 8000 else self.scanner.device_log_other
            log = log_dict.get(device['ip'], {})
            # 新增：优先从品牌固件版本日志读取
            firmware = device.get('firmware', '未知')
            memory = device.get('memory', '未知')
            if port == 8000:
                brand, system = '自组NAS', 'fnOS'
                model = log.get('model', '')
                cpu = log.get('cpu', '')
                # 优先显示品牌固件日志
                firmware = self.firmware_log_feiniu.get(device['ip'], firmware)
                memory_log_val = self.memory_log_feiniu.get(device['ip'], None)
                print(f"[DEBUG] 读取飞牛内存日志: ip={device['ip']} => {memory_log_val}")
                if memory_log_val:
                    memory = memory_log_val
                    device['memory'] = memory_log_val  # 同步到devices
            elif port == 8181:
                brand, system = '铁威马', 'TOS'
                model = log.get('model', '')
                cpu = log.get('cpu', '')
                firmware = self.firmware_log_terra.get(device['ip'], firmware)
                memory_log_val = self.memory_log_terra.get(device['ip'], None)
                print(f"[DEBUG] 读取铁威马内存日志: ip={device['ip']} => {memory_log_val}")
                if memory_log_val:
                    memory = memory_log_val
                    device['memory'] = memory_log_val
            elif port == 5000 or port == 8080:
                brand, system = '威联通', 'QTS'
                model = log.get('model', '')
                cpu = log.get('cpu', '')
                firmware = self.firmware_log_qnap.get(device['ip'], firmware)
                memory_log_val = self.memory_log_qnap.get(device['ip'], None)
                print(f"[DEBUG] 读取威联通内存日志: ip={device['ip']} => {memory_log_val}")
                if memory_log_val:
                    memory = memory_log_val
                    device['memory'] = memory_log_val
            else:
                brand, system = '-', '-'
                model = log.get('model', '')
                cpu = log.get('cpu', '')
            print(f"[DEBUG] 插入表格: ip={device['ip']}, memory={memory}")
            self.tree.insert("", tk.END, values=(
                device['ip'],
                brand,
                model,
                system,
                device['name'],
                cpu,
                memory,
                firmware,
                device['status'],
                device['port'],
                device['last_seen']
            ))
            if device['name'] == '飞牛NAS':
                self.tree.item(self.tree.get_children()[-1], tags=('feiniu',))
            elif device['name'] == '未知设备':
                self.tree.item(self.tree.get_children()[-1], tags=('unknown',))
            else:
                self.tree.item(self.tree.get_children()[-1], tags=('other',))
        self.tree.tag_configure('feiniu', background='#e8f5e9')
        self.tree.tag_configure('unknown', background='#fff8e1')
        self.tree.tag_configure('other', background='#e3f2fd')
        self.update_device_combo()
        self.save_sort_json()  # 只在这里保存JSON

    def patch_devices_memory_from_log(self):
        for dev in self.scanner.devices:
            ip = dev.get('ip')
            port = int(dev.get('port', 0))
            if port == 8000:
                mem = self.memory_log_feiniu.get(ip)
                if mem:
                    dev['memory'] = mem
            elif port == 8181:
                mem = self.memory_log_terra.get(ip)
                if mem:
                    dev['memory'] = mem
            elif port == 5000 or port == 8080:
                mem = self.memory_log_qnap.get(ip)
                if mem:
                    dev['memory'] = mem

    def patch_device_fields(self):
        for dev in self.scanner.devices:
            port = int(dev.get('port', 0))
            log_dict = self.scanner.device_log_feiniu if port == 8000 else self.scanner.device_log_other
            log = log_dict.get(dev['ip'], {})
            # 修正：设备名称优先取日志/用户填写内容，没有则为'未知设备'
            if port == 8000:
                dev['brand'] = '自组NAS'
                dev['system'] = 'fnOS'
                dev['model'] = log.get('model', '')
                dev['name'] = log.get('name', dev.get('name', '未知设备'))
            elif port == 8181:
                dev['brand'] = '铁威马'
                dev['system'] = 'TOS'
                dev['model'] = log.get('model', '')
                dev['name'] = log.get('name', dev.get('name', '未知设备'))
            elif port == 5000 or port == 8080:
                dev['brand'] = '威联通'
                dev['system'] = 'QTS'
                dev['model'] = log.get('model', '')
                dev['name'] = log.get('name', dev.get('name', '未知设备'))
            else:
                dev['brand'] = '-'
                dev['system'] = '-'
                dev['model'] = log.get('model', '')
                dev['name'] = log.get('name', dev.get('name', '未知设备'))

    def on_double_click(self, event):
        """双击设备事件"""
        selected = self.tree.selection()
        if not selected:
            return

        item = selected[0]
        values = self.tree.item(item, "values")
        if values:
            ip = values[0]
            port = values[9]  # 修正为端口列
            self.open_device_web(ip, port)

    def open_device_web(self, ip, port):
        """打开设备管理页面，优先用chrome，没有则用默认浏览器"""
        import webbrowser
        import shutil
        url = f"http://{ip}:{port}"
        try:
            chrome_path = shutil.which("chrome") or shutil.which("chrome.exe")
            if chrome_path:
                webbrowser.get(f'"{chrome_path}" %s').open(url)
                self.status_var.set(f"正在用Chrome打开: {url}")
            else:
                webbrowser.open(url)
                self.status_var.set(f"正在打开: {url}")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开浏览器: {str(e)}")

    def on_scan_finished(self):
        self.status_var.set(f"扫描完成 | 共找到 {self.device_count} 台设备 | 100%")
        self.progressbar['value'] = 100

    def on_right_click(self, event):
        # 右键菜单弹出，只显示当前列的修改项
        iid = self.tree.identify_row(event.y)
        col = self.tree.identify_column(event.x)
        if iid and col:
            self.tree.selection_set(iid)
            col_index = int(col.replace('#', '')) - 1
            columns = self.tree['columns']
            field = columns[col_index]
            # 获取当前选中行的所有值
            values = self.tree.item(iid, "values")
            port = int(values[9]) if len(values) > 9 else 0
            # 只允许自组NAS（端口8000）型号栏可编辑，CPU栏不可编辑
            field_map = {
                'name': '设备名称',
                'model': '型号',
                'memory': '内存',
                'firmware': '固件版本'
            }
            if field == 'cpu' and port == 8000:
                return  # 禁止自组NAS的CPU栏编辑
            if field in field_map:
                menu = tk.Menu(self.root, tearoff=0)
                menu.add_command(label=f"修改{field_map[field]}", command=lambda: self.edit_device_field(field))
                menu.post(event.x_root, event.y_root)

    def edit_device_field(self, field):
        selected = self.tree.selection()
        if not selected:
            return
        item = selected[0]
        values = self.tree.item(item, "values")
        field_map = {
            'name': '设备名称',
            'model': '型号',
            'memory': '内存',
            'firmware': '固件版本'
        }
        current_value = ''
        idx = 0
        if field == 'name':
            # 修正：默认内容取表格当前行的设备名称栏内容
            current_value = values[4]  # 设备名称栏
            idx = 4
        elif field == 'model':
            current_value = values[2]
            idx = 2
        elif field == 'memory':
            current_value = values[6]
            idx = 6
        elif field == 'firmware':
            current_value = values[7]
            idx = 7
        else:
            return
        win = tk.Toplevel(self.root)
        win.title(f"修改{field_map[field]}")
        # 使用配置的宽高
        width = getattr(self, 'popup_default_width', 420)
        height = getattr(self, 'popup_default_height', 260)
        if field == 'firmware':
            win.geometry(f"{width}x{height}")
        elif field == 'memory':
            win.geometry(f"{width}x170")
        else:
            win.geometry(f"{width}x140")
        win.transient(self.root)
        win.grab_set()
        # 设置弹窗位置：主窗口右侧对齐
        def place_popup():
            if not win.winfo_exists():
                return
            self.root.update_idletasks()
            root_x = self.root.winfo_x()
            root_y = self.root.winfo_y()
            root_w = self.root.winfo_width()
            win.update_idletasks()
            win_h = win.winfo_height()
            x = root_x + root_w
            y = root_y
            win.geometry(f"{width}x{win_h}+{x}+{y}")
        place_popup()
        # 优化：主窗口移动/缩放时弹窗实时跟随
        def on_root_configure(event):
            place_popup()
        bind_id = self.root.bind('<Configure>', on_root_configure)
        # 失去焦点时自动关闭（仅主窗口获得焦点时关闭）
        def on_popup_focus_out(event):
            # 如果焦点在主窗口（不是弹窗或其子控件），则关闭
            if win.focus_get() is None or not str(win.focus_get()).startswith(str(win)):
                try:
                    self.root.unbind('<Configure>', bind_id)
                except Exception:
                    pass
                win.grab_release()
                win.destroy()
        win.bind('<FocusOut>', on_popup_focus_out)
        def on_close():
            try:
                self.root.unbind('<Configure>', bind_id)
            except Exception:
                pass
            win.grab_release()
            win.destroy()
        win.protocol("WM_DELETE_WINDOW", on_close)
        if field == 'memory':
            import re
            m = re.match(r'DDR(\d+)-(\d+)GB', current_value)
            ddr = m.group(1) if m else ''
            size = m.group(2) if m else ''
            tk.Label(win, text=f"现内存：{current_value}", font=("微软雅黑", 10)).pack(pady=8)
            mem_frame = tk.Frame(win)
            mem_frame.pack(pady=5)
            tk.Label(mem_frame, text="内存代数：DDR", font=("微软雅黑", 10)).pack(side=tk.LEFT)
            entry_ddr = tk.Entry(mem_frame, width=4, font=("微软雅黑", 10))
            entry_ddr.insert(0, ddr)
            entry_ddr.pack(side=tk.LEFT, padx=2)
            tk.Label(mem_frame, text="-", font=("微软雅黑", 10)).pack(side=tk.LEFT)
            entry_size = tk.Entry(mem_frame, width=6, font=("微软雅黑", 10))
            entry_size.insert(0, size)
            entry_size.pack(side=tk.LEFT, padx=2)
            tk.Label(mem_frame, text="GB", font=("微软雅黑", 10)).pack(side=tk.LEFT)
        elif field == 'firmware':
            import re
            port = int(values[9]) if len(values) > 9 else 0
            brand = values[1]
            current_value = values[7]
            # 飞牛 fnOS 固件格式：0.9.12
            if port == 8000:
                m = re.match(r'(\d+)\.(\d+)\.(\d+)', current_value)
                major = m.group(1) if m else ''
                minor = m.group(2) if m else ''
                patch = m.group(3) if m else ''
                tk.Label(win, text=f"现固件版本：{current_value}", font=("微软雅黑", 10)).pack(pady=8)
                fw_frame = tk.Frame(win)
                fw_frame.pack(pady=5)
                tk.Label(fw_frame, text="主版本号：", font=("微软雅黑", 10)).pack(side=tk.LEFT)
                entry_major = tk.Entry(fw_frame, width=4, font=("微软雅黑", 10))
                entry_major.insert(0, major)
                entry_major.pack(side=tk.LEFT, padx=2)
                tk.Label(fw_frame, text=".", font=("微软雅黑", 10)).pack(side=tk.LEFT)
                entry_minor = tk.Entry(fw_frame, width=4, font=("微软雅黑", 10))
                entry_minor.insert(0, minor)
                entry_minor.pack(side=tk.LEFT, padx=2)
                tk.Label(fw_frame, text=".", font=("微软雅黑", 10)).pack(side=tk.LEFT)
                entry_patch = tk.Entry(fw_frame, width=4, font=("微软雅黑", 10))
                entry_patch.insert(0, patch)
                entry_patch.pack(side=tk.LEFT, padx=2)
                # 自动全选
                for e in (entry_major, entry_minor, entry_patch):
                    e.bind('<FocusIn>', lambda event, entry=e: entry.select_range(0, tk.END))
                # 回车跳转
                entry_major.bind('<Return>', lambda event: entry_minor.focus_set())
                entry_minor.bind('<Return>', lambda event: entry_patch.focus_set())
                entry_patch.bind('<Return>', lambda event: save())
                # 新增复选框
                group_all_var = tk.BooleanVar(value=False)
                group_all_chk = tk.Checkbutton(win, text="修改飞牛全部设备版本号", variable=group_all_var, font=("微软雅黑", 10))
                group_all_chk.pack(pady=4)
            elif port in (5000, 8080):
                m = re.match(r'(\d+)\.(\d+)\.(\d+)\.(\d+)', current_value)
                v1 = m.group(1) if m else ''
                v2 = m.group(2) if m else ''
                v3 = m.group(3) if m else ''
                v4 = m.group(4) if m else ''
                tk.Label(win, text=f"现固件版本：{current_value}", font=("微软雅黑", 10)).pack(pady=8)
                fw_frame = tk.Frame(win)
                fw_frame.pack(pady=5)
                entrys = []
                for i, val in enumerate([v1, v2, v3, v4]):
                    entry = tk.Entry(fw_frame, width=4, font=("微软雅黑", 10))
                    entry.insert(0, val)
                    entry.pack(side=tk.LEFT, padx=2)
                    entrys.append(entry)
                    if i < 3:
                        tk.Label(fw_frame, text=".", font=("微软雅黑", 10)).pack(side=tk.LEFT)
                entry_major, entry_minor, entry_patch, entry_build = entrys
                # 自动全选
                for e in entrys:
                    e.bind('<FocusIn>', lambda event, entry=e: entry.select_range(0, tk.END))
                # 回车跳转
                entry_major.bind('<Return>', lambda event: entry_minor.focus_set())
                entry_minor.bind('<Return>', lambda event: entry_patch.focus_set())
                entry_patch.bind('<Return>', lambda event: entry_build.focus_set())
                entry_build.bind('<Return>', lambda event: save())
                # 新增复选框
                group_all_var = tk.BooleanVar(value=False)
                group_all_chk = tk.Checkbutton(win, text="修改威联通全部设备版本号", variable=group_all_var, font=("微软雅黑", 10))
                group_all_chk.pack(pady=4)
            elif port == 8181:
                m = re.match(r'(\d+)\.(\d+)\.(\d+)-(\d+)', current_value)
                v1 = m.group(1) if m else ''
                v2 = m.group(2) if m else ''
                v3 = m.group(3) if m else ''
                v4 = m.group(4) if m else ''
                tk.Label(win, text=f"现固件版本：{current_value}", font=("微软雅黑", 10)).pack(pady=8)
                fw_frame = tk.Frame(win)
                fw_frame.pack(pady=5)
                entry_v1 = tk.Entry(fw_frame, width=4, font=("微软雅黑", 10))
                entry_v1.insert(0, v1)
                entry_v1.pack(side=tk.LEFT, padx=2)
                tk.Label(fw_frame, text=".", font=("微软雅黑", 10)).pack(side=tk.LEFT)
                entry_v2 = tk.Entry(fw_frame, width=4, font=("微软雅黑", 10))
                entry_v2.insert(0, v2)
                entry_v2.pack(side=tk.LEFT, padx=2)
                tk.Label(fw_frame, text=".", font=("微软雅黑", 10)).pack(side=tk.LEFT)
                entry_v3 = tk.Entry(fw_frame, width=6, font=("微软雅黑", 10))
                entry_v3.insert(0, v3)
                entry_v3.pack(side=tk.LEFT, padx=2)
                tk.Label(fw_frame, text="-", font=("微软雅黑", 10)).pack(side=tk.LEFT)
                entry_v4 = tk.Entry(fw_frame, width=6, font=("微软雅黑", 10))
                entry_v4.insert(0, v4)
                entry_v4.pack(side=tk.LEFT, padx=2)
                # 自动全选
                for e in (entry_v1, entry_v2, entry_v3, entry_v4):
                    e.bind('<FocusIn>', lambda event, entry=e: entry.select_range(0, tk.END))
                # 回车跳转
                entry_v1.bind('<Return>', lambda event: entry_v2.focus_set())
                entry_v2.bind('<Return>', lambda event: entry_v3.focus_set())
                entry_v3.bind('<Return>', lambda event: entry_v4.focus_set())
                entry_v4.bind('<Return>', lambda event: save())
                # 新增复选框
                group_all_var = tk.BooleanVar(value=False)
                group_all_chk = tk.Checkbutton(win, text="修改铁威马全部设备版本号", variable=group_all_var, font=("微软雅黑", 10))
                group_all_chk.pack(pady=4)
        elif field == 'model' or field == 'name':
            tk.Label(win, text=f"现{field_map[field]}：{current_value}", font=("微软雅黑", 10)).pack(pady=10)
            entry = tk.Entry(win, font=("微软雅黑", 10))
            entry.insert(0, current_value)
            entry.pack(pady=5, fill=tk.X, padx=20)
            entry.focus_set()
        else:
            tk.Label(win, text=f"现{field_map[field]}：{current_value}", font=("微软雅黑", 10)).pack(pady=10)
            entry = tk.Entry(win, font=("微软雅黑", 10))
            entry.insert(0, current_value)
            entry.pack(pady=5, fill=tk.X, padx=20)
            entry.focus_set()
        def close():
            win.grab_release()
            win.destroy()
        def save():
            ip = values[0]
            port = int(values[9]) if len(values) > 9 else 0
            log_dict, log_file = self.scanner.get_log_dict_and_file(port)
            if not isinstance(log_dict, dict):
                log_dict = {}
            if not isinstance(log_file, str):
                log_file = os.path.join(self.scanner.log_path, '未知设备.log')
            if field == 'firmware':
                # --- 飞牛 ---
                if port == 8000:
                    major = entry_major.get().strip() or '0'
                    minor = entry_minor.get().strip() or '0'
                    patch = entry_patch.get().strip() or '0'
                    if not (major.isdigit() and minor.isdigit() and patch.isdigit()):
                        close()
                        return
                    new_value = f"{major}.{minor}.{patch}"
                    if 'group_all_var' in locals() and group_all_var.get():
                        # 修改全部飞牛
                        for dev in self.scanner.devices:
                            if int(dev.get('port', 0)) == 8000:
                                self.firmware_log_feiniu[dev['ip']] = new_value
                        self.save_firmware_log(self.firmware_log_feiniu_path, self.firmware_log_feiniu)
                    else:
                        self.firmware_log_feiniu[ip] = new_value
                        self.save_firmware_log(self.firmware_log_feiniu_path, self.firmware_log_feiniu)
                # --- 威联通 ---
                elif port in (5000, 8080):
                    v1 = entry_major.get().strip() or '0'
                    v2 = entry_minor.get().strip() or '0'
                    v3 = entry_patch.get().strip() or '0'
                    v4 = entry_build.get().strip() or '0'
                    if not (v1.isdigit() and v2.isdigit() and v3.isdigit() and v4.isdigit()):
                        close()
                        return
                    new_value = f"{v1}.{v2}.{v3}.{v4}"
                    if 'group_all_var' in locals() and group_all_var.get():
                        for dev in self.scanner.devices:
                            if int(dev.get('port', 0)) in (5000, 8080):
                                self.firmware_log_qnap[dev['ip']] = new_value
                        self.save_firmware_log(self.firmware_log_qnap_path, self.firmware_log_qnap)
                    else:
                        self.firmware_log_qnap[ip] = new_value
                        self.save_firmware_log(self.firmware_log_qnap_path, self.firmware_log_qnap)
                # --- 铁威马 ---
                elif port == 8181:
                    v1 = entry_v1.get().strip() or '0'
                    v2 = entry_v2.get().strip() or '0'
                    v3 = entry_v3.get().strip() or '0'
                    v4 = entry_v4.get().strip() or '0'
                    if not (v1.isdigit() and v2.isdigit() and v3.isdigit() and v4.isdigit()):
                        close()
                        return
                    v4 = v4.zfill(5)
                    new_value = f"{v1}.{v2}.{v3}-{v4}"
                    if 'group_all_var' in locals() and group_all_var.get():
                        for dev in self.scanner.devices:
                            if int(dev.get('port', 0)) == 8181:
                                self.firmware_log_terra[dev['ip']] = new_value
                        self.save_firmware_log(self.firmware_log_terra_path, self.firmware_log_terra)
                    else:
                        self.firmware_log_terra[ip] = new_value
                        self.save_firmware_log(self.firmware_log_terra_path, self.firmware_log_terra)
                else:
                    new_value = entry.get().strip()
                # 更新设备内存
                for dev in self.scanner.devices:
                    if dev['ip'] == ip and int(dev.get('port', 0)) == port:
                        dev['firmware'] = new_value
                log_entry = log_dict.get(ip, {})
                log_entry['firmware'] = new_value
                log_dict[ip] = log_entry
                self.scanner.save_device_log(log_file, log_dict)
                self.update_device_list(self.scanner.devices)
                close()
                return
            elif field == 'memory':
                ddr = entry_ddr.get().strip()
                size = entry_size.get().strip()
                if not ddr or not size or not ddr.isdigit() or not size.isdigit():
                    close()
                    return
                new_value = f"DDR{ddr}-{size}GB"
                print(f"[DEBUG] 保存内存: ip={ip}, port={port}, new_value={new_value}")
                for dev in self.scanner.devices:
                    if dev['ip'] == ip and int(dev.get('port', 0)) == port:
                        dev['memory'] = new_value
                log_entry = log_dict.get(ip, {})
                log_entry['memory'] = new_value
                log_dict[ip] = log_entry
                self.scanner.save_device_log(log_file, log_dict)
                # 保存到品牌内存日志（所有品牌都写）
                if port == 8000:
                    self.memory_log_feiniu[ip] = new_value
                    self.save_memory_log(self.memory_log_feiniu_path, self.memory_log_feiniu)
                elif port == 8181:
                    self.memory_log_terra[ip] = new_value
                    self.save_memory_log(self.memory_log_terra_path, self.memory_log_terra)
                elif port == 5000 or port == 8080:
                    self.memory_log_qnap[ip] = new_value
                    self.save_memory_log(self.memory_log_qnap_path, self.memory_log_qnap)
                self.update_device_list(self.scanner.devices)
                close()
                return
            elif field == 'model':
                new_value = entry.get().strip()
                if not new_value:
                    close()
                    return
                ip = values[0]
                port = int(values[9]) if len(values) > 9 else 0
                brand = values[1]
                log_dict, log_file = self.scanner.get_log_dict_and_file(port)
                if port == 8000:
                    def norm(s):
                        return s.upper().replace(' ', '').replace('-', '')
                    short = norm(new_value)
                    cpu_name = ''
                    for k, v in self.cpu_map.items():
                        if short == norm(k):
                            cpu_name = v
                            break
                    if not cpu_name:
                        for k, v in self.cpu_map.items():
                            if short in norm(k):
                                cpu_name = v
                                break
                    log_entry = log_dict.get(ip, {})
                    log_entry['model'] = new_value
                    log_entry['cpu'] = cpu_name
                    log_dict[ip] = log_entry
                    for dev in self.scanner.devices:
                        if dev.get('ip') == ip and dev.get('port', 0) == 8000:
                            dev['model'] = new_value
                            dev['cpu'] = cpu_name
                    new_values = list(values)
                    new_values[2] = new_value
                    new_values[5] = cpu_name
                    self.tree.item(item, values=new_values)
                    self.scanner.save_device_log(log_file, log_dict)
                    # 型号变更时自动写入内存日志（如果有内存信息）
                    memory = log_entry.get('memory', '')
                    if memory:
                        self.memory_log_feiniu[ip] = memory
                        self.save_memory_log(self.memory_log_feiniu_path, self.memory_log_feiniu)
                    close()
                    return
                else:
                    info = self.find_nas_model_info(brand, new_value)
                    cpu = info.get('处理器', '') if info else ''
                    memory = info.get('内存', '') if info else ''
                    log_entry = log_dict.get(ip, {})
                    log_entry['model'] = new_value
                    log_entry['cpu'] = cpu
                    log_entry['memory'] = memory
                    log_dict[ip] = log_entry
                    for dev in self.scanner.devices:
                        if dev.get('ip') == ip and dev.get('port', 0) == port:
                            dev['model'] = new_value
                            dev['cpu'] = cpu
                            dev['memory'] = memory
                    new_values = list(values)
                    new_values[2] = new_value
                    new_values[5] = cpu
                    new_values[6] = memory
                    self.tree.item(item, values=new_values)
                    self.scanner.save_device_log(log_file, log_dict)
                    # 型号变更时自动写入内存日志（如果有内存信息）
                    if memory:
                        if port == 8000:
                            self.memory_log_feiniu[ip] = memory
                            self.save_memory_log(self.memory_log_feiniu_path, self.memory_log_feiniu)
                        elif port == 8181:
                            self.memory_log_terra[ip] = memory
                            self.save_memory_log(self.memory_log_terra_path, self.memory_log_terra)
                        elif port == 5000 or port == 8080:
                            self.memory_log_qnap[ip] = memory
                            self.save_memory_log(self.memory_log_qnap_path, self.memory_log_qnap)
                    close()
                    return
            elif field == 'name':
                new_value = entry.get().strip()
                if not new_value:
                    close()
                    return
                ip = values[0]
                port = int(values[9]) if len(values) > 9 else 0
                log_dict, log_file = self.scanner.get_log_dict_and_file(port)
                # 只修改当前IP的设备名称
                for dev in self.scanner.devices:
                    if dev['ip'] == ip and int(dev.get('port', 0)) == port:
                        dev['name'] = new_value
                log_entry = log_dict.get(ip, {})
                log_entry['name'] = new_value
                log_dict[ip] = log_entry
                self.scanner.save_device_log(log_file, log_dict)
                # 写入品牌设备名称日志
                if port == 8000:
                    self.name_log_feiniu[ip] = new_value
                    self.save_name_log(self.name_log_feiniu_path, self.name_log_feiniu)
                elif port == 8181:
                    self.name_log_terra[ip] = new_value
                    self.save_name_log(self.name_log_terra_path, self.name_log_terra)
                elif port == 5000 or port == 8080:
                    self.name_log_qnap[ip] = new_value
                    self.save_name_log(self.name_log_qnap_path, self.name_log_qnap)
                self.update_device_list(self.scanner.devices)
                close()
                return
            else:
                new_value = entry.get().strip()
                for dev in self.scanner.devices:
                    dev[field] = new_value
                log_entry = log_dict.get(ip, {})
                log_entry[field] = new_value
                log_dict[ip] = log_entry
                self.scanner.save_device_log(log_file, log_dict)
                self.update_device_list(self.scanner.devices)
                close()
        btn_frame = tk.Frame(win)
        btn_frame.pack(pady=10)
        tk.Button(btn_frame, text="确定", command=save, width=10).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="取消", command=close, width=10).pack(side=tk.LEFT, padx=5)
        focus_entries = []
        if field == 'memory':
            # ...原有代码...
            focus_entries = [entry_ddr, entry_size]
        elif field == 'firmware':
            import re
            port = int(values[9]) if len(values) > 9 else 0
            brand = values[1]
            current_value = values[7]
            if port == 8000:
                # ...原有代码...
                focus_entries = [entry_major, entry_minor, entry_patch]
            elif port in (5000, 8080):
                # ...原有代码...
                focus_entries = [entry_major, entry_minor, entry_patch, entry_build]
            elif port == 8181:
                # ...原有代码...
                focus_entries = [entry_v1, entry_v2, entry_v3, entry_v4]
        elif field == 'model' or field == 'name':
            # ...原有代码...
            focus_entries = [entry]
        else:
            # ...原有代码...
            focus_entries = [entry]
        def on_popup_focus_out(event):
            # 只在有entry控件时才判断焦点
            if win.focus_get() not in focus_entries:
                try:
                    self.root.unbind('<Configure>', bind_id)
                except Exception:
                    pass
                win.grab_release()
                win.destroy()
        win.bind("<FocusOut>", on_popup_focus_out)
        win.protocol("WM_DELETE_WINDOW", close)

    def open_all_feiniu_web(self):
        """一键打开所有飞牛NAS（端口8000）管理页面"""
        # 只遍历当前表格显示的设备
        for item in self.tree.get_children():
            values = self.tree.item(item, "values")
            if len(values) > 9 and int(values[9]) == 8000:
                ip = values[0]
                port = values[9]
                self.open_device_web(ip, port)

    def open_all_qnap_web(self):
        """一键打开所有威联通（端口5000/8080）管理页面"""
        for item in self.tree.get_children():
            values = self.tree.item(item, "values")
            if len(values) > 9 and int(values[9]) in (5000, 8080):
                ip = values[0]
                port = values[9]
                self.open_device_web(ip, port)

    def open_all_terra_web(self):
        """一键打开所有铁威马（端口8181）管理页面"""
        for item in self.tree.get_children():
            values = self.tree.item(item, "values")
            if len(values) > 9 and int(values[9]) == 8181:
                ip = values[0]
                port = values[9]
                self.open_device_web(ip, port)

    def open_all_devices_web(self):
        """一键打开所有设备管理页面"""
        for item in self.tree.get_children():
            values = self.tree.item(item, "values")
            if len(values) > 9:
                ip = values[0]
                port = values[9]
                self.open_device_web(ip, port)

    def update_device_combo(self):
        # 修复：设备IP下拉框与表格一致，优先8000端口，唯一IP
        ip_to_device = {}
        for dev in self.scanner.devices:
            ip = dev.get('ip')
            port = int(dev.get('port', 0))
            if ip not in ip_to_device or (port == 8000):
                ip_to_device[ip] = dev
        ips = list(ip_to_device.keys())
        self.device_combo['values'] = ips
        # 不自动current(0)，由表格点击带入

    def on_device_selected(self, event=None):
        ip = self.device_combo.get()
        cred = self.cred_mgr.get_cred(ip)
        self.nas_user_entry.delete(0, tk.END)
        self.nas_user_entry.insert(0, cred['user'])
        self.nas_pass_entry.delete(0, tk.END)
        self.nas_pass_entry.insert(0, cred['pass'])

    def ask_share_selection(self, shares):
        win = tk.Toplevel(self.root)
        win.title("选择共享名")
        win.geometry("320x120")
        win.transient(self.root)
        win.grab_set()
        tk.Label(win, text="请选择NAS共享名：", font=("微软雅黑", 10)).pack(pady=10)
        var = tk.StringVar(value=shares[0])
        combo = ttk.Combobox(win, values=shares, textvariable=var, state="readonly", width=28)
        combo.pack(pady=5)
        result = {"share": None}
        def ok():
            # 直接用真实大小写的共享名
            result["share"] = var.get()
            win.grab_release()
            win.destroy()
        def cancel():
            win.grab_release()
            win.destroy()
        btn_frame = tk.Frame(win)
        btn_frame.pack(pady=8)
        tk.Button(btn_frame, text="确定", command=ok, width=10).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="取消", command=cancel, width=10).pack(side=tk.LEFT, padx=5)
        win.wait_window()
        return result["share"]

    def choose_nas_folder(self):
        ip = self.device_combo.get()
        nas_user = self.nas_user_entry.get()
        nas_pass = self.nas_pass_entry.get()  # 修正为获取密码
        print(f"【UI】选择NAS目录，当前IP: {ip}, 用户: {nas_user}")
        if ip and nas_user and nas_pass:
            shares = self.list_smb_shares(ip, nas_user, nas_pass)
            print(f"【UI】下拉菜单共享名: {shares}")
            if shares:
                share = self.ask_share_selection(shares)
                print(f"【UI】用户选择共享名: {share}")
                if share:
                    self.nas_path_var.set(share)
                    return
        # 兼容原有方式
        path = filedialog.askdirectory(title="请选择NAS目录（可选择映射的网络驱动器）")
        if path:
            smb_path = self.scanner.get_unc_path(path)
            print(f"【UI】映射盘符转换SMB路径: {smb_path}")
            self.nas_path_var.set(smb_path)

    def choose_local_folder(self):
        path = filedialog.askdirectory(title="请选择本地目录")
        if path:
            self.local_path_var.set(path)

    def toggle_password(self):
        if self.show_pass_var.get():
            self.nas_pass_entry.config(show="")
        else:
            self.nas_pass_entry.config(show="*")

    def show_sync_mode_menu(self):
        self.sync_mode_menu.post(self.sync_mode_btn.winfo_rootx(), self.sync_mode_btn.winfo_rooty() + self.sync_mode_btn.winfo_height())

    def set_sync_mode(self, mode):
        self.sync_mode_var.set(mode)
        self.sync_mode_btn.config(text=mode)
        self.sync_mode_menu.unpost()

    def on_tree_select(self, event):
        selected = self.tree.selection()
        if selected:
            item = selected[0]
            values = self.tree.item(item, "values")
            if values:
                ip = values[0]
                # 强制set并触发on_device_selected
                self.device_combo.set(ip)
                self.on_device_selected()

    def list_smb_shares(self, ip, user, passwd):
        """列出指定NAS的所有SMB共享名，保持原始大小写，并输出详细日志"""
        try:
            from smb.SMBConnection import SMBConnection
        except ImportError:
            messagebox.showerror("缺少依赖", "请先安装pysmb库: pip install pysmb")
            print("【SMB】缺少pysmb库")
            return []
        try:
            import socket
            client_name = socket.gethostname()
            server_name = ip
            passwd_masked = passwd[:2] + '*' * (len(passwd)-4) + passwd[-2:] if len(passwd) > 4 else '*'*len(passwd)
            print(f"【SMB】连接参数: user={user}, passwd={passwd_masked}, client_name={client_name}, server_name={server_name}")
            conn = SMBConnection(user, passwd, client_name, server_name, use_ntlm_v2=True)
            conn.connect(ip, 445)
            shares = conn.listShares()
            share_names = [s.name for s in shares if not s.isSpecial and s.name not in ("NETLOGON", "SYSVOL")]
            print(f"【SMB】获取到共享名列表: {share_names}")
            conn.close()
            return share_names
        except Exception as e:
            print(f"【SMB】获取共享名失败: {e}")
            traceback.print_exc()
            messagebox.showerror("SMB错误", f"获取共享名失败: {e}")
            return []

    def find_nas_model_info(self, brand, model_input):
        # 模糊匹配型号
        def norm(s):
            return s.upper().replace(' ', '').replace('-', '')
        model_input_norm = norm(model_input)
        for info in self.nas_model_info:
            if info.get('品牌') == brand:
                model_norm = norm(info.get('型号', ''))
                if model_input_norm in model_norm or model_norm in model_input_norm:
                    return info
        return None

    def load_firmware_log(self, path):
        if os.path.exists(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}

    def save_firmware_log(self, path, data):
        os.makedirs(os.path.dirname(path), exist_ok=True)
        if not isinstance(data, dict):
            data = {}
        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f'写入固件版本日志失败: {e}')

    def load_memory_log(self, path):
        if os.path.exists(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}

    def save_memory_log(self, path, data):
        os.makedirs(os.path.dirname(path), exist_ok=True)
        if not isinstance(data, dict):
            data = {}
        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f'写入内存日志失败: {e}')

    def load_name_log(self, path):
        if os.path.exists(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}

    def save_name_log(self, path, data):
        os.makedirs(os.path.dirname(path), exist_ok=True)
        if not isinstance(data, dict):
            data = {}
        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f'写入设备名称日志失败: {e}')

    # ====== 新增：表头点击排序 ======
    def on_column_click(self, col):
        # 只允许五个字段排序
        sortable_cols = {"ip", "brand", "model", "system", "name"}
        if col not in sortable_cols:
            return
        if self.sort_col == col:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_col = col
            self.sort_reverse = False
        self.sort_devices(col, self.sort_reverse)

    def sort_devices(self, col, reverse=False):
        import locale
        locale.setlocale(locale.LC_COLLATE, '')
        devices = self.scanner.devices
        if col == 'ip':
            def ip_key(dev):
                return [int(x) for x in dev['ip'].split('.')]
            devices.sort(key=ip_key, reverse=reverse)
        else:
            devices.sort(key=lambda d: locale.strxfrm(str(d.get(col, ''))), reverse=reverse)
        self.update_device_list(devices)

    # ====== 新增：保存排序用JSON ======
    def save_sort_json(self):
        sort_json_path = os.path.join(self.scanner.log_path, '设备排序.json')
        try:
            with open(sort_json_path, 'w', encoding='utf-8') as f:
                json.dump(self.scanner.devices, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"[排序JSON] 保存失败: {e}")

    def load_sort_json(self):
        sort_json_path = os.path.join(self.scanner.log_path, '设备排序.json')
        if os.path.exists(sort_json_path):
            try:
                with open(sort_json_path, 'r', encoding='utf-8') as f:
                    self.scanner.devices = json.load(f)
            except Exception as e:
                print(f"[排序JSON] 加载失败: {e}")
    # ====== 新增结束 ======

    # 新增：NAS预读取进度条回调
    def update_nas_preload_progress(self, device_index=1, device_total=1, device_name=None, folder_done=0, folder_total=1, all_done=False, **kwargs):
        percent = int((folder_done / folder_total) * 100) if folder_total else 0
        if percent > 100:
            percent = 100
        self.nas_preload_progress.set(percent)
        # 记录最后一个设备名
        if device_name:
            show_name = device_name if len(device_name) <= 10 else device_name[:10] + '...'
            self.nas_preload_device_label.config(text=show_name)
            self._last_preload_device_name = show_name
        elif all_done:
            # 全部完成时显示最后一个设备名（如果没有则显示"已完成"）
            last_name = getattr(self, '_last_preload_device_name', '已完成')
            self.nas_preload_device_label.config(text=last_name)
        # 不再else清空
        self.nas_preload_percent_label.config(text=f"{percent}%")
        self.nas_preload_label.config(text=f"NAS文件夹预读取进度：{device_index}/{device_total}")
        if all_done:
            self.nas_preload_status_label.config(text="预读取完成", fg="#27ae60")
        else:
            self.nas_preload_status_label.config(text="预读取进行中", fg="#e67e22")

    def save_account_and_preload(self):
        ip = self.device_combo.get().strip()
        user = self.nas_user_entry.get().strip()
        passwd = self.nas_pass_entry.get().strip()
        if not ip or not user or not passwd:
            messagebox.showerror("错误", "请填写设备IP、用户名和密码")
            return
        self.cred_mgr.set_cred(ip, user, passwd)
        messagebox.showinfo("提示", "账号密码已保存，将自动预读取NAS文件夹")
        self.nas_folder_preloader.preload_all()


# 优化：后台预读取所有有账号密码的设备的SMB共享及文件夹结构，树形结构+多线程+详细日志
class NASFolderPreloader:
    def __init__(self, scanner, cred_mgr, device_list_func, progress_callback=None):
        print("[DEBUG] NASFolderPreloader: 初始化")
        self.scanner = scanner
        self.cred_mgr = cred_mgr
        self.device_list_func = device_list_func  # 获取当前设备列表的方法
        self.log_dir = r'D:\飞牛NAS设备记录日志\日志\NAS文件夹预读取'
        os.makedirs(self.log_dir, exist_ok=True)
        self.device_log_map = {}  # ip -> (name, json_path)
        self.progress_callback = progress_callback
        import threading
        self.log_lock = threading.Lock()  # 日志读写锁

    def is_online(self, ip, port=445, timeout=1):
        try:
            with socket.create_connection((ip, port), timeout=timeout):
                return True
        except Exception:
            return False

    def preload_all(self):
        import threading
        print("[DEBUG] NASFolderPreloader: preload_all 启动")
        threading.Thread(target=self._preload_all, daemon=True).start()

    def _preload_all(self):
        print("[DEBUG] NASFolderPreloader: _preload_all 启动")
        devices = self.device_list_func()  # 获取当前设备列表（含设备名称）
        print(f"[DEBUG] NASFolderPreloader: 设备列表 {devices}")
        # 只统计有用户名和密码且在线的设备
        unique_devices = []
        seen_ips = set()
        for dev in devices:
            ip = dev.get('ip')
            # 强制补全设备名称字段
            if not dev.get('name'):
                if '设备名称' in dev:
                    dev['name'] = dev['设备名称']
                elif 'model' in dev:
                    dev['name'] = dev['model']
                else:
                    dev['name'] = ''
            device_name = dev.get('name', '')
            print(f"[DEBUG] NASFolderPreloader: 预读取设备 ip={ip}, name={device_name}")
            cred = self.cred_mgr.get_cred(ip)
            user, passwd = cred.get('user'), cred.get('pass')
            if ip and ip not in seen_ips and user and passwd and self.is_online(ip):
                unique_devices.append(dev)
                seen_ips.add(ip)
                # 先创建空日志
                safe_name = ''.join(c for c in device_name if c.isalnum() or c in '-_').strip() or ip
                json_path = os.path.join(self.log_dir, f'{safe_name}.json')
                self.device_log_map[ip] = (device_name, json_path)
                if not os.path.exists(json_path):
                    with self.log_lock:
                        with open(json_path, 'w', encoding='utf-8') as f:
                            json.dump({'设备IP': ip, '设备名称': device_name, '共享列表': []}, f, ensure_ascii=False, indent=2)
        device_total = len(unique_devices)
        print(f"[DEBUG] NASFolderPreloader: 需预读取设备数 {device_total}")
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor(max_workers=NAS_PRELOAD_THREAD_COUNT) as executor:
            futures = []
            for idx, dev in enumerate(unique_devices, 1):
                device_name = dev.get('name', '')
                # 新增：每台设备开始时回调一次，显示设备名和0进度
                if self.progress_callback:
                    self.progress_callback(
                        device_index=idx,
                        device_total=device_total,
                        device_name=device_name,
                        folder_done=0,
                        folder_total=1
                    )
                futures.append(executor.submit(self._preload_device_with_progress, dev, idx, device_total))
            for f in futures:
                try:
                    f.result()
                except Exception as e:
                    print(f"[ERROR] 设备线程异常: {e}")
        # 新增：所有设备都完成后，通知GUI
        if self.progress_callback:
            self.progress_callback(
                device_index=device_total,
                device_total=device_total,
                device_name=None,
                folder_done=1,
                folder_total=1,
                all_done=True
            )
        print("预读取完成")

    def _preload_device_with_progress(self, dev, device_index, device_total):
        ip = dev.get('ip')
        # 强制补全设备名称字段
        if not dev.get('name'):
            if '设备名称' in dev:
                dev['name'] = dev['设备名称']
            elif 'model' in dev:
                dev['name'] = dev['model']
            else:
                dev['name'] = ''
        device_name = dev.get('name', '')
        print(f"[DEBUG] _preload_device_with_progress: 开始设备 {device_name}({ip}) idx={device_index}/{device_total}")
        cred = self.cred_mgr.get_cred(ip)
        user, passwd = cred.get('user'), cred.get('pass')
        if not (user and passwd):
            print(f"[DEBUG] _preload_device_with_progress: 跳过无账号设备 {device_name}({ip})")
            return
        try:
            from smb.SMBConnection import SMBConnection
            import socket
            client_name = socket.gethostname()
            server_name = ip
            conn = SMBConnection(user, passwd, client_name, server_name, use_ntlm_v2=True)
            conn.connect(ip, 445)
            shares = [s.name for s in conn.listShares() if not s.isSpecial and s.name not in ("NETLOGON", "SYSVOL")]
            print(f"[DEBUG] _preload_device_with_progress: 设备 {device_name} 共享列表 {shares}")
            for share in shares:
                # 分层递归扫描
                share_struct = {'共享名': share, '目录': []}
                folder_counter = {'done': 0}
                def scan_layer(share, parent_path, parent_struct, layer):
                    # 新增：递归层级限制
                    if MAX_NAS_SCAN_DEPTH > 0 and layer > MAX_NAS_SCAN_DEPTH:
                        return
                    try:
                        files = conn.listPath(share, parent_path)
                        changed = False
                        for f in files:
                            if f.isDirectory and f.filename not in ('.', '..'):
                                folder_counter['done'] += 1
                                # 新增：每递归一个文件夹时回调一次，实时刷新进度条
                                if self.progress_callback:
                                    self.progress_callback(
                                        device_index=device_index,
                                        device_total=device_total,
                                        device_name=device_name,
                                        folder_done=folder_counter['done'],
                                        folder_total=100  # 估算总数，实际无法提前统计
                                    )
                                sub_path = parent_path + '/' + f.filename if parent_path else f.filename
                                # 查找是否已存在该目录
                                exist = None
                                for d in parent_struct:
                                    if d.get('名称') == f.filename:
                                        exist = d
                                        break
                                if not exist:
                                    new_dir = {'名称': f.filename, '子目录': []}
                                    parent_struct.append(new_dir)
                                    changed = True
                                else:
                                    new_dir = exist
                                # 递归下一层
                                scan_layer(share, sub_path, new_dir['子目录'], layer+1)
                        # 每层扫描完就写入日志（全量覆盖，但只在有变化时写入）
                        if changed:
                            print(f"[DEBUG] scan_layer: 写入日志 设备 {device_name} 共享 {share} 层 {layer}")
                            self._save_log_threadsafe(ip, device_name, [share_struct])
                    except Exception as e:
                        print(f'[NAS预读取] 递归层{layer}失败: {e}')
                scan_layer(share, '', share_struct['目录'], 1)
                # 扫描完所有层后再写一次日志，保证完整
                print(f"[DEBUG] _preload_device_with_progress: 设备 {device_name} 共享 {share} 扫描完成，写入日志")
                self._save_log_threadsafe(ip, device_name, [share_struct])
            conn.close()
        except Exception as e:
            print(f'[NAS预读取] 设备 {ip} 失败: {e}')

    def _save_log_threadsafe(self, ip, device_name, share_structs):
        """
        增量写入日志：递归合并目录树，只追加新目录，不覆盖已有内容。
        """
        import json
        safe_name = ''.join(c for c in device_name if c.isalnum() or c in '-_').strip() or ip
        json_path = os.path.join(self.log_dir, f'{safe_name}.json')
        def merge_dirs(old_dirs, new_dirs):
            # old_dirs/new_dirs: list of {'名称':..., '子目录':...}
            name2old = {d['名称']: d for d in old_dirs}
            for new_dir in new_dirs:
                name = new_dir['名称']
                if name in name2old:
                    # 递归合并子目录
                    merge_dirs(name2old[name]['子目录'], new_dir.get('子目录', []))
                else:
                    old_dirs.append(new_dir)
        with self.log_lock:
            # 读取旧内容
            if os.path.exists(json_path):
                try:
                    with open(json_path, 'r', encoding='utf-8') as f:
                        old_data = json.load(f)
                except Exception:
                    old_data = {'设备IP': ip, '设备名称': device_name, '共享列表': []}
            else:
                old_data = {'设备IP': ip, '设备名称': device_name, '共享列表': []}
            # 合并共享列表
            old_share_map = {s['共享名']: s for s in old_data.get('共享列表', [])}
            for new_share in share_structs:
                share_name = new_share['共享名']
                if share_name in old_share_map:
                    merge_dirs(old_share_map[share_name]['目录'], new_share.get('目录', []))
                else:
                    old_data['共享列表'].append(new_share)
            # 写回
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(old_data, f, ensure_ascii=False, indent=2)

    def _preload_device(self, dev):
        ip = dev.get('ip')
        name = dev.get('name') or ip
        cred = self.cred_mgr.get_cred(ip)
        user, passwd = cred.get('user'), cred.get('pass')
        if not (user and passwd):
            return
        try:
            print(f"[NAS预读取] 正在读取设备: {ip}（{name}）")
            shares = self._list_shares(ip, user, passwd)
            share_structs = []
            for share in shares:
                print(f"[NAS预读取] 正在读取共享: {share}")
                conn = self._get_conn(ip, user, passwd)
                struct = {
                    '共享名': share,
                    '目录': self._list_dirs(conn, share, '', ip, name, share_structs)
                }
                conn.close()
                share_structs.append(struct)
                # 每次有新共享结构都实时保存
                self._save_log(ip, name, share_structs)
            # 设备全部完成后再保存一次（冗余安全）
            self._save_log(ip, name, share_structs)
            print(f"[NAS预读取] 设备 {ip}（{name}）完成，已保存到 {os.path.join(self.log_dir, ''.join(c for c in name if c.isalnum() or c in '-_').strip() or ip + '.json')} ")
        except Exception as e:
            print(f'[NAS预读取] 设备 {ip} 失败: {e}')

    def _get_conn(self, ip, user, passwd):
        from smb.SMBConnection import SMBConnection
        import socket
        client_name = socket.gethostname()
        server_name = ip
        conn = SMBConnection(user, passwd, client_name, server_name, use_ntlm_v2=True)
        conn.connect(ip, 445)
        return conn

    def _list_shares(self, ip, user, passwd):
        conn = self._get_conn(ip, user, passwd)
        shares = conn.listShares()
        share_names = [s.name for s in shares if not s.isSpecial and s.name not in ("NETLOGON", "SYSVOL")]
        conn.close()
        return share_names

    def _list_dirs(self, conn, share, path, ip, name, share_structs, visited=None, layer=1):
        # 新增：递归层级限制
        if MAX_NAS_SCAN_DEPTH > 0 and layer > MAX_NAS_SCAN_DEPTH:
            return []
        if visited is None:
            visited = set()
        key = f"{share}/{path}"
        if key in visited:
            return []
        visited.add(key)
        result = []
        try:
            print(f"[NAS预读取] 正在递归目录: {share}/{path}")
            files = conn.listPath(share, path)
            for f in files:
                if f.isDirectory and f.filename not in ('.', '..'):
                    sub_path = path + '/' + f.filename if path else f.filename
                    sub_result = self._list_dirs(conn, share, sub_path, ip, name, share_structs, visited, layer+1)
                    result.append({
                        '名称': f.filename,
                        '子目录': sub_result
                    })
                    # 每递归一层都实时保存日志
                    self._save_log(ip, name, share_structs)
        except Exception as e:
            print(f'[NAS预读取] 读取 {share} {path} 失败: {e}')
        return result


# ====== 代码配置区（请在此处修改参数）======
# 预读取NAS文件夹的线程数（建议4-8，过高会影响NAS性能）
NAS_PRELOAD_THREAD_COUNT = 8
# 扫描设备的线程数（建议100-300，过高会影响网络）
DEVICE_SCAN_THREAD_COUNT = 300
# 预读取NAS文件夹最大递归层级（如2表示只递归到共享名/第一级/第二级目录，防止扫描过深导致卡顿）
MAX_NAS_SCAN_DEPTH = 1  # 建议2~3，0为不限制
# =======================

if __name__ == "__main__":
    root = tk.Tk()

    # 设置Windows下更好的DPI处理
    if sys.platform.startswith('win'):
        from ctypes import windll
        windll.shcore.SetProcessDpiAwareness(1)

    app = ScannerApp(root)
    root.mainloop()