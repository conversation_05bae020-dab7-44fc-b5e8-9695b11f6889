import os
import re
import time
import random
import logging
import img2pdf
import requests
import shutil
import json
from datetime import datetime
from threading import Lock, RLock
from tqdm import tqdm
from PyPDF2 import PdfMerger
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from PIL import Image
from io import BytesIO
from smb.SMBConnection import SMBConnection
import hashlib
import sys
import traceback
import threading


class ComicDownloader:
    # 类级锁用于保护对比日志
    compare_log_lock = Lock()

    # 新增：全局请求失败统计与自适应限流参数
    REQUEST_FAIL_COUNT = 0
    REQUEST_TOTAL_COUNT = 0
    LAST_BAN_TIME = 0
    GLOBAL_SLEEP = 0
    GLOBAL_MAX_WORKERS = 4
    GLOBAL_MIN_WORKERS = 1
    GLOBAL_CUR_WORKERS = 4
    GLOBAL_LOCK = Lock()

    def __init__(self, book_id, user_agents, nas_config=None, max_img_threads=5, max_chapter_threads=5):
        self.book_id = book_id
        self.user_agents = user_agents
        self.nas_config = nas_config or {}
        self.max_img_threads = max_img_threads
        self._chapter_threads = max_chapter_threads
        self.ua_counter = {ua: 0 for ua in user_agents}
        self.ua_last_used = {ua: 0 for ua in user_agents}
        self.ua_lock = Lock()

        # 初始化基础目录
        self.base_dir = "D:/ComicsDownloads/开心看漫画"
        self.log_dir = os.path.join(self.base_dir, "开心看漫画日志")
        self.cover_dir = os.path.join(self.base_dir, "开心看漫画封面")
        self.final_pdf_dir = os.path.join(self.base_dir, "开心看漫画PDF")
        self.raw_dir = os.path.join(self.base_dir, "开心看漫画章节图片下载", f"comic_{book_id}")
        self.pdf_dir = os.path.join(self.base_dir, "ComicPDFs", f"comic_{book_id}")
        self.nas_temp_dir = os.path.join(self.base_dir, "开心看漫画NAS中间件")

        # 创建必要目录
        os.makedirs(self.log_dir, exist_ok=True)
        os.makedirs(self.cover_dir, exist_ok=True)
        os.makedirs(self.final_pdf_dir, exist_ok=True)
        os.makedirs(self.raw_dir, exist_ok=True)
        os.makedirs(self.pdf_dir, exist_ok=True)
        os.makedirs(self.nas_temp_dir, exist_ok=True)

        # 设置对比日志路径
        self.compare_log_path = os.path.join(self.log_dir, "开心看漫画对比日志.txt")

        # 获取元数据
        raw_name, cover_url = self.get_comic_metadata()
        self.comic_name = self.clean_comic_name(raw_name)
        safe_comic_name = self.safe_filename(self.comic_name, 30)

        # 设置封面路径
        self.cover_path = os.path.join(self.cover_dir, f"{safe_comic_name}-{book_id}.jpg")

        # 初始化日志
        self.logger = self.setup_logger()
        self.session = requests.Session()
        self.first_chapter = 0
        self.last_chapter = 0
        self.stats = {
            'total_images': 0,
            'converted_images': 0,
            'original_sizes': 0,
            'optimized_sizes': 0,
            'failed_downloads': 0
        }

        # 下载状态相关
        self.need_download = True
        self.log_record_max = 0
        self.download_status = "new"  # new/update/skip
        self.website_chapters = 0
        self.nas_upload_lock = RLock()  # 用于NAS上传的锁
        self.old_nas_pdf_name = None  # 存储旧PDF名称用于后续删除

        # 检查封面是否已下载
        if not os.path.exists(self.cover_path):
            self.download_cover(cover_url)

        self.logger.info(f"系统初始化完成 | 漫画名称: {self.comic_name}")

    @staticmethod
    def safe_filename(name, max_length=100):
        # 去除 Windows 和 Linux 不允许的字符
        name = re.sub(r'[\\/:*?"<>|\n\r\t]', '', name)
        name = name.strip().rstrip('. ')
        if len(name) > max_length:
            name = name[:max_length]
        return name

    def clean_comic_name(self, raw_name):
        cleaned = re.sub(r'[-|]免费看漫画.*$', '', raw_name)
        cleaned = re.sub(r'[^a-zA-Z0-9\u4e00-\u9fa5]', '', cleaned)
        return cleaned[:20] if cleaned else f"未命名漫画_{self.book_id}"

    def setup_logger(self):
        logger = logging.getLogger(f"Comic_{self.book_id}")
        logger.setLevel(logging.DEBUG)
        if not logger.handlers:
            safe_name = self.safe_filename(self.comic_name, 30)
            log_path = os.path.join(self.log_dir, f"{safe_name}-{self.book_id}.log")
            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
            logger.addHandler(file_handler)
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)  # 只显示INFO及以上
            console_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
            logger.addHandler(console_handler)
        return logger

    def get_random_ua(self):
        current_time = time.time()
        with self.ua_lock:
            available_ua = [ua for ua in self.user_agents if current_time - self.ua_last_used[ua] > 10]
            if not available_ua:
                min_count = min(self.ua_counter.values())
                candidates = [ua for ua, count in self.ua_counter.items() if count == min_count]
            else:
                candidates = available_ua
            selected_ua = random.choice(candidates)
            self.ua_counter[selected_ua] += 1
            self.ua_last_used[selected_ua] = current_time
            return selected_ua

    def get_comic_metadata(self):
        headers = {'Referer': 'https://kxmanhua.com/', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                   'User-Agent': self.get_random_ua()}
        for _ in range(3):
            try:
                response = requests.get(f"https://kxmanhua.com/manga/{self.book_id}", headers=headers, timeout=15,
                                        allow_redirects=False)
                soup = BeautifulSoup(response.text, 'lxml')
                title_tag = next((soup.select_one(s) for s in ['h3.anime__details__title', 'h1.comic-title', 'title'] if
                                  soup.select_one(s)), None)
                raw_title = title_tag.get_text(strip=True) if title_tag else ""
                cover_tag = next((soup.select_one(s) for s in
                                  ['div.anime__details__pic[data-setbg]', 'img.comic-cover[src]',
                                   'meta[property="og:image"]'] if soup.select_one(s)), None)
                cover_url = cover_tag.get('data-setbg') or cover_tag.get('src') or cover_tag.get('content', "")
                return raw_title, urljoin(response.url, cover_url)
            except Exception as e:
                time.sleep(random.uniform(3, 6))
        return f"未命名漫画_{self.book_id}", ""

    def download_cover(self, cover_url):
        if not cover_url or os.path.exists(self.cover_path):
            return
        retry_count = 0
        while True:
            temp_path = os.path.join(self.cover_dir, f"temp_{self.book_id}.cover")
            try:
                time.sleep(random.uniform(1, 3))
                headers = {'Referer': 'https://kxmanhua.com/', 'User-Agent': self.get_random_ua()}
                with tqdm(total=1, desc="封面下载", unit="文件") as pbar:
                    response = requests.get(cover_url, headers=headers, timeout=20, stream=True)
                    with open(temp_path, 'wb') as f:
                        for chunk in response.iter_content(5120):
                            f.write(chunk)

                    with Image.open(temp_path) as img:
                        if img.mode in ('RGBA', 'LA'):
                            background = Image.new('RGB', img.size, (255, 255, 255))
                            background.paste(img, mask=img.split()[-1])
                            img = background
                        if img.mode != 'RGB':
                            img = img.convert('RGB')
                        img.save(self.cover_path, 'JPEG', quality=85, optimize=True, progressive=True, subsampling=0)

                    os.remove(temp_path)
                    pbar.update(1)
                    self.logger.info("封面下载并优化成功")
                    return
            except Exception as e:
                retry_count += 1
                wait = random.uniform(5, 25)
                self.logger.warning(f"封面下载失败第{retry_count}次，{wait:.1f}秒后重试: {str(e)}")
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass
                time.sleep(wait)

    def check_downloaded(self):
        """检查对比日志并返回是否需要下载"""
        # 使用双重锁确保日志操作的原子性
        with self.compare_log_lock:  # 类级锁保护整体日志操作
            # 读锁保证并发读
            if not os.path.exists(self.compare_log_path):
                self.download_status = "new"
                return False

            compare_data = {}
            try:
                with open(self.compare_log_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            data = json.loads(line.strip())
                            # 合并所有条目
                            for key, value in data.items():
                                compare_data[key] = value
                        except json.JSONDecodeError:
                            continue
            except Exception as e:
                self.logger.error(f"读取对比日志失败: {str(e)}")
                self.download_status = "new"
                return False

            book_id_str = str(self.book_id)
            if book_id_str in compare_data:
                record = compare_data[book_id_str]
                recorded_chapters = record.get('local_chapters', 0)
                self.log_record_max = recorded_chapters

                # 确定下载状态
                if self.website_chapters <= recorded_chapters:
                    self.download_status = "skip"
                    return True
                else:
                    self.download_status = "update"
                    return False
            else:
                self.download_status = "new"
                return False

    def update_compare_log(self):
        """更新对比日志（原子操作）"""
        with self.compare_log_lock:  # 类级锁保护整体日志操作
            compare_data = {}

            # 读取现有日志
            if os.path.exists(self.compare_log_path):
                try:
                    with open(self.compare_log_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            try:
                                data = json.loads(line.strip())
                                for key, value in data.items():
                                    compare_data[key] = value  # 只保留最新的
                            except json.JSONDecodeError:
                                continue
                except Exception as e:
                    self.logger.error(f"读取对比日志失败: {str(e)}")

            # 准备当前漫画的数据
            current_time = datetime.now().isoformat()
            final_pdf_path = self.get_final_pdf_path() if self.last_chapter > 0 else ""
            final_pdf_name = os.path.basename(final_pdf_path) if final_pdf_path else ""
            pdf_size = 0
            pdf_hash = ""
            if final_pdf_path and os.path.exists(final_pdf_path):
                try:
                    pdf_size = os.path.getsize(final_pdf_path)
                    with open(final_pdf_path, 'rb') as f:
                        pdf_hash = hashlib.md5(f.read()).hexdigest()
                except Exception as e:
                    self.logger.warning(f"获取PDF大小/哈希失败: {e}")

            book_id_str = str(self.book_id)
            compare_data[book_id_str] = {
                "comic_name": self.comic_name,
                "website_chapters": self.website_chapters,
                "local_chapters": self.last_chapter,
                "last_updated": current_time,
                "pdf_file": final_pdf_name,
                "pdf_size": pdf_size,
                "pdf_hash": pdf_hash
            }

            # 写入临时文件
            temp_path = self.compare_log_path + ".tmp"
            with open(temp_path, 'w', encoding='utf-8') as f:
                for book_id, data in compare_data.items():
                    json_line = json.dumps({book_id: data}, ensure_ascii=False)
                    f.write(json_line + '\n')

            # 原子替换
            if os.path.exists(self.compare_log_path):
                os.remove(self.compare_log_path)
            os.rename(temp_path, self.compare_log_path)

    def process_chapters(self):
        try:
            chapters = self.get_chapter_list()
            if not chapters:
                self.logger.error("未找到有效章节")
                return False

            self.website_chapters = len(chapters)
            self.first_chapter = chapters[0]['seq']
            self.last_chapter = chapters[-1]['seq']
            self.old_nas_pdf_name = None  # 重置旧PDF名称

            # 检查是否需要下载
            if self.check_downloaded():
                status_map = {
                    "skip": f"🟡 [{self.book_id}-{self.comic_name}] 跳过已下载内容(本地第{self.log_record_max}话)",
                    "update": f"🟠 [{self.book_id}-{self.comic_name}] 发现新章节({self.log_record_max + 1}-{self.last_chapter}话)"
                }
                with tqdm(total=1, desc=status_map[self.download_status], bar_format="{desc}") as pbar:
                    pbar.update(1)
                    time.sleep(0.5)

                    # 即使跳过也要更新网站章节数
                    self.update_compare_log()

                if self.download_status == "skip":
                    return True

            # 检查本地PDF是否存在
            local_final_pdf = self.get_final_pdf_path()
            if os.path.exists(local_final_pdf):
                self.logger.info(f"本地已存在完整PDF: {local_final_pdf}")
                # 检查NAS上是否存在
                if self.nas_config.get('enabled', False):
                    if self.check_nas_file_exists(os.path.basename(local_final_pdf)):
                        self.logger.info(f"NAS上已存在完整PDF: {os.path.basename(local_final_pdf)}")
                        return True
                    else:
                        return self.upload_to_nas(local_final_pdf)
                return True

            # ===== 分支1：首次下载 =====
            start_chapter = 1
            if self.download_status != "update" or self.log_record_max == 0:
                # 全量处理所有章节
                existing_pdfs, pending_chapters = self._check_existing_chapters(chapters, start_chapter)
                pdf_files = existing_pdfs.copy()
                self.logger.info(f"发现 {len(existing_pdfs)} 个已处理章节，剩余 {len(pending_chapters)} 个待处理章节")
                if pending_chapters:
                    with tqdm(total=len(pending_chapters), desc=f"📥 [{self.book_id}-{self.comic_name}] 章节下载", unit="章", bar_format='{desc}: {percentage:.0f}%|{bar}| {n_fmt}/{total_fmt}', colour='MAGENTA') as main_pbar:
                        for chapter in pending_chapters:
                            try:
                                pdf_path = self.process_chapter(chapter)
                                if pdf_path:
                                    pdf_files.append(pdf_path)
                                    main_pbar.update(1)
                            except Exception as e:
                                self.logger.error(f"章节处理失败: {chapter['title']}")
                                main_pbar.update(1)
                # 合并PDF及上传
                result = False
                if pdf_files:
                    with tqdm(total=2, desc=f"📦 [{self.book_id}-{self.comic_name}] PDF处理", unit="阶段", bar_format='{desc}: {percentage:.0f}%|{bar}|') as final_pbar:
                        final_pbar.set_description("首次合并PDF")
                        result = self.finalize_process_first(pdf_files)
                        final_pbar.update(1)
                        if result:
                            final_pdf = self.get_final_pdf_path()
                            upload_success = self.upload_to_nas(final_pdf)
                            final_pbar.update(1)
                            if upload_success:
                                self.logger.info("PDF上传NAS成功")
                            else:
                                self.logger.error("PDF上传NAS失败")
                                result = False
                if result:
                    self.clean_temp_files(pdf_files)
                    self.update_compare_log()
                return result

            # ===== 分支2：增量更新 =====
            if self.download_status == "update" and self.log_record_max > 0:
                start_chapter = self.log_record_max + 1
                nas_pdf_name = f"{self.comic_name}+({self.first_chapter}-{self.log_record_max}话)-开心看漫画({self.book_id}).pdf"
                self.old_nas_pdf_name = nas_pdf_name
                if not self.download_nas_file(nas_pdf_name):
                    self.logger.error("无法从NAS下载旧合并PDF，使用本地PDF合并")
                    local_old_pdf = os.path.join(self.final_pdf_dir, nas_pdf_name)
                    if os.path.exists(local_old_pdf):
                        self.logger.info(f"使用本地旧PDF: {local_old_pdf}")
                        nas_temp_path = os.path.join(self.nas_temp_dir, nas_pdf_name)
                        shutil.copy(local_old_pdf, nas_temp_path)
                existing_pdfs, pending_chapters = self._check_existing_chapters(chapters, start_chapter)
                pdf_files = existing_pdfs.copy()
                self.logger.info(f"发现 {len(existing_pdfs)} 个已处理章节，剩余 {len(pending_chapters)} 个待处理章节")
                if pending_chapters:
                    with tqdm(total=len(pending_chapters), desc=f"📥 [{self.book_id}-{self.comic_name}] 新章节下载", unit="章", bar_format='{desc}: {percentage:.0f}%|{bar}| {n_fmt}/{total_fmt}', colour='MAGENTA') as main_pbar:
                        for chapter in pending_chapters:
                            try:
                                pdf_path = self.process_chapter(chapter)
                                if pdf_path:
                                    pdf_files.append(pdf_path)
                                    main_pbar.update(1)
                            except Exception as e:
                                self.logger.error(f"章节处理失败: {chapter['title']}")
                                main_pbar.update(1)
                # 合并PDF及上传
                result = False
                if pdf_files:
                    with tqdm(total=2, desc=f"📦 [{self.book_id}-{self.comic_name}] PDF处理", unit="阶段", bar_format='{desc}: {percentage:.0f}%|{bar}|') as final_pbar:
                        final_pbar.set_description("增量合并PDF")
                        result = self.finalize_process_incremental(pdf_files)
                        final_pbar.update(1)
                        if result:
                            final_pdf = self.get_final_pdf_path()
                            upload_success = self.upload_to_nas(final_pdf)
                            final_pbar.update(1)
                            if upload_success:
                                self.logger.info("PDF上传NAS成功")
                                if self.old_nas_pdf_name:
                                    self.delete_nas_file(self.old_nas_pdf_name)
                            else:
                                self.logger.error("PDF上传NAS失败")
                                result = False
                if result:
                    self.clean_temp_files(pdf_files)
                    self.update_compare_log()
                return result
        except Exception as e:
            self.logger.error(f"主流程异常: {str(e)}")
            return False

    def get_final_pdf_path(self):
        safe_comic_name = self.safe_filename(self.comic_name, 30)
        return os.path.join(
            self.final_pdf_dir,
            f"{safe_comic_name}+({self.first_chapter}-{self.last_chapter}话)-开心看漫画({self.book_id}).pdf"
        )

    def _check_existing_chapters(self, chapters, start_chapter=1):
        existing_pdfs = []
        pending_chapters = []

        for chapter in chapters:
            if chapter['seq'] < start_chapter:
                continue  # 跳过不需要的章节

            pdf_name = f"{chapter['seq']:04d}-{self.comic_name}-{chapter['seq']:03d}-{self.book_id}.pdf"
            pdf_path = os.path.join(self.pdf_dir, pdf_name)

            if os.path.exists(pdf_path):
                existing_pdfs.append(pdf_path)
            else:
                pending_chapters.append(chapter)
        return existing_pdfs, pending_chapters

    def get_chapter_list(self):
        try:
            headers = {'Referer': f'https://kxmanhua.com/manga/{self.book_id}', 'User-Agent': self.get_random_ua()}
            html = self.fetch_html_with_retry(f"https://kxmanhua.com/manga/{self.book_id}", headers)
            soup = BeautifulSoup(html, 'lxml')
            raw_chapters = []
            for link in soup.select('div.chapter_list a[href], ul.chapter-list li a[href]'):
                try:
                    chapter_url = urljoin(f"https://kxmanhua.com/manga/{self.book_id}", link['href'])
                    raw_title = re.sub(r'\s+', ' ', link.get_text(strip=True))
                    clean_title = re.sub(r'[\\/*?:"<>|]', '', raw_title.split('-')[-1])[:20]
                    raw_chapters.append({'url': chapter_url, 'title': clean_title})
                except Exception as e:
                    self.logger.warning(f"章节解析失败: {e}")

            # 确保章节排序正确
            ordered_chapters = list(reversed(raw_chapters))

            # 分配序列号
            for idx, ch in enumerate(ordered_chapters, 1):
                ch['seq'] = idx

            return ordered_chapters
        except Exception as e:
            self.logger.error(f"获取章节失败: {e}")
            return []

    def process_chapter(self, chapter):
        # 使用安全的文件名
        safe_comic_name = self.safe_filename(self.comic_name, 30)
        safe_title = self.safe_filename(chapter['title'], 30)
        chapter_dir = os.path.join(self.raw_dir, f"{safe_comic_name}-{chapter['seq']:03d}-{safe_title}")
        os.makedirs(chapter_dir, exist_ok=True)
        pdf_name = f"{chapter['seq']:04d}-{safe_comic_name}-{chapter['seq']:03d}-{self.book_id}.pdf"
        pdf_path = os.path.join(self.pdf_dir, pdf_name)

        if os.path.exists(pdf_path):
            self.logger.info(f"跳过已存在章节: 第{chapter['seq']:03d}章")
            return pdf_path

        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 再次确保目录存在（多线程/重试安全）
                os.makedirs(chapter_dir, exist_ok=True)
                # 检查图片是否部分下载
                existing_imgs = [f for f in os.listdir(chapter_dir) if f.endswith('.jpg')]
                if existing_imgs:
                    self.logger.info(f"发现部分下载的图片 {len(existing_imgs)}张")

                with tqdm(desc=f"📖 [{self.book_id}-{self.comic_name}] 第{chapter['seq']:03d}章",
                          bar_format='{desc}: {percentage:.0f}%|{bar}| {n_fmt}/{total_fmt}',
                          leave=False) as pbar:
                    img_files = self.download_images(chapter, chapter_dir, pbar)

                if img_files:
                    return self.generate_pdf(chapter, img_files)
                else:
                    raise Exception("图片全部下载失败")
            except Exception as e:
                self.logger.error(f"章节处理失败(第{attempt+1}次): {chapter['title']} - {e}")
                # 新增：如果是找不到路径，强制重建目录再重试
                if isinstance(e, FileNotFoundError) or '[WinError 3]' in str(e):
                    os.makedirs(chapter_dir, exist_ok=True)
                if attempt == max_retries - 1:
                    raise
                else:
                    time.sleep(2)

    def download_images(self, chapter, save_dir, pbar):
        import threading
        headers = {'Referer': chapter['url'], 'User-Agent': self.get_random_ua()}
        html = self.fetch_html_with_retry(chapter['url'], headers)
        soup = BeautifulSoup(html, 'lxml')
        image_urls = [urljoin(chapter['url'], img['src']) for img in soup.select('div.blog__details__content img[src], div.reader-container img[src]')]
        pbar.reset(total=len(image_urls))
        thread_lock = threading.Lock()
        self._img_dl_threads = self.max_img_threads
        self._img_dl_fail = 0
        self._img_dl_succ = 0
        tasks = []
        for idx, url in enumerate(image_urls, 1):
            img_path = os.path.join(save_dir, f"{chapter['seq']:04d}-{idx:03d}.jpg")
            tasks.append((idx, url, img_path, chapter['url']))
        img_files = []
        fail_count = 0
        from concurrent.futures import ThreadPoolExecutor, as_completed
        def download_task(idx, url, img_path, referer):
            try:
                result_type, fail_reason = self.download_single_image_with_reason(url, img_path, referer, 1024, 5)
                if result_type and os.path.exists(result_type) and is_valid_image(result_type):
                    with thread_lock:
                        self._img_dl_succ += 1
                    return (idx, result_type, True, None)
                else:
                    with thread_lock:
                        self._img_dl_fail += 1
                    return (idx, None, False, fail_reason)
            except Exception as e:
                with thread_lock:
                    self._img_dl_fail += 1
                return (idx, None, False, str(e))
        with ThreadPoolExecutor(max_workers=self._img_dl_threads) as executor:
            futures = [executor.submit(download_task, idx, url, img_path, referer) for idx, url, img_path, referer in tasks]
            for future in as_completed(futures):
                idx, img_path, success, reason = future.result()
                if success:
                    img_files.append((idx, img_path))
                else:
                    fail_count += 1
                    tqdm.write(f"[图片失败]{reason} 章节{chapter['seq']} 图片{idx} URL: {image_urls[idx-1]}")
                pbar.update(1)
        # 需要传递章节线程数和BOOKID线程数
        chapter_threads = getattr(self, '_chapter_threads', None)
        bookid_threads = getattr(self, '_bookid_threads', None)
        pbar.set_postfix({
            "失败": fail_count,
            "图片线程": self._img_dl_threads,
            "章节线程": chapter_threads if chapter_threads else '-',
            "BOOKID线程": bookid_threads if bookid_threads else '-'
        })
        # 下载完后，按idx排序，重新编号为连续编号
        img_files.sort()
        final_img_files = []
        for new_idx, (old_idx, old_path) in enumerate(img_files, 1):
            new_path = os.path.join(save_dir, f"{chapter['seq']:04d}-{new_idx:03d}.jpg")
            if old_path != new_path:
                try:
                    os.rename(old_path, new_path)
                except Exception as e:
                    self.logger.warning(f"重命名图片失败: {old_path} -> {new_path}, {e}")
            final_img_files.append(new_path)
        if fail_count > 0:
            tqdm.write(f"章节{chapter['seq']}下载失败图片数: {fail_count}")
        return final_img_files

    def download_single_image_with_reason(self, url, save_path, referer, min_file_size, max_retries):
        temp_path = f"{save_path}.tmp"
        retry_count = 0
        min_width = 200
        min_height = 300
        last_exception = None
        fail_reason = None
        while retry_count < 5:  # 固定重试5次
            try:
                delay = random.uniform(5, 25)
                time.sleep(delay)
                headers = {'Referer': referer, 'User-Agent': self.get_random_ua()}
                with requests.get(url, headers=headers, stream=True, timeout=15) as response:
                    response.raise_for_status()
                    os.makedirs(os.path.dirname(save_path), exist_ok=True)
                    with open(temp_path, 'wb') as f:
                        for chunk in response.iter_content(5120):
                            f.write(chunk)
                    file_size = os.path.getsize(temp_path)
                    if file_size < min_file_size:
                        self.safe_remove(temp_path)
                        fail_reason = '体积过小'
                        return 'skip', fail_reason
                    self.stats['original_sizes'] += file_size  # 修复：累加原始图片体积
                    with Image.open(temp_path) as img:
                        img_width, img_height = img.size
                        if img_width < min_width or img_height < min_height:
                            self.safe_remove(temp_path)
                            fail_reason = '尺寸过小'
                            return 'skip', fail_reason
                        if img.mode in ('RGBA', 'LA'):
                            background = Image.new('RGB', img.size, (255, 255, 255))
                            background.paste(img, mask=img.split()[-1])
                            img = background
                        if img.mode != 'RGB':
                            img = img.convert('RGB')
                        img.save(temp_path, 'JPEG', quality=85, optimize=True, progressive=True, subsampling=0)
                    optimized_size = os.path.getsize(temp_path)
                    if optimized_size < min_file_size:
                        self.safe_remove(temp_path)
                        fail_reason = '优化后体积过小'
                        return 'skip', fail_reason
                    # 修复WinError 183：os.rename前先删除已存在的目标文件
                    if os.path.exists(save_path):
                        self.safe_remove(save_path)
                    # 新增：重命名时遇到WinError 32则重试
                    rename_retries = 5
                    for i in range(rename_retries):
                        try:
                            os.rename(temp_path, save_path)
                            break
                        except OSError as e:
                            if hasattr(e, 'winerror') and e.winerror == 32:
                                time.sleep(0.5)
                            else:
                                raise
                    else:
                        # 最终还是失败
                        self.safe_remove(temp_path)
                        fail_reason = '重命名时文件被占用'
                        return 'fail', fail_reason
                    self.stats['total_images'] += 1
                    self.stats['optimized_sizes'] += optimized_size
                    return save_path, None
            except Exception as e:
                last_exception = e
                retry_count += 1
                self.safe_remove(temp_path)
                self.safe_remove(save_path)
                fail_reason = str(e)
                wait = random.uniform(5, 25)
                if retry_count % 10 == 0:
                    self.logger.error(f"图片下载失败第{retry_count}次，{wait:.1f}秒后重试: {str(e)}")
                else:
                    self.logger.debug(f"图片下载失败第{retry_count}次，{wait:.1f}秒后重试: {str(e)}")
                time.sleep(wait)
        # 超过最大重试次数
        return 'fail', fail_reason

    def generate_pdf(self, chapter, img_files):
        try:
            safe_comic_name = self.safe_filename(self.comic_name, 30)
            pdf_name = f"{chapter['seq']:04d}-{safe_comic_name}-{chapter['seq']:03d}-{self.book_id}.pdf"
            pdf_path = os.path.join(self.pdf_dir, pdf_name)
            # 只保留jpg/png图片且有效
            img_files = [f for f in img_files if f.lower().endswith(('.jpg', '.jpeg', '.png')) and os.path.isfile(f) and is_valid_image(f)]
            if not img_files:
                self.logger.error(f"无有效图片文件用于生成PDF: {chapter['title']}")
                return None
            self.logger.debug(f"用于生成PDF的图片文件: {img_files}")
            # 新增调试：检查每个图片路径的存在性和有效性（去掉print，仅日志）
            for f in img_files:
                exists = os.path.exists(f)
                valid = is_valid_image(f)
                # print(f"检查图片: {f}, 存在: {exists}, 有效: {valid}")  # 已去除
            # 取消PDF生成进度条，改为文字提示
            try:
                tqdm.write(f"正在生成PDF: {pdf_path}")
                with open(pdf_path, 'wb') as f:
                    f.write(img2pdf.convert(img_files))
                tqdm.write(f"PDF生成完成: {pdf_path}")
            except Exception as e:
                print(f"img2pdf 生成PDF失败: {e}")
                self.logger.error(f"img2pdf 生成PDF失败: {e}")
                return None
            # 记录章节PDF体积和页数（只用有效图片）
            if not hasattr(self, 'chapter_pdf_sizes'):
                self.chapter_pdf_sizes = {}
            if not hasattr(self, 'chapter_pdf_pages'):
                self.chapter_pdf_pages = {}
            self.chapter_pdf_sizes[chapter['seq']] = os.path.getsize(pdf_path)
            self.chapter_pdf_pages[chapter['seq']] = len(img_files)
            return pdf_path
        except Exception as e:
            self.logger.error(f"PDF生成失败: {e}")
            self.logger.error(f"用于生成PDF的图片文件: {img_files}")
            return None

    def upload_to_nas(self, local_pdf_path, max_retries=3):
        """上传到NAS（带重试），成功后删除本地文件"""
        if not self.nas_config.get('enabled', False):
            return True

        basename = os.path.basename(local_pdf_path)
        remote_path = os.path.join(
            self.nas_config['target_dir'],
            basename
        ).replace('\\', '/')

        success = False
        with self.nas_upload_lock:  # 使用锁确保单一上传
            for attempt in range(max_retries):
                conn = None
                try:
                    conn = SMBConnection(
                        self.nas_config['username'],
                        self.nas_config['password'],
                        self.nas_config['client_name'],
                        self.nas_config['server_name'],
                        use_ntlm_v2=True
                    )

                    server_ip = self.nas_config['server_ip']
                    share_name = self.nas_config['share_name']

                    self.logger.debug(f"尝试连接NAS服务器: {server_ip}")
                    if not conn.connect(server_ip, 445):
                        self.logger.error("NAS连接失败")
                        continue

                    # 上传文件（带进度条）
                    file_size = os.path.getsize(local_pdf_path)
                    from tqdm import tqdm
                    with open(local_pdf_path, 'rb') as file:
                        with tqdm(total=file_size, unit='B', unit_scale=True, desc=f"上传NAS: {basename}") as pbar:
                            class ProgressFile:
                                def __init__(self, f, pbar):
                                    self.f = f
                                    self.pbar = pbar
                                def read(self, n):
                                    data = self.f.read(n)
                                    self.pbar.update(len(data))
                                    return data
                            progress_file = ProgressFile(file, pbar)
                            conn.storeFile(share_name, remote_path, progress_file)

                    # 验证上传
                    attrs = conn.getAttributes(share_name, remote_path)
                    local_size = os.path.getsize(local_pdf_path)
                    if attrs.file_size == local_size:
                        self.logger.info(f"上传NAS成功: {basename}")
                        success = True
                        break
                    else:
                        self.logger.warning(f"上传验证失败: 本地大小={local_size} NAS大小={attrs.file_size}")
                        continue

                except Exception as e:
                    wait = random.uniform(5, 25)
                    self.logger.error(f"上传异常(尝试{attempt + 1}/{max_retries}), {wait:.1f}秒后重试: {str(e)}")
                    traceback.print_exc()  # 打印详细异常到PyCharm控制台
                    time.sleep(wait)
                finally:
                    try:
                        if conn:
                            conn.close()
                    except Exception as e:
                        self.logger.error(f"关闭连接异常: {str(e)}")

        # 上传成功后删除本地文件
        if success:
            try:
                os.remove(local_pdf_path)
                self.logger.info(f"已删除本地PDF: {local_pdf_path}")
            except Exception as e:
                self.logger.error(f"删除本地PDF失败: {str(e)}")

        return success

    def download_nas_file(self, remote_file_name):
        """从NAS下载旧合并PDF（带进度条）"""
        if not self.nas_config.get('enabled', False):
            return False

        conn = None
        # 每个BOOKID一个子文件夹
        local_dir = os.path.join(self.nas_temp_dir, str(self.book_id))
        os.makedirs(local_dir, exist_ok=True)
        local_path = os.path.join(local_dir, remote_file_name)
        try:
            with self.nas_upload_lock:  # 使用锁确保单一连接
                conn = SMBConnection(
                    self.nas_config['username'],
                    self.nas_config['password'],
                    self.nas_config['client_name'],
                    self.nas_config['server_name'],
                    use_ntlm_v2=True
                )

                server_ip = self.nas_config['server_ip']
                share_name = self.nas_config['share_name']
                remote_path = os.path.join(
                    self.nas_config['target_dir'],
                    remote_file_name
                ).replace('\\', '/')

                if not conn.connect(server_ip, 445):
                    self.logger.error("NAS连接失败")
                    return False

                # 检查NAS文件是否存在
                try:
                    attrs = conn.getAttributes(share_name, remote_path)
                    if attrs.file_size == 0:
                        self.logger.error("NAS文件大小为0，无效")
                        return False
                    remote_file_size = attrs.file_size
                except:
                    self.logger.error(f"NAS文件不存在: {remote_path}")
                    return False

                # 下载文件（带进度条）
                from tqdm import tqdm
                with open(local_path, 'wb') as file:
                    with tqdm(total=remote_file_size, unit='B', unit_scale=True, desc=f"下载NAS: {remote_file_name}") as pbar:
                        def write_callback(data):
                            file.write(data)
                            pbar.update(len(data))
                        conn.retrieveFile(share_name, remote_path, file, callback=write_callback)
                self.logger.info(f"成功下载NAS文件到: {local_path}")
                return True

        except Exception as e:
            self.logger.error(f"下载NAS文件失败: {str(e)}")
            return False
        finally:
            try:
                if conn:
                    conn.close()
            except Exception as e:
                self.logger.error(f"关闭连接异常: {str(e)}")

    def check_nas_file_exists(self, filename):
        """检查文件是否存在于NAS"""
        if not self.nas_config.get('enabled', False):
            return False

        conn = None
        try:
            with self.nas_upload_lock:
                conn = SMBConnection(
                    self.nas_config['username'],
                    self.nas_config['password'],
                    self.nas_config['client_name'],
                    self.nas_config['server_name'],
                    use_ntlm_v2=True
                )

                server_ip = self.nas_config['server_ip']
                share_name = self.nas_config['share_name']
                remote_path = os.path.join(
                    self.nas_config['target_dir'],
                    filename
                ).replace('\\', '/')

                if not conn.connect(server_ip, 445):
                    self.logger.error("NAS连接失败")
                    return False

                # 检查文件是否存在
                try:
                    attrs = conn.getAttributes(share_name, remote_path)
                    return attrs.file_size > 0
                except:
                    return False

        except Exception as e:
            self.logger.error(f"检查NAS文件失败: {str(e)}")
            return False
        finally:
            try:
                if conn:
                    conn.close()
            except Exception as e:
                self.logger.error(f"关闭连接异常: {str(e)}")

    def delete_nas_file(self, remote_file_name, max_retries=3):
        """删除NAS上的旧PDF文件"""
        if not remote_file_name or not self.nas_config.get('enabled', False):
            return False

        conn = None
        try:
            with self.nas_upload_lock:  # 使用锁确保单一连接
                for attempt in range(max_retries):
                    try:
                        conn = SMBConnection(
                            self.nas_config['username'],
                            self.nas_config['password'],
                            self.nas_config['client_name'],
                            self.nas_config['server_name'],
                            use_ntlm_v2=True
                        )

                        server_ip = self.nas_config['server_ip']
                        share_name = self.nas_config['share_name']
                        remote_path = os.path.join(
                            self.nas_config['target_dir'],
                            remote_file_name
                        ).replace('\\', '/')

                        if not conn.connect(server_ip, 445):
                            self.logger.error("NAS连接失败")
                            time.sleep(random.uniform(5, 25))
                            continue

                        # 检查文件是否存在
                        try:
                            attrs = conn.getAttributes(share_name, remote_path)
                            if attrs.file_size > 0:
                                # 删除文件
                                conn.deleteFiles(share_name, remote_path)
                                self.logger.info(f"成功删除NAS旧PDF: {remote_file_name}")
                                return True
                            else:
                                self.logger.warning(f"NAS文件大小为0，跳过删除: {remote_file_name}")
                                return False
                        except:
                            self.logger.error(f"文件不存在: {remote_path}")
                            return False

                    except Exception as e:
                        wait = random.uniform(5, 25)
                        self.logger.error(f"删除失败(尝试{attempt + 1}/{max_retries}), {wait:.1f}秒后重试: {str(e)}")
                        time.sleep(wait)
                    finally:
                        try:
                            if conn:
                                conn.close()
                        except Exception as e:
                            self.logger.error(f"关闭连接异常: {str(e)}")
        except Exception as e:
            self.logger.error(f"删除NAS文件异常: {str(e)}")
        return False

    def finalize_process_first(self, pdf_files):
        from PyPDF2 import PdfMerger, PdfReader
        if not hasattr(self, 'merge_size_failures'):
            self.merge_size_failures = 0
        merger = PdfMerger()
        try:
            # 添加封面
            if os.path.exists(self.cover_path):
                import img2pdf
                from io import BytesIO
                cover_pdf_bytes = img2pdf.convert([
                    self.cover_path
                ], rotation=img2pdf.Rotation.ifvalid)
                merger.append(BytesIO(cover_pdf_bytes))
                self.logger.info("成功添加封面到PDF")
            # 添加章节PDF
            sorted_pdfs = sorted(pdf_files, key=lambda x: int(os.path.basename(x).split('-')[0]))
            for pdf in sorted_pdfs:
                if os.path.exists(pdf):
                    merger.append(pdf)
            # 创建最终PDF
            final_pdf = self.get_final_pdf_path()
            os.makedirs(os.path.dirname(final_pdf), exist_ok=True)
            merger.write(final_pdf)
            merger.close()
            # 校验体积和页数：合并后PDF体积需大于所有章节PDF体积总和的95%，页数等于所有章节有效图片数之和
            total_chapter_size = sum([self.chapter_pdf_sizes.get(int(os.path.basename(pdf).split('-')[0]), 0) for pdf in sorted_pdfs])
            total_chapter_pages = sum([self.chapter_pdf_pages.get(int(os.path.basename(pdf).split('-')[0]), 0) for pdf in sorted_pdfs])
            merged_size = os.path.getsize(final_pdf)
            from PyPDF2 import PdfReader
            merged_pages = len(PdfReader(final_pdf).pages)
            if total_chapter_size > 0 and merged_size < total_chapter_size * 0.95:
                self.merge_size_failures += 1
                self.logger.error(f"合并PDF体积校验失败: 合并体积={merged_size}, 章节总和={total_chapter_size}，失败次数={self.merge_size_failures}")
                if self.merge_size_failures >= 2:
                    self.logger.error("体积校验失败两次，进入全量下载模式。")
                    return self.full_merge_all_chapters(pdf_files)
                else:
                    return self.finalize_process_first(pdf_files)
            if merged_pages != total_chapter_pages + 1:  # +1为封面
                self.logger.error(f"合并PDF页数校验失败: 合并页数={merged_pages}, 章节图片总和+封面={total_chapter_pages + 1}")
                return False
            # 打印PDF信息
            self.merge_size_failures = 0
            self.print_pdf_info(final_pdf, self.book_id, mode='首次下载')
            return True
        except Exception as e:
            self.logger.error(f"首次合并失败: {e}")
            merger.close()
            return False

    def finalize_process_incremental(self, pdf_files):
        from PyPDF2 import PdfMerger, PdfReader
        if not hasattr(self, 'merge_size_failures'):
            self.merge_size_failures = 0
        merger = PdfMerger()
        try:
            # 先找NAS临时目录下的旧PDF
            nas_temp_files = [f for f in os.listdir(self.nas_temp_dir) if f.endswith(f'({self.book_id}).pdf')]
            base_files = []
            old_pdf_path = None
            if nas_temp_files:
                nas_file = sorted(nas_temp_files, key=lambda x: int(x.split('(')[1].split('-')[0]), reverse=True)[0]
                old_pdf_path = os.path.join(self.nas_temp_dir, nas_file)
                if os.path.exists(old_pdf_path):
                    merger.append(old_pdf_path)
                    base_files.append(old_pdf_path)
                    self.logger.info(f"添加NAS合并PDF: {nas_file}")
                else:
                    self.logger.warning("NAS临时文件不存在，跳过")
            # 添加新增章节PDF
            sorted_pdfs = sorted(pdf_files, key=lambda x: int(os.path.basename(x).split('-')[0]))
            for pdf in sorted_pdfs:
                if os.path.exists(pdf):
                    merger.append(pdf)
                    base_files.append(pdf)
            # 创建最终PDF
            final_pdf = self.get_final_pdf_path()
            os.makedirs(os.path.dirname(final_pdf), exist_ok=True)
            merger.write(final_pdf)
            merger.close()
            # 校验体积和页数：合并后PDF体积需大于（旧PDF体积+新增章节PDF体积总和）的95%，页数等于旧PDF页数+新增章节有效图片数
            old_size = os.path.getsize(old_pdf_path) if old_pdf_path and os.path.exists(old_pdf_path) else 0
            new_chapter_size = sum([self.chapter_pdf_sizes.get(int(os.path.basename(pdf).split('-')[0]), 0) for pdf in sorted_pdfs])
            new_chapter_pages = sum([self.chapter_pdf_pages.get(int(os.path.basename(pdf).split('-')[0]), 0) for pdf in sorted_pdfs])
            merged_size = os.path.getsize(final_pdf)
            merged_pages = len(PdfReader(final_pdf).pages)
            old_pages = 0
            if old_pdf_path and os.path.exists(old_pdf_path):
                old_pages = len(PdfReader(old_pdf_path).pages)
            if (old_size + new_chapter_size) > 0 and merged_size < (old_size + new_chapter_size) * 0.95:
                self.merge_size_failures += 1
                self.logger.error(f"增量合并PDF体积校验失败: 合并体积={merged_size}, 旧体积={old_size}, 新章节体积={new_chapter_size}，失败次数={self.merge_size_failures}")
                if self.merge_size_failures >= 2:
                    self.logger.error("体积校验失败两次，进入全量下载模式。")
                    # 全量下载：清空NAS旧PDF，合并所有章节PDF
                    return self.full_merge_all_chapters(pdf_files)
                else:
                    return self.finalize_process_incremental(pdf_files)
            if merged_pages != old_pages + new_chapter_pages:
                self.logger.error(f"增量合并PDF页数校验失败: 合并页数={merged_pages}, 旧页数={old_pages}, 新章节图片数={new_chapter_pages}")
                return False
            # 增量合并后只校验页数和体积
            self.merge_size_failures = 0
            if old_pdf_path and os.path.exists(old_pdf_path):
                old_reader = PdfReader(old_pdf_path)
                new_reader = PdfReader(final_pdf)
                old_pages = len(old_reader.pages)
                new_pages = len(new_reader.pages)
                new_size = os.path.getsize(final_pdf)
                # 打印PDF信息
                self.print_pdf_info(final_pdf, self.book_id, mode='增量更新', old_pdf_path=old_pdf_path)
                if new_pages == old_pages + len(sorted_pdfs) and new_size > old_size:
                    return True
                else:
                    self.logger.error(f"增量合并校验失败: 旧页数={old_pages}, 新页数={new_pages}, 新体积={new_size}, 旧体积={old_size}")
                    return False
            else:
                # 没有旧PDF时，按首次下载逻辑校验
                self.print_pdf_info(final_pdf, self.book_id, mode='增量更新')
                return True
        except Exception as e:
            self.logger.error(f"增量合并失败: {e}")
            merger.close()
            return False

    def full_merge_all_chapters(self, pdf_files):
        # 失败后全量下载：只合并所有章节PDF（不加NAS旧PDF）
        from PyPDF2 import PdfMerger
        merger = PdfMerger()
        try:
            # 添加封面
            if os.path.exists(self.cover_path):
                import img2pdf
                from io import BytesIO
                cover_pdf_bytes = img2pdf.convert([
                    self.cover_path
                ], rotation=img2pdf.Rotation.ifvalid)
                merger.append(BytesIO(cover_pdf_bytes))
                self.logger.info("[失败后全量下载]成功添加封面到PDF")
            # 添加所有章节PDF
            sorted_pdfs = sorted(pdf_files, key=lambda x: int(os.path.basename(x).split('-')[0]))
            for pdf in sorted_pdfs:
                if os.path.exists(pdf):
                    merger.append(pdf)
            final_pdf = self.get_final_pdf_path()
            os.makedirs(os.path.dirname(final_pdf), exist_ok=True)
            merger.write(final_pdf)
            merger.close()
            self.logger.info("[失败后全量下载]合并所有章节PDF完成")
            self.print_pdf_info(final_pdf, self.book_id, mode='失败后全量下载')
            self.merge_size_failures = 0
            return True
        except Exception as e:
            self.logger.error(f"[失败后全量下载]合并失败: {e}")
            merger.close()
            return False

    def print_pdf_info(self, pdf_path, book_id, mode='首次下载', old_pdf_path=None):
        from PyPDF2 import PdfReader
        try:
            reader = PdfReader(pdf_path)
            num_pages = len(reader.pages)
            size = os.path.getsize(pdf_path)
            bookmarks = self._count_bookmarks(reader)
            mode_str = f"[{mode}]"
            info = f"{mode_str} BOOKID={book_id} PDF: {os.path.basename(pdf_path)} 体积={size/1024/1024:.2f}MB 页数={num_pages} 书签数={bookmarks}"
            tqdm.write(info)
            if old_pdf_path and os.path.exists(old_pdf_path):
                old_reader = PdfReader(old_pdf_path)
                old_num_pages = len(old_reader.pages)
                old_size = os.path.getsize(old_pdf_path)
                old_bookmarks = self._count_bookmarks(old_reader)
                tqdm.write(f"[旧PDF] BOOKID={book_id} PDF: {os.path.basename(old_pdf_path)} 体积={old_size/1024/1024:.2f}MB 页数={old_num_pages} 书签数={old_bookmarks}")
        except Exception as e:
            tqdm.write(f"打印PDF信息失败: {e}")

    def _count_bookmarks(self, reader):
        # 递归统计书签数
        def count_outlines(outlines):
            count = 0
            for item in outlines:
                if isinstance(item, list):
                    count += count_outlines(item)
                else:
                    count += 1
            return count
        try:
            outlines = reader.outline
            return count_outlines(outlines) if outlines else 0
        except Exception:
            return 0

    def clean_temp_files(self, pdf_files):
        try:
            # 清理中间PDF
            for pdf in pdf_files:
                if os.path.exists(pdf):
                    os.remove(pdf)

            # 清理原始图片
            if os.path.exists(self.raw_dir):
                shutil.rmtree(self.raw_dir, ignore_errors=True)

            # 清理章节PDF目录
            if os.path.exists(self.pdf_dir):
                shutil.rmtree(self.pdf_dir, ignore_errors=True)

            self.logger.info("临时文件清理完成")
        except Exception as e:
            self.logger.error(f"清理异常: {str(e)}")

    def fetch_html_with_retry(self, url, headers):
        retry_count = 0
        while True:
            try:
                time.sleep(random.uniform(1, 3))
                response = requests.get(url, headers=headers, timeout=20)
                response.raise_for_status()
                return response.text
            except Exception as e:
                retry_count += 1
                wait = random.uniform(5, 25)
                self.logger.warning(f"请求失败第{retry_count}次，{wait:.1f}秒后重试")
                time.sleep(wait)

    def safe_remove(self, file_path):
        for _ in range(3):
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                break
            except:
                time.sleep(0.5)

    def get_file_hash(self, file_path):
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except:
            return "invalid"

    def print_statistics(self):
        if self.stats['total_images'] == 0:
            return
        tqdm.write("\n统计指标")
        tqdm.write("-" * 40)
        tqdm.write(f"{'总图片数':<20} {self.stats['total_images']:>15,}")
        tqdm.write(f"{'转换图片数':<20} {self.stats['converted_images']:>15,}")
        tqdm.write(f"{'原始总大小':<20} {self.stats['original_sizes'] / 1024 / 1024:>15.2f} MB")
        tqdm.write(f"{'优化后总大小':<20} {self.stats['optimized_sizes'] / 1024 / 1024:>15.2f} MB")
        tqdm.write(f"{'压缩率':<20} {(1 - self.stats['optimized_sizes'] / self.stats['original_sizes']) * 100:>15.1f}%")
        tqdm.write(f"{'失败下载数':<20} {self.stats['failed_downloads']:>15}")

    def record_failed_bookid(self, book_id):
        # 记录失败BOOKID到TXT文件，逗号分隔
        failed_file = os.path.join(self.log_dir, 'failed_bookids.txt')
        try:
            if os.path.exists(failed_file):
                with open(failed_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    failed_ids = set(content.split(',')) if content else set()
            else:
                failed_ids = set()
            failed_ids.add(str(book_id))
            with open(failed_file, 'w', encoding='utf-8') as f:
                f.write(','.join(sorted(failed_ids)))
        except Exception as e:
            self.logger.error(f"写入失败BOOKID日志失败: {e}")

    def remove_failed_bookid(self, book_id):
        # 下载成功后移除失败BOOKID
        failed_file = os.path.join(self.log_dir, 'failed_bookids.txt')
        try:
            if os.path.exists(failed_file):
                with open(failed_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    failed_ids = set(content.split(',')) if content else set()
                if str(book_id) in failed_ids:
                    failed_ids.remove(str(book_id))
                    with open(failed_file, 'w', encoding='utf-8') as f:
                        f.write(','.join(sorted(failed_ids)))
        except Exception as e:
            self.logger.error(f"移除失败BOOKID日志失败: {e}")

    # 在合并并上传NAS成功后调用
    def on_download_success(self):
        self.remove_failed_bookid(self.book_id)
        self.update_compare_log()


class TaskController:
    def __init__(self, max_concurrent=3, nas_config=None, max_img_threads=5, max_chapter_threads=5):
        self.max_concurrent = max_concurrent
        self.task_queue = []
        self.results = []
        self.lock = Lock()
        self.nas_config = nas_config or {}
        self.max_img_threads = max_img_threads
        self.max_chapter_threads = max_chapter_threads

    def add_tasks(self, book_ids, user_agents):
        for bid in book_ids:
            self.task_queue.append({
                'book_id': bid,
                'user_agents': user_agents,
                'add_time': time.time()
            })
            tqdm.write(f"🆕 新增下载任务: ID {bid}")

    def process_tasks(self):
        with ThreadPoolExecutor(max_workers=self.max_concurrent) as executor:
            futures = {}

            while self.task_queue or futures:
                while len(futures) < self.max_concurrent and self.task_queue:
                    task = self.task_queue.pop(0)
                    future = executor.submit(self._process_single_task, task)
                    futures[future] = task
                    time.sleep(1)  # 避免同时启动太多任务

                done_iter = as_completed(futures.keys())
                try:
                    while True:
                        future = next(done_iter, None)
                        if future is None:
                            break

                        task = futures.pop(future)
                        result = future.result()
                        self.results.append((task, result))

                        if self.task_queue and len(futures) < self.max_concurrent:
                            new_task = self.task_queue.pop(0)
                            new_future = executor.submit(self._process_single_task, new_task)
                            futures[new_future] = new_task
                except StopIteration:
                    pass

    def _process_single_task(self, task):
        start_time = time.time()
        downloader = ComicDownloader(
            book_id=task['book_id'],
            user_agents=task['user_agents'],
            nas_config=self.nas_config,
            max_img_threads=self.max_img_threads,
            max_chapter_threads=self.max_chapter_threads
        )
        try:
            success = downloader.process_chapters()
            downloader.print_statistics()
            return {
                'status': 'success' if success else 'partial',
                'duration': time.time() - start_time
            }
        except Exception as e:
            tqdm.write(f"❌ [{task['book_id']}] 处理失败: {str(e)}")
            return {'status': 'failed', 'duration': time.time() - start_time}
        finally:
            # 不再保留任何临时文件
            downloader.clean_temp_files([])


def kxmanhua_choose_and_fetch_bookids():
    CATEGORIES = {
        '开心看漫画': {
            '全站全部漫画': {
                '全部漫画': 'https://kxmanhua.com/manga/library?type=0&complete=1&page=1&orderby=1',
                '全部3D漫画': 'https://kxmanhua.com/manga/library?type=1&complete=1&page=1&orderby=1',
                '全部韩漫': 'https://kxmanhua.com/manga/library?type=2&complete=1&page=1&orderby=1',
                '全部日漫': 'https://kxmanhua.com/manga/library?type=3&complete=1&page=1&orderby=1',
                '全部真人漫画': 'https://kxmanhua.com/manga/library?type=4&complete=1&page=1&orderby=1',
                '全部耽美BL漫画': 'https://kxmanhua.com/manga/library?type=5&complete=1&page=1&orderby=1',
            },
            '全站完结漫画': {
                '全部完结漫画': 'https://kxmanhua.com/manga/library?type=0&complete=2&page=1&orderby=1',
                '全部完结3D漫画': 'https://kxmanhua.com/manga/library?type=1&complete=2&page=1&orderby=1',
                '全部完结韩漫': 'https://kxmanhua.com/manga/library?type=2&complete=2&page=1&orderby=1',
                '全部完结日漫': 'https://kxmanhua.com/manga/library?type=3&complete=2&page=1&orderby=1',
                '全部完结真人漫画': 'https://kxmanhua.com/manga/library?type=4&complete=2&page=1&orderby=1',
                '全部完结耽美BL漫画': 'https://kxmanhua.com/manga/library?type=5&complete=2&page=1&orderby=1',
            },
            '全站连载漫画': {
                '全部连载漫画': 'https://kxmanhua.com/manga/library?type=0&complete=3&page=1&orderby=1',
                '全部连载3D漫画': 'https://kxmanhua.com/manga/library?type=1&complete=3&page=1&orderby=1',
                '全部连载韩漫': 'https://kxmanhua.com/manga/library?type=2&complete=3&page=1&orderby=1',
                '全部连载日漫': 'https://kxmanhua.com/manga/library?type=3&complete=3&page=1&orderby=1',
                '全部连载真人漫画': 'https://kxmanhua.com/manga/library?type=4&complete=3&page=1&orderby=1',
                '全部连载耽美BL漫画': 'https://kxmanhua.com/manga/library?type=5&complete=3&page=1&orderby=1',
            },
        }
    }
    def input_with_options(prompt, options):
        for i, k in enumerate(options):
            tqdm.write(f"{i+1}. {k}")
        while True:
            try:
                idx = int(input(prompt)) - 1
                if 0 <= idx < len(options):
                    return idx, options[idx]
            except Exception:
                pass
            tqdm.write("输入有误，请重新输入。")
    first_keys = list(CATEGORIES['开心看漫画'].keys())
    idx, first_key = input_with_options("请选择一级分类: ", first_keys)
    second_keys = list(CATEGORIES['开心看漫画'][first_key].keys())
    idx2, second_key = input_with_options("请选择二级分类: ", second_keys)
    url = CATEGORIES['开心看漫画'][first_key][second_key]
    # 新增页码输入
    try:
        start_page = int(input("请输入起始页（默认1）: ").strip() or "1")
    except Exception:
        start_page = 1
    try:
        end_page = int(input("请输入结束页（0表示抓到没有为止，默认1）: ").strip() or "1")
    except Exception:
        end_page = 1
    tqdm.write(f"开始抓取：{first_key} - {second_key}，起始页:{start_page}，结束页:{end_page}")
    book_ids = fetch_all_bookids_kxmanhua(url, start_page, end_page)
    tqdm.write(f"共抓取到{len(book_ids)}个BOOKID")
    return book_ids

def fetch_all_bookids_kxmanhua(url, start_page=1, end_page=1):
    import re, random, time
    import requests
    from bs4 import BeautifulSoup
    def get_random_headers():
        return {
            'User-Agent': random.choice(USER_AGENTS),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8',
            'Connection': 'keep-alive',
            'Referer': 'https://kxmanhua.com/'
        }
    def parse_kxmanhua(html):
        soup = BeautifulSoup(html, 'lxml')
        uids = []
        for a in soup.select('div.product__item__pic[onclick]'):
            m = re.search(r"/manga/(\d+)", a['onclick'])
            if m:
                uids.append(m.group(1))
        return uids
    def get_max_page_kxmanhua(html):
        soup = BeautifulSoup(html, 'lxml')
        page = 1
        for a in soup.select('div.product__pagination a[title]'):
            m = re.search(r'第(\d+)页', a.get('title', ''))
            if m:
                page = max(page, int(m.group(1)))
        return page
    uids = []
    page = start_page
    max_page = 1
    while True:
        page_url = re.sub(r'page=\d+', f'page={page}', url)
        try:
            resp = requests.get(page_url, headers=get_random_headers(), timeout=15)
            resp.raise_for_status()
            html = resp.text
            if page == start_page:
                max_page = get_max_page_kxmanhua(html)
            uids_page = parse_kxmanhua(html)
            if not uids_page:
                break
            uids.extend(uids_page)
            tqdm.write(f"已抓取第{page}页，新增{len(uids_page)}个BOOKID")
            page += 1
            # 结束条件
            if end_page == 0:
                if page > max_page:
                    break
            else:
                if page > end_page:
                    break
        except Exception as e:
            tqdm.write(f"抓取失败: {page_url} - {e}")
            break
    return uids

def is_valid_image(path):
    try:
        with Image.open(path) as img:
            img.verify()
        return True
    except Exception:
        return False

if __name__ == "__main__":
    NAS_CONFIG = {
        'enabled': True,
        'server_ip': '**************',
        'server_name': 'NAS',
        'share_name': '小说',
        'username': 'admin',
        'password': 'ly120220',
        'target_dir': '/漫画/开心看漫画韩漫',
        'client_name': 'ComicDownloader'
    }
    USER_AGENTS = [
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.3249.1039 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/353.1.720279278 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/605.1.15 (Ecosia ios@11.0.2.2268)',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.9224.1328 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2719.1336 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.1798.1919 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.8853.1645 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1 Ddg/18.4',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.5891.1666 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.1961.1249 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:109.0) Gecko/20100101 Firefox/115.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/121.0.6167.138 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.9364.1479 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/294.0.588955078 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.3303.1187 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.4076.1486 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.6623.1159 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.4580.1463 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.6133.1363 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.5411.1984 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3798.1257 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.237.272 Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/360.1.737798518 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Mobile/15E148 Safari/604.1 Ddg/18.3',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/28.0 Chrome/130.0.0.0 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.8128.1783 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/*********',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/22D82 Twitter for iPhone/10.91',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.9254.1809 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:127.0) Gecko/20100101 Firefox/127.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/92.0.4515.90 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.1934.1183 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6.1 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (X11; Linux x86_64; rv:136.0) Gecko/20100101 Firefox/136.0',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.4869.1970 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_2_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.2533.1684 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.2078.1966 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.6572.1938 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPad; CPU OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.83 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPad; CPU OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.6904.1489 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.4673.1681 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.10 Safari/605.1.15',
        'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.7924.1815 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.1383.1917 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_7_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/131.0.6778.134 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPad; CPU OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.1047.1013 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.4262.1139 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.7338.1011 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.6897.1674 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.6580.1166 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/137.0  Mobile/15E148 Safari/605.1.15',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:136.0) Gecko/20100101 Firefox/136.0',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.9974.1464 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.4 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.1781.1399 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.6881.1258 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.3590.1632 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/137 Mobile/15E148 Version/15.0',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.1102.1564 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.4350.1896 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.9567.1498 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.1892.1577 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.4319.1409 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.1555.1494 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 YaBrowser/24.1.8.486.10 SA/3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.3933.1733 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.1191.1326 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.5350.1673 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2934.1490 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.3956.1532 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_7_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.8.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.5115.1285 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2682.1243 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.4230.1383 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.8832.1524 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.5385.1668 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2714.1548 Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Android 14; Mobile; rv:137.0) Gecko/137.0 Firefox/137.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/107.0.5304.66 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.8.1 Safari/605.1.15',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.83 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:135.0) Gecko/20100101 Firefox/135.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/132.0.6834.78 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.7619.1926 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.9773.1685 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.1629.1538 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.5160.1726 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.6505.1614 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.8191.1319 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.3814.1083 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/605.1.15 BingSapphire/31.2.430402003',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.6633.1045 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.8731.1840 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.5777.1497 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.7381.1845 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 Ddg/18.3',
        'Mozilla/5.0 (iPad; CPU OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.83 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.83 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.7824.1851 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.1598.1288 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.8146.1655 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.4114.1522 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.3232.1198 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2308.1021 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.3317.1149 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2296.1271 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/333.0.671582647 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.1387.1291 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Mobile/15E148 Safari/604.1 OPT/5.5.0',
        'Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0.1 Safari/605.1.15',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.9099.1037 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.3302.1402 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.9996.1335 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.7525.1386 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2317.1361 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.4891.1057 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.6117.1333 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.5431.1706 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; SM-G973U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2922.1448 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.9512.1691 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.8798.1867 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/124.0.6367.111 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.3341.1026 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.1369.1439 Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.4122.1586 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2921.1957 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 15_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.6110.1156 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.4779.1118 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.6630.1540 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 15_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.2 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.2 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.7369.1685 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.8520.1462 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/22D82 Twitter for iPhone/10.90',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2469.1901 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.4936.1160 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.4452.1451 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/362.0.741018905 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2732.1510 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.9782.1042 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.1591.1485 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.4102.1129 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6943.127 ADG/11.1.4805 Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.8792.1538 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.9616.1766 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.5954.1941 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.11 Safari/605.1.15',
        'Mozilla/5.0 (iPad; CPU OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_7_10 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.9121.1581 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/********* (Edition std-2)',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.1847.1578 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.8469.1900 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/137.0  Mobile/15E148 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.1158.1705 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/133.0.6943.120 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.6481.1219 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.1639.1109 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.6651.1925 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.9536.1764 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.7200.1531 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:136.0) Gecko/20100101 Firefox/136.0',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2554.1489 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.4841.1333 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:134.0) Gecko/20100101 Firefox/134.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.9552.1553 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/*********',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.1972.1490 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.8465.1065 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Avast/*********',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.7488.1336 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.6799.1857 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.5496.1071 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.6110.1819 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.9988.1427 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Mobile/15E148 Safari/604.1 Ddg/18.3',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.2 Safari/605.1.15',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/342.0.693598186 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.3633.1595 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.9931.1390 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.1414.1194 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.8517.1951 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.7100.1187 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.5244.1563 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.7235.1399 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.1.1; KFDOWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/108.16.2 like Chrome/108.0.5359.220 Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2652.1149 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.7951.1310 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.5823.1639 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:130.0) Gecko/20100101 Firefox/130.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.5704.1718 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/133.0.6943.120 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.6171.1906 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.3515.1462 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.3316.1643 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 OPR/********',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.8419.1071 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.2974.1730 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/22D82 Twitter for iPhone/10.83.2',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.7514.1426 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2619.1298 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/122.0.6261.62 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/134.0.6998.99 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (X11; Linux x86_64; rv:125.0) Gecko/20100101 Firefox/125.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/342.0.693598186 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.7 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.3144.1201 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.6759.1270 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.5498.1279 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.4407.1211 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_0_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_2_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.83 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.1289.1481 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.4884.1563 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.4205.1640 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.1915.1844 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.6598.1817 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.83 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.6448.1339 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.5940.1692 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.4694.1946 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/134.0.6998.99 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.9846.1973 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.8650.1649 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.6526.1309 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.83 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/114.0.5735.124 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.7271.1311 Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 18_4_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.1470.1951 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 [FBAN/FBIOS;FBAV/501.**********;FBBV/717317385;FBDV/iPhone12,8;FBMD/iPhone;FBSN/iOS;FBSV/18.3.2;FBSS/2;FBCR/;FBID/phone;FBLC/en_US;FBOP/80]',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.1986.1277 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/361.0.737942756 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/360.1.737798518 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 AVG/*********',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.1704.1172 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.8949.1410 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_7_10 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3154.1393 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) EdgiOS/134.0.3124.95 Version/18.0 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 12; SM-T500) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.88 Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.5052.1839 Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) EdgiOS/134.0.3124.95 Version/18.0 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/132.0.6834.78 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.4277.1250 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.8555.1919 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2228.1468 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6.1 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.1007.1579 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.8 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.1 Safari/605.1.15',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.83 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/119.0.6045.169 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.4681.1876 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.2 Mobile/15E148 Safari/605.1.15 BingSapphire/31.2.430402003',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) CMAC ********; Chrome/118.0.5993.119 Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2708.1875 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.1916.1170 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.4329.1844 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_0_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/133.0.6943.120 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0.1 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.1522.1128 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.4313.1254 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.3435.1443 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.8875.1496 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2628.1005 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.8869.1107 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.5 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.1678.1000 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.6942.1057 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/131.0.6778.73 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19582',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/362.0.741018905 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2860.1526 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.6285.1498 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.83 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.7200.1377 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.1383.1267 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.4213.1921 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.4907.1684 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/134.0.6998.99 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/135.0.7049.53 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.4564.1193 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/123.0.6312.52 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.9301.1782 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.8301.1045 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.4823.1292 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.7513.1954 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.3108.1882 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/359.2.735510536 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.4486.1408 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.8800.1258 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14816.131.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.1184.1873 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.2590.1079 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.7879.1821 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.7838.1904 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.2466.1062 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 15; oBv8GzsDB8; U; en) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6943.137 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.1062.1066 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 15) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile DuckDuckGo/5 Safari/537.36',
        'Mozilla/5.0 (Android 15; Mobile; rv:136.0) Gecko/136.0 Firefox/136.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/123.0.6312.52 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.8210.1389 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2997.1769 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.3355.1124 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.5593.1681 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Mobile Safari/537.36 OPR/********',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.7623.1842 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 14) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile DuckDuckGo/5 Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.3629.1546 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.4537.1538 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.9962.1259 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.7209.1514 Mobile Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.1.1; KFSUWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/108.16.2 like Chrome/108.0.5359.220 Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.4304.1028 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.7046.1498 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/134.0.6998.33 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.7420.1792 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.4681.1312 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.4245.1773 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.2 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.5095.1183 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.2 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) GSA/363.0.743255906 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.5013.1897 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.8793.1611 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.4402.1176 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.9288.1527 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2646.1518 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.7152.1265 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2213.1291 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.9450.1657 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.3 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 15_8_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.6 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.1539.1372 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.8924.1582 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.9263.1769 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.6644.1565 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.6861.1933 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.5459.1483 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.9058.1401 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2632.1425 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.3940.1328 Mobile Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.1780.1552 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.8719.1822 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.5981.1638 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.5707.1741 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2169.1250 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.1.2 Safari/605.1.15',
        'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.2255.1464 Mobile Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:138.0) Gecko/20100101 Firefox/138.0',
    ]
    MAX_CONCURRENT_TASKS = 5
    MAX_WORKERS_PER_TASK = 2
    # 新增：补充线程数变量定义，防止NameError
    MAX_IMG_THREADS_PER_CHAPTER = 5
    MAX_CHAPTER_THREADS_PER_BOOKID = 5
    tqdm.write("请选择操作方式：")
    tqdm.write("1. 手动输入BOOKID")
    tqdm.write("2. 通过菜单批量抓取开心看漫画BOOKID")
    mode = input("输入序号: ").strip()
    if mode == "2":
        book_ids = kxmanhua_choose_and_fetch_bookids()
        if not book_ids:
            tqdm.write("❌ 没有抓取到有效的BOOKID，程序退出")
            exit()
        from tqdm import tqdm
        tqdm.write(f"共获取到 {len(book_ids)} 个漫画：")
        valid_ids = []
        for book_id in tqdm(book_ids, desc="漫画检查", unit="本"):
            try:
                downloader = ComicDownloader(
                    book_id=book_id,
                    user_agents=USER_AGENTS,
                    nas_config=NAS_CONFIG
                )
                chapters = downloader.get_chapter_list()
                website_chapters = len(chapters)
                tqdm.write(f"[{book_id}] 发现 {website_chapters} 个章节")
                # 读取本地对比日志
                local_chapters = None
                comic_name = downloader.comic_name
                compare_log_path = downloader.compare_log_path
                if os.path.exists(compare_log_path):
                    with open(compare_log_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            try:
                                data = json.loads(line.strip())
                                if str(book_id) in data:
                                    local_chapters = data[str(book_id)].get('local_chapters', 0)
                                    comic_name = data[str(book_id)].get('comic_name', comic_name)
                            except:
                                continue
                if local_chapters is None:
                    local_chapters = 0
                tqdm.write(f"[{book_id}] 对比日志信息: 本地章节={local_chapters}, 网站章节={website_chapters}")
                if local_chapters == 0:
                    tqdm.write(f" - [需下载] {comic_name}+({1}-{website_chapters}话)-开心看漫画({book_id}) - 首次下载")
                    if website_chapters > 0:
                        valid_ids.append(int(book_id))
                elif website_chapters > local_chapters:
                    tqdm.write(f" - [需更新] {comic_name}+({local_chapters+1}-{website_chapters}话)-开心看漫画({book_id}) - 有新章节")
                    valid_ids.append(int(book_id))
                else:
                    tqdm.write(f" - [已跳过] {comic_name}+({1}-{website_chapters}话)-开心看漫画({book_id}) - 无需更新")
            except Exception as e:
                tqdm.write(f"[{book_id}] 检查异常: {e}")
        if not valid_ids:
            tqdm.write("没有需要下载或更新的漫画，程序退出")
            exit()
    else:
        input_ids = input("请输入漫画ID（多个用逗号分隔）: ").strip()
        book_ids = [bid.strip() for bid in input_ids.split(',') if bid.strip().isdigit()]
        if not book_ids:
            tqdm.write("❌ 没有输入有效的漫画ID，程序退出")
            exit()
        from tqdm import tqdm
        tqdm.write(f"共输入 {len(book_ids)} 个漫画：")
        valid_ids = []
        for book_id in tqdm(book_ids, desc="漫画检查", unit="本"):
            try:
                downloader = ComicDownloader(
                    book_id=book_id,
                    user_agents=USER_AGENTS,
                    nas_config=NAS_CONFIG
                )
                chapters = downloader.get_chapter_list()
                website_chapters = len(chapters)
                tqdm.write(f"[{book_id}] 发现 {website_chapters} 个章节")
                # 读取本地对比日志
                local_chapters = None
                comic_name = downloader.comic_name
                compare_log_path = downloader.compare_log_path
                if os.path.exists(compare_log_path):
                    with open(compare_log_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            try:
                                data = json.loads(line.strip())
                                if str(book_id) in data:
                                    local_chapters = data[str(book_id)].get('local_chapters', 0)
                                    comic_name = data[str(book_id)].get('comic_name', comic_name)
                            except:
                                continue
                if local_chapters is None:
                    local_chapters = 0
                tqdm.write(f"[{book_id}] 对比日志信息: 本地章节={local_chapters}, 网站章节={website_chapters}")
                if local_chapters == 0:
                    tqdm.write(f" - [需下载] {comic_name}+({1}-{website_chapters}话)-开心看漫画({book_id}) - 首次下载")
                    if website_chapters > 0:
                        valid_ids.append(int(book_id))
                elif website_chapters > local_chapters:
                    tqdm.write(f" - [需更新] {comic_name}+({local_chapters+1}-{website_chapters}话)-开心看漫画({book_id}) - 有新章节")
                    valid_ids.append(int(book_id))
                else:
                    tqdm.write(f" - [已跳过] {comic_name}+({1}-{website_chapters}话)-开心看漫画({book_id}) - 无需更新")
            except Exception as e:
                tqdm.write(f"[{book_id}] 检查异常: {e}")
        if not valid_ids:
            tqdm.write("没有需要下载或更新的漫画，程序退出")
            exit()
    controller = TaskController(
        max_concurrent=MAX_CONCURRENT_TASKS,
        nas_config=NAS_CONFIG,
        max_img_threads=MAX_IMG_THREADS_PER_CHAPTER,
        max_chapter_threads=MAX_CHAPTER_THREADS_PER_BOOKID
    )
    controller.add_tasks(valid_ids, USER_AGENTS)
    tqdm.write(f"\n🎯 已创建 {len(controller.task_queue)} 个下载任务，启动处理引擎...")
    controller.process_tasks()

    tqdm.write("\n任务完成报告:")
    status_icons = {
        'success': '✅ 成功',
        'partial': '⚠️ 部分成功',
        'failed': '❌ 失败'
    }
    for task, result in controller.results:
        desc = f"[{task['book_id']}] {status_icons[result['status']]} - 耗时: {result['duration']:.1f}秒"
        tqdm.write(desc)
    tqdm.write("\n所有任务处理完成！")