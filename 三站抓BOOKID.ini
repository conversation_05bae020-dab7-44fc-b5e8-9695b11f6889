import os
import sys
import requests
import re
import random
import time
from bs4 import BeautifulSoup
import shutil
from PIL import Image
from io import BytesIO
from tqdm import tqdm
import json
import threading
import queue
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import PyPDF2
from datetime import datetime
from smb.SMBConnection import SMBConnection
import socket
import tempfile

#################################################
# 配置区域
#################################################

# NAS配置 - 设置NAS连接参数
NAS_CONFIG = {
    'enabled': True,  # 是否启用NAS功能
    'server_ip': '**************',  # NAS服务器IP
    'server_name': 'D1581-fnos-91',  # NAS服务器名称
    'share_name': '小说',  # NAS共享名称
    'username': 'admin',  # NAS用户名
    'password': 'ly120220',  # NAS密码
    'target_dir': '漫画/三站全爬',  # NAS目标目录
    'client_name': 'ComicDownloader',  # 客户端名称
    'max_retries': 5,  # NAS操作最大重试次数
    'retry_delay': 3  # 重试间隔时间(秒)
}

# 并发配置 - 控制下载并发数
TOTAL_WORKERS = 3  # 同时下载的漫画数量
CHAPTER_WORKERS = 5  # 每本漫画的章节下载线程数
MAX_RETRIES = 3  # 下载重试次数

# 随机User-Agent列表 - 防止被网站封禁
USER_AGENTS = [
    # Windows Chrome
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.0.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",

    # Firefox
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.1) Gecko/20100101 Firefox/125.1",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.1) Gecko/20100101 Firefox/125.1",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:125.2) Gecko/20100101 Firefox/125.2",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:125.1) Gecko/20100101 Firefox/125.1",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",

    # Mac Safari
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.2.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.1 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.1.0 Safari/537.36",

    # Windows Edge
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.1.1.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/125.1.2.0 Safari/537.36",

    # Linux Chrome
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.1.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.2.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.0.1 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.1 Safari/537.36",

    # Mac Chrome
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.1.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.2.0.1 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.1 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.1.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.2.0 Safari/537.36",
]

#################################################
# 日志配置
#################################################

logging.basicConfig(
    level=logging.WARNING,  # 将默认日志级别改为WARNING，减少控制台输出
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comic_downloader.log', encoding='utf-8'),
        # 移除控制台处理器，避免干扰用户界面
        # logging.StreamHandler()
    ]
)
logger = logging.getLogger('ComicDownloader')
logger.setLevel(logging.INFO)  # 为我们的主日志保持INFO级别

# 降低SMB连接库的日志级别，减少控制台输出
logging.getLogger('SMB').setLevel(logging.WARNING)
logging.getLogger('SMB.SMBConnection').setLevel(logging.WARNING)

# 创建一个单独的文件处理器，确保所有日志都写入文件而不是控制台
file_handler = logging.FileHandler('comic_downloader.log', encoding='utf-8')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.handlers = [file_handler]  # 替换默认处理器

#################################################
# 全局锁
#################################################

# SMB连接锁 - 防止多线程同时使用SMB连接
smb_lock = threading.Lock()

# 日志锁 - 防止多线程同时写入日志文件
log_lock = threading.Lock()

# 章节处理锁 - 用于确保章节按顺序处理
chapter_lock = threading.RLock()


# 基础目录结构
def setup_directories(site):
    # 使用指定的主路径
    base_dir = r'D:\ComicsDownloads\三站漫画下载'
    site_dir = os.path.join(base_dir, site)

    # 创建主要目录结构
    dirs = {
        'raw': os.path.join(site_dir, '原始图片'),
        'cover_chapter_pdf': os.path.join(site_dir, '封面图及章节PDF'),
        'merged_pdf': os.path.join(site_dir, '合并PDF'),
        'logs': os.path.join(site_dir, '日志'),
        'nas_middleware': os.path.join(site_dir, 'NAS中间件')
    }

    # 创建所有目录
    for dir_path in dirs.values():
        os.makedirs(dir_path, exist_ok=True)

    # 创建封面目录
    cover_dir = os.path.join(dirs['raw'], f'{site}封面图')
    os.makedirs(cover_dir, exist_ok=True)

    return dirs


# 日志系统
class LogManager:
    def __init__(self, site):
        self.log_dir = os.path.join(r'D:\ComicsDownloads\三站漫画下载', site, '日志')
        os.makedirs(self.log_dir, exist_ok=True)
        self.bookid_log_path = os.path.join(self.log_dir, 'bookid_log.txt')
        self.compare_log_path = os.path.join(self.log_dir, f'{site}_compare_log.json')
        self.site = site

        # 初始化比较日志
        with log_lock:
            if not os.path.exists(self.compare_log_path):
                with open(self.compare_log_path, 'w', encoding='utf-8') as f:
                    json.dump({}, f, ensure_ascii=False, indent=2)

    def log_bookid(self, bookid, bookname):
        with log_lock:
            with open(self.bookid_log_path, 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {bookid} - {bookname}\n")

    def update_compare_log(self, bookid, bookname, local_chapters, remote_chapters, status='completed'):
        with log_lock:
            try:
                # 读取当前日志
                if os.path.exists(self.compare_log_path):
                    with open(self.compare_log_path, 'r', encoding='utf-8') as f:
                        try:
                            compare_log = json.load(f)
                        except json.JSONDecodeError:
                            logger.warning(f"比较日志格式错误，将重新创建: {self.compare_log_path}")
                            compare_log = {}
                else:
                    compare_log = {}

                # 更新日志
                compare_log[bookid] = {
                    'bookname': bookname,
                    'local_chapters': local_chapters,
                    'remote_chapters': remote_chapters,
                    'site': self.site,
                    'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'status': status
                }

                # 写入更新后的日志
                with open(self.compare_log_path, 'w', encoding='utf-8') as f:
                    json.dump(compare_log, ensure_ascii=False, indent=2, fp=f)

                logger.info(f"已更新比较日志: {bookid} - {bookname} - {local_chapters}/{remote_chapters}")
                return True
            except Exception as e:
                logger.error(f"更新比较日志失败: {bookid} - {e}")
                return False

    def check_update_needed(self, bookid, remote_chapters):
        with log_lock:
            try:
                if not os.path.exists(self.compare_log_path):
                    logger.info(f"比较日志不存在，需要首次下载: {bookid}")
                    return True, 0

                with open(self.compare_log_path, 'r', encoding='utf-8') as f:
                    try:
                        compare_log = json.load(f)
                    except json.JSONDecodeError:
                        logger.warning(f"比较日志格式错误，需要重新下载: {bookid}")
                        return True, 0

                if bookid in compare_log:
                    local_chapters = compare_log[bookid]['local_chapters']
                    if remote_chapters > local_chapters:
                        logger.info(f"需要更新: {bookid}, 本地章节: {local_chapters}, 远程章节: {remote_chapters}")
                        return True, local_chapters
                    else:
                        logger.info(f"无需更新: {bookid}, 本地章节: {local_chapters}, 远程章节: {remote_chapters}")
                        return False, local_chapters
                else:
                    logger.info(f"首次下载: {bookid}, 远程章节: {remote_chapters}")
                    return True, 0
            except Exception as e:
                logger.error(f"检查更新失败: {bookid} - {e}")
                return True, 0


# NAS连接管理
class NASManager:
    def __init__(self):
        self.config = NAS_CONFIG
        self.conn = None

    def connect(self):
        """连接到NAS，使用锁确保线程安全"""
        with smb_lock:
            if not self.config['enabled']:
                logger.info("NAS功能未启用")
                return False

            # 如果已经连接，先关闭
            if self.conn:
                try:
                    self.conn.close()
                except:
                    pass
                self.conn = None

            # 尝试连接，带重试机制
            retry_count = 0
            max_retries = self.config['max_retries']

            while retry_count <= max_retries:
                try:
                    self.conn = SMBConnection(
                        self.config['username'],
                        self.config['password'],
                        self.config['client_name'],
                        self.config['server_name'],
                        use_ntlm_v2=True
                    )
                    connected = self.conn.connect(self.config['server_ip'], 139)

                    if connected:
                        logger.debug(f"成功连接到NAS: {self.config['server_name']}")
                        return True
                    else:
                        logger.debug(f"NAS连接失败，重试 {retry_count + 1}/{max_retries}")
                        retry_count += 1
                        time.sleep(self.config['retry_delay'])
                except Exception as e:
                    logger.debug(f"NAS连接异常: {e}, 重试 {retry_count + 1}/{max_retries}")
                    retry_count += 1
                    time.sleep(self.config['retry_delay'])

            logger.error("NAS连接失败，已达到最大重试次数")
            return False

    def ensure_directory(self, path):
        """确保NAS上的目录存在，如不存在则创建"""
        with smb_lock:
            if not self.conn:
                if not self.connect():
                    return False

            path_parts = path.replace('\\', '/').split('/')
            current_path = ''

            for part in path_parts:
                if not part:
                    continue

                if current_path:
                    current_path = f"{current_path}/{part}"
                else:
                    current_path = part

                retry_count = 0
                max_retries = self.config['max_retries']
                success = False

                while retry_count <= max_retries and not success:
                    try:
                        # 尝试列出目录，检查是否存在
                        self.conn.listPath(self.config['share_name'], current_path)
                        success = True
                    except Exception:
                        # 目录不存在，尝试创建
                        try:
                            self.conn.createDirectory(self.config['share_name'], current_path)
                            logger.info(f"在NAS上创建目录: {current_path}")
                            success = True
                        except Exception as e:
                            retry_count += 1
                            if retry_count > max_retries:
                                logger.error(f"无法在NAS上创建目录 {current_path}: {e}")
                                return False
                            logger.warning(f"创建目录失败，重试 {retry_count}/{max_retries}: {current_path}")
                            time.sleep(self.config['retry_delay'])

                            # 重新连接NAS
                            if not self.connect():
                                return False
            return True

    def upload_file(self, local_path, remote_path):
        """上传文件到NAS，带重试机制"""
        with smb_lock:
            if not self.conn:
                if not self.connect():
                    return False

            retry_count = 0
            max_retries = self.config['max_retries']

            while retry_count <= max_retries:
                try:
                    remote_dir = os.path.dirname(remote_path).replace('\\', '/')
                    if not self.ensure_directory(remote_dir):
                        return False

                    with open(local_path, 'rb') as file_obj:
                        remote_filename = os.path.basename(remote_path)
                        remote_dir_path = os.path.dirname(remote_path).replace('\\', '/')
                        self.conn.storeFile(self.config['share_name'], f"{remote_dir_path}/{remote_filename}", file_obj)

                    logger.info(f"成功上传文件到NAS: {remote_path}")
                    return True
                except Exception as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logger.error(f"上传文件到NAS失败: {e}")
                        return False

                    logger.warning(f"上传文件失败，重试 {retry_count}/{max_retries}: {local_path}")
                    time.sleep(self.config['retry_delay'])

                    # 重新连接NAS
                    if not self.connect():
                        return False

    def download_file(self, remote_path, local_path):
        """从NAS下载文件，带重试机制"""
        with smb_lock:
            if not self.conn:
                if not self.connect():
                    return False

            retry_count = 0
            max_retries = self.config['max_retries']

            while retry_count <= max_retries:
                try:
                    os.makedirs(os.path.dirname(local_path), exist_ok=True)
                    with open(local_path, 'wb') as file_obj:
                        self.conn.retrieveFile(self.config['share_name'], remote_path.replace('\\', '/'), file_obj)

                    logger.info(f"成功从NAS下载文件: {remote_path} -> {local_path}")
                    return True
                except Exception as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logger.error(f"从NAS下载文件失败: {e}")
                        return False

                    logger.warning(f"下载文件失败，重试 {retry_count}/{max_retries}: {remote_path}")
                    time.sleep(self.config['retry_delay'])

                    # 重新连接NAS
                    if not self.connect():
                        return False

    def delete_file(self, remote_path):
        """删除NAS上的文件，带重试机制"""
        with smb_lock:
            if not self.conn:
                if not self.connect():
                    return False

            retry_count = 0
            max_retries = self.config['max_retries']

            while retry_count <= max_retries:
                try:
                    self.conn.deleteFiles(self.config['share_name'], remote_path.replace('\\', '/'))
                    logger.info(f"成功删除NAS上的文件: {remote_path}")
                    return True
                except Exception as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logger.error(f"删除NAS上的文件失败: {e}")
                        return False

                    logger.warning(f"删除文件失败，重试 {retry_count}/{max_retries}: {remote_path}")
                    time.sleep(self.config['retry_delay'])

                    # 重新连接NAS
                    if not self.connect():
                        return False

    def file_exists(self, remote_path):
        """检查NAS上的文件是否存在，带重试机制"""
        with smb_lock:
            if not self.conn:
                if not self.connect():
                    return False

            retry_count = 0
            max_retries = self.config['max_retries']

            while retry_count <= max_retries:
                try:
                    remote_dir = os.path.dirname(remote_path).replace('\\', '/')
                    remote_filename = os.path.basename(remote_path)
                    file_list = self.conn.listPath(self.config['share_name'], remote_dir)

                    for item in file_list:
                        if item.filename == remote_filename:
                            return True
                    return False
                except Exception as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logger.error(f"检查NAS文件存在性失败: {e}")
                        return False

                    logger.warning(f"检查文件存在性失败，重试 {retry_count}/{max_retries}: {remote_path}")
                    time.sleep(self.config['retry_delay'])

                    # 重新连接NAS
                    if not self.connect():
                        return False

    def list_files(self, remote_dir):
        """列出NAS上目录中的文件，带重试机制"""
        with smb_lock:
            if not self.conn:
                if not self.connect():
                    return []

            retry_count = 0
            max_retries = self.config['max_retries']

            while retry_count <= max_retries:
                try:
                    file_list = self.conn.listPath(self.config['share_name'], remote_dir.replace('\\', '/'))
                    return [item.filename for item in file_list if item.filename not in ['.', '..']]
                except Exception as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logger.error(f"列出NAS目录失败: {e}")
                        return []

                    logger.warning(f"列出目录失败，重试 {retry_count}/{max_retries}: {remote_dir}")
                    time.sleep(self.config['retry_delay'])

                    # 重新连接NAS
                    if not self.connect():
                        return []

    def close(self):
        """关闭NAS连接"""
        with smb_lock:
            if self.conn:
                try:
                    self.conn.close()
                except:
                    pass
                self.conn = None


# PDF处理工具
class PDFManager:
    @staticmethod
    def images_to_pdf(image_paths, output_pdf_path):
        """将多个图片转换为单个PDF文件"""
        try:
            if not image_paths:
                logger.error(f"没有图片可转换为PDF: {output_pdf_path}")
                return False

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_pdf_path), exist_ok=True)

            # 转换图片为PDF
            images = []
            for img_path in image_paths:
                try:
                    img = Image.open(img_path)
                    if img.mode == 'RGBA':
                        img = img.convert('RGB')
                    images.append(img)
                except Exception as e:
                    logger.error(f"处理图片失败 {img_path}: {e}")

            if not images:
                logger.error(f"没有有效图片可转换为PDF: {output_pdf_path}")
                return False

            # 保存第一张图片，然后追加其余图片
            images[0].save(
                output_pdf_path,
                save_all=True,
                append_images=images[1:] if len(images) > 1 else [],
                resolution=100.0
            )

            logger.info(f"成功创建PDF: {output_pdf_path}")
            return True
        except Exception as e:
            logger.error(f"创建PDF失败 {output_pdf_path}: {e}")
            return False

    @staticmethod
    def merge_pdfs(pdf_paths, output_path):
        """合并多个PDF文件，确保按正确顺序合并"""
        try:
            if not pdf_paths:
                logger.error(f"没有PDF可合并: {output_path}")
                return False

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 排序PDF路径 - 确保按章节顺序
            # 对于合并增量更新的情况，第一个文件是旧PDF，其余是新章节PDF
            # 新章节PDF命名格式: {bookname}-{chapter_index:03d}-{chapter_name}-{bookid}-{site}.pdf

            # 提取第一个PDF（可能是封面或旧的合并PDF）
            first_pdf = pdf_paths[0]
            chapter_pdfs = pdf_paths[1:]

            # 尝试按章节索引排序章节PDF
            try:
                def extract_chapter_index(pdf_path):
                    # 从文件名提取章节索引
                    filename = os.path.basename(pdf_path)
                    parts = filename.split('-')
                    if len(parts) >= 2:
                        try:
                            # 尝试提取章节索引，通常是第二个部分
                            return int(parts[1].lstrip('0') or '0')
                        except ValueError:
                            # 如果无法提取，返回一个大数字，确保排在后面
                            return 9999
                    return 9999

                # 按章节索引排序
                chapter_pdfs = sorted(chapter_pdfs, key=extract_chapter_index)
                logger.info(f"已按章节索引排序 {len(chapter_pdfs)} 个章节PDF")
            except Exception as e:
                logger.warning(f"章节PDF排序失败，使用原始顺序: {e}")

            # 重新组合PDF路径列表
            sorted_pdf_paths = [first_pdf] + chapter_pdfs

            merger = PyPDF2.PdfMerger()

            # 添加所有PDF文件
            for pdf in sorted_pdf_paths:
                try:
                    merger.append(pdf)
                    logger.debug(f"添加PDF: {os.path.basename(pdf)}")
                except Exception as e:
                    logger.error(f"添加PDF失败 {pdf}: {e}")

            # 写入合并后的PDF
            merger.write(output_path)
            merger.close()

            logger.info(f"成功合并PDF: {output_path}")
            return True
        except Exception as e:
            logger.error(f"合并PDF失败 {output_path}: {e}")
            return False


def get_random_headers(host=None, referer=None):
    headers = {
        'User-Agent': random.choice(USER_AGENTS),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Connection': 'keep-alive'
    }
    if host:
        headers['Host'] = host
    if referer:
        headers['Referer'] = referer
    return headers


# 分类结构
CATEGORIES = {
    '漫小肆漫画': {
        '全站全部漫画': {
            '全部漫画': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=-1&end=-1',
            '全部韩漫': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=1&end=-1',
            '全部日漫': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=2&end=-1',
            '全部台湾': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=3&end=-1',
        },
        '全站完结漫画': {
            '完结漫画': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=-1&end=1',
            '完结韩漫': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=1&end=1',
            '完结日漫': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=2&end=1',
            '完结台湾': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=3&end=1',
        },
        '全站连载漫画': {
            '连载漫画': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=-1&end=0',
            '连载韩漫': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=1&end=0',
            '连载日漫': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=2&end=0',
            '连载台湾': 'https://www.ikanwzd.cc/booklist?tag=%E5%85%A8%E9%83%A8&area=3&end=0',
        },
    },
    '开心看漫画': {
        '全站全部漫画': {
            '全部漫画': 'https://kxmanhua.com/manga/library?type=0&complete=1&page=1&orderby=1',
            '全部3D漫画': 'https://kxmanhua.com/manga/library?type=1&complete=1&page=1&orderby=1',
            '全部韩漫': 'https://kxmanhua.com/manga/library?type=2&complete=1&page=1&orderby=1',
            '全部日漫': 'https://kxmanhua.com/manga/library?type=3&complete=1&page=1&orderby=1',
            '全部真人漫画': 'https://kxmanhua.com/manga/library?type=4&complete=1&page=1&orderby=1',
            '全部耽美BL漫画': 'https://kxmanhua.com/manga/library?type=5&complete=1&page=1&orderby=1',
        },
        '全站完结漫画': {
            '全部完结漫画': 'https://kxmanhua.com/manga/library?type=0&complete=2&page=1&orderby=1',
            '全部完结3D漫画': 'https://kxmanhua.com/manga/library?type=1&complete=2&page=1&orderby=1',
            '全部完结韩漫': 'https://kxmanhua.com/manga/library?type=2&complete=2&page=1&orderby=1',
            '全部完结日漫': 'https://kxmanhua.com/manga/library?type=3&complete=2&page=1&orderby=1',
            '全部完结真人漫画': 'https://kxmanhua.com/manga/library?type=4&complete=2&page=1&orderby=1',
            '全部完结耽美BL漫画': 'https://kxmanhua.com/manga/library?type=5&complete=2&page=1&orderby=1',
        },
        '全站连载漫画': {
            '全部连载漫画': 'https://kxmanhua.com/manga/library?type=0&complete=3&page=1&orderby=1',
            '全部连载3D漫画': 'https://kxmanhua.com/manga/library?type=1&complete=3&page=1&orderby=1',
            '全部连载韩漫': 'https://kxmanhua.com/manga/library?type=2&complete=3&page=1&orderby=1',
            '全部连载日漫': 'https://kxmanhua.com/manga/library?type=3&complete=3&page=1&orderby=1',
            '全部连载真人漫画': 'https://kxmanhua.com/manga/library?type=4&complete=3&page=1&orderby=1',
            '全部连载耽美BL漫画': 'https://kxmanhua.com/manga/library?type=5&complete=3&page=1&orderby=1',
        },
    },
    '肉漫天堂': {
        '全站全部漫画': {
            '全部漫画': 'https://rmtt7.com/comics-cata',
            '全部韩漫': 'https://rmtt7.com/comics-cata/%E9%9F%A9%E6%BC%AB/ob/time/st/all',
            '全部日漫': 'https://rmtt7.com/comics-cata/%E6%97%A5%E6%BC%AB/ob/time/st/all',
            '全部3D漫画': 'https://rmtt7.com/comics-cata/3D%E6%BC%AB%E7%94%BB/ob/time/st/all',
            '全部真人漫画': 'https://rmtt7.com/comics-cata/%E7%9C%9F%E4%BA%BA/ob/time/st/all',
            '全部短篇': 'https://rmtt7.com/comics-cata/%E7%9F%AD%E7%AF%87/ob/time/st/all',
        },
        '全站完结漫画': {
            '全部完结漫画': 'https://rmtt7.com/comics-cata/all/ob/time/st/completed',
            '全部完结韩漫': 'https://rmtt7.com/comics-cata/%E9%9F%A9%E6%BC%AB/ob/time/st/completed',
            '全部完结日漫': 'https://rmtt7.com/comics-cata/%E6%97%A5%E6%BC%AB/ob/time/st/completed',
            '全部完结3D漫画': 'https://rmtt7.com/comics-cata/3D%E6%BC%AB%E7%94%BB/ob/time/st/completed',
            '全部完结真人漫画': 'https://rmtt7.com/comics-cata/%E7%9C%9F%E4%BA%BA/ob/time/st/completed',
            '全部完结短篇': 'https://rmtt7.com/comics-cata/%E7%9F%AD%E7%AF%87/ob/time/st/completed',
        },
        '全站连载漫画': {
            '全部连载漫画': 'https://rmtt7.com/comics-cata/all/ob/time/st/serialized',
            '全部连载韩漫': 'https://rmtt7.com/comics-cata/%E9%9F%A9%E6%BC%AB/ob/time/st/serialized',
            '全部连载日漫': 'https://rmtt7.com/comics-cata/%E6%97%A5%E6%BC%AB/ob/time/st/serialized',
            '全部连载3D漫画': 'https://rmtt7.com/comics-cata/3D%E6%BC%AB%E7%94%BB/ob/time/st/serialized',
            '全部连载真人漫画': 'https://rmtt7.com/comics-cata/%E7%9C%9F%E4%BA%BA/ob/time/st/serialized',
            '全部连载短篇': 'https://rmtt7.com/comics-cata/%E7%9F%AD%E7%AF%87/ob/time/st/serialized',
        },
    },
}


# 选择分类

def choose_category():
    def input_with_options(prompt, options):
        print("\n" + "-" * 40)
        for i, k in enumerate(options):
            print(f"{i + 1}. {k}")
        print("-" * 40)

        while True:
            try:
                choice = input(prompt + " ")
                idx = int(choice) - 1
                if 0 <= idx < len(options):
                    return idx, options[idx]
            except Exception:
                pass
            print("输入有误，请重新输入。")

    # 一级分类
    while True:
        print("\n选择网站:")
        first_keys = list(CATEGORIES.keys())
        first_keys_show = ["全部"] + first_keys + ["退出"]
        idx, first_key = input_with_options("请输入序号:", first_keys_show)
        if first_key == "退出":
            sys.exit(0)
        if first_key == "全部":
            # 返回所有站点的全部分类（不包含二级、三级的'全部'、'返回上一级'、'退出'）
            all_tasks = []
            for site in CATEGORIES:
                for second in CATEGORIES[site]:
                    for third in CATEGORIES[site][second]:
                        # 只抓取具体分类
                        all_tasks.append((site, second, third, CATEGORIES[site][second][third]))
            return all_tasks, True
        # 二级分类
        while True:
            print(f"\n选择分类 ({first_key}):")
            second_keys = list(CATEGORIES[first_key].keys())
            second_keys_show = ["全部", "输入BOOKID"] + second_keys + ["返回上一级", "退出"]
            idx2, second_key = input_with_options("请输入序号:", second_keys_show)
            if second_key == "退出":
                sys.exit(0)
            if second_key == "返回上一级":
                break
            if second_key == "全部":
                all_tasks = []
                for second in CATEGORIES[first_key]:
                    for third in CATEGORIES[first_key][second]:
                        all_tasks.append((first_key, second, third, CATEGORIES[first_key][second][third]))
                return all_tasks, True

            # 处理"输入BOOKID"选项
            if second_key == "输入BOOKID":
                while True:
                    bookid = input("\n请输入BOOKID (输入q返回): ").strip()
                    if bookid.lower() == 'q':
                        break
                    if bookid.isdigit():
                        # 返回特殊格式，表示直接下载指定BOOKID
                        return [(first_key, "直接下载", "输入BOOKID", bookid)], False
                    else:
                        print("BOOKID必须是数字，请重新输入。")
                continue

            # 三级分类
            while True:
                print(f"\n选择子分类 ({second_key}):")
                third_keys = list(CATEGORIES[first_key][second_key].keys())
                third_keys_show = ["全部", "设置页数"] + third_keys + ["返回上一级", "退出"]
                idx3, third_key = input_with_options("请输入序号:", third_keys_show)
                if third_key == "退出":
                    sys.exit(0)
                if third_key == "返回上一级":
                    break
                if third_key == "全部":
                    all_tasks = []
                    for third in CATEGORIES[first_key][second_key]:
                        all_tasks.append((first_key, second_key, third, CATEGORIES[first_key][second_key][third]))
                    return all_tasks, True

                # 处理"设置页数"选项
                if third_key == "设置页数":
                    try:
                        start_page_input = input("\n请输入起始页码: ").strip()
                        if not start_page_input.isdigit() or int(start_page_input) < 1:
                            print("起始页码必须是大于等于1的整数，请重新选择。")
                            continue
                        start_page = int(start_page_input)

                        end_page_input = input("请输入结束页码 (0表示抓取到最后): ").strip()
                        if not end_page_input.isdigit() or int(end_page_input) < 0:
                            print("结束页码必须是大于等于0的整数，请重新选择。")
                            continue
                        end_page = int(end_page_input)

                        print(f"\n已设置页码范围: {start_page} - {end_page if end_page > 0 else '最后'}")

                        # 选择要应用页码设置的分类
                        print(f"\n选择要应用页码设置的子分类:")
                        third_keys_for_pages = third_keys + ["全部"]
                        idx4, selected_third = input_with_options("请输入序号:", third_keys_for_pages)

                        if selected_third == "全部":
                            all_tasks = []
                            for third in CATEGORIES[first_key][second_key]:
                                url = CATEGORIES[first_key][second_key][third]
                                # 添加页码信息
                                all_tasks.append((first_key, second_key, third, url, start_page, end_page))
                            return all_tasks, True
                        else:
                            url = CATEGORIES[first_key][second_key][selected_third]
                            # 返回带页码信息的任务
                            return [(first_key, second_key, selected_third, url, start_page, end_page)], False
                    except Exception as e:
                        print(f"设置页码时发生错误: {e}")
                        continue

                url = CATEGORIES[first_key][second_key][third_key]
                return [(first_key, second_key, third_key, url)], False


# 站点解析器基类和实现

class SiteParser:
    """站点解析器基类，定义通用接口"""

    def __init__(self):
        self.name = "未知站点"
        self.host = ""
        self.referer = ""

    def get_page_url(self, url, page):
        """获取分页URL"""
        return url

    def get_max_page(self, html):
        """获取最大页数"""
        return 1

    def parse_bookids(self, html):
        """解析页面中的漫画ID"""
        return []

    def get_detail_url(self, bookid):
        """获取漫画详情页URL"""
        return ""

    def get_chapter_url(self, bookid, chapter_id):
        """获取章节URL"""
        return ""

    def parse_comic_name(self, html):
        """解析漫画名称"""
        return ""

    def parse_chapters(self, html):
        """解析章节列表"""
        return []

    def parse_image_urls(self, html):
        """解析图片URL列表"""
        return []


class ManxisiParser(SiteParser):
    """漫小肆漫画解析器"""

    def __init__(self):
        super().__init__()
        self.name = "漫小肆漫画"
        self.host = "www.ikanwzd.cc"
        self.referer = "https://www.ikanwzd.cc/"

    def get_page_url(self, url, page):
        if page == 1:
            return url
        else:
            if '?' in url:
                page_url = re.sub(r'(\?|&)page=\d+', '', url)
                page_url += f'&page={page}'
            else:
                page_url = url + f'?page={page}'
            return page_url

    def get_max_page(self, html):
        soup = BeautifulSoup(html, 'lxml')
        page = 1
        for a in soup.select('footer .pagination li a[title]'):
            m = re.search(r'第(\d+)页', a.get('title', ''))
            if m:
                page = max(page, int(m.group(1)))
        return page

    def parse_bookids(self, html):
        soup = BeautifulSoup(html, 'lxml')
        uids = []
        for a in soup.select('ul.mh-list.col7 li .mh-item a[href^="/book/"]'):
            m = re.search(r'/book/(\d+)', a['href'])
            if m:
                uids.append(m.group(1))
        return uids

    def get_detail_url(self, bookid):
        return f"https://www.ikanwzd.cc/book/{bookid}"

    def get_chapter_url(self, bookid, chapter_id):
        return f"https://www.ikanwzd.cc/chapter/{bookid}/{chapter_id}"

    def parse_comic_name(self, html):
        soup = BeautifulSoup(html, 'lxml')
        title = soup.select_one('div.banner_detail_form div.info h1')
        if title:
            return title.text.strip()
        return ""

    def parse_chapters(self, html):
        soup = BeautifulSoup(html, 'lxml')
        chapter_list = []

        # 漫小肆漫画的章节是正序排列
        chapter_items = soup.select('ul#detail-list-select li')

        # 如果找不到章节，尝试其他选择器
        if not chapter_items:
            chapter_items = soup.select('div.detail-list-select li')
        if not chapter_items:
            chapter_items = soup.select('div.detail-list li')

        for idx, li in enumerate(chapter_items):
            a = li.find('a')
            if a and a.has_attr('href'):
                chap_url = 'https://www.ikanwzd.cc' + a['href']
                chap_name = a.text.strip().replace('/', '_').replace('\\', '_')
                chap_id = a['href'].split('/')[-1]
                chapter_list.append({
                    'index': idx + 1,
                    'name': chap_name,
                    'url': chap_url,
                    'id': chap_id
                })

        return chapter_list

    def parse_image_urls(self, html, chapter_url):
        soup = BeautifulSoup(html, 'lxml')
        image_urls = []

        img_tags = soup.select('div.comicpage img')

        for i, img_tag in enumerate(img_tags):
            img_url = img_tag.get('data-original') or img_tag.get('src')
            if not img_url:
                continue
            if not img_url.startswith('http'):
                img_url = 'https://www.wzd1.cc' + img_url
            image_urls.append((i + 1, img_url))

        return image_urls


class KxmanhuaParser(SiteParser):
    """开心看漫画解析器"""

    def __init__(self):
        super().__init__()
        self.name = "开心看漫画"
        self.host = "kxmanhua.com"
        self.referer = "https://kxmanhua.com/"

    def get_page_url(self, url, page):
        return re.sub(r'page=\d+', f'page={page}', url)

    def get_max_page(self, html):
        soup = BeautifulSoup(html, 'lxml')
        page = 1
        for a in soup.select('div.product__pagination a[title]'):
            m = re.search(r'第(\d+)页', a.get('title', ''))
            if m:
                page = max(page, int(m.group(1)))
        return page

    def parse_bookids(self, html):
        soup = BeautifulSoup(html, 'lxml')
        uids = []
        for a in soup.select('div.product__item__pic[onclick]'):
            m = re.search(r"/manga/(\d+)", a['onclick'])
            if m:
                uids.append(m.group(1))
        return uids

    def get_detail_url(self, bookid):
        return f"https://kxmanhua.com/manga/{bookid}"

    def get_chapter_url(self, bookid, chapter_id):
        return f"https://kxmanhua.com/chapter/{bookid}/{chapter_id}"

    def parse_comic_name(self, html):
        soup = BeautifulSoup(html, 'lxml')
        title = soup.select_one('div.anime__details__title h3')
        if title:
            return title.text.strip()
        return ""

    def parse_chapters(self, html):
        soup = BeautifulSoup(html, 'lxml')
        chapter_list = []

        # 开心看漫画的章节是倒序排列，需要反转
        temp_list = []
        chapter_links = soup.select('div.chapter_list a')

        # 如果找不到章节，尝试其他选择器
        if not chapter_links:
            chapter_links = soup.select('.chapter-list a')
        if not chapter_links:
            chapter_links = soup.select('.anime__details__episodes a')

        for a in chapter_links:
            if a.has_attr('href'):
                chap_url = 'https://kxmanhua.com' + a['href']
                chap_name = a.text.strip().replace('/', '_').replace('\\', '_')
                chap_id = a['href'].split('/')[-1]
                temp_list.append({
                    'name': chap_name,
                    'url': chap_url,
                    'id': chap_id
                })

        # 开心看漫画是倒序，需要反转
        temp_list.reverse()
        for idx, chapter in enumerate(temp_list):
            chapter['index'] = idx + 1
            chapter_list.append(chapter)

        return chapter_list

    def parse_image_urls(self, html, chapter_url):
        soup = BeautifulSoup(html, 'lxml')
        image_urls = []

        img_tags = soup.select('div.blog__details__content img')

        for i, img_tag in enumerate(img_tags):
            img_url = img_tag.get('src')
            if not img_url:
                continue
            if not img_url.startswith('http'):
                img_url = 'https:' + img_url if img_url.startswith('//') else 'https://kxmanhua.com' + img_url
            image_urls.append((i + 1, img_url))

        return image_urls


class RmttParser(SiteParser):
    """肉漫天堂解析器"""

    def __init__(self):
        super().__init__()
        self.name = "肉漫天堂"
        self.host = "rmtt7.com"
        self.referer = "https://rmtt7.com/"

    def get_page_url(self, url, page):
        if page == 1:
            return url
        else:
            if url.endswith('/all') or url.endswith('/completed') or url.endswith('/serialized'):
                page_url = url + f'/page/{page}'
            else:
                page_url = re.sub(r'/page/\d+$', '', url) + f'/page/{page}'
            return page_url

    def get_max_page(self, html):
        soup = BeautifulSoup(html, 'lxml')
        page = 1
        for a in soup.select('ul.page li a'):
            try:
                p = int(a.text.strip())
                page = max(page, p)
            except:
                continue
        return page

    def parse_bookids(self, html):
        soup = BeautifulSoup(html, 'lxml')
        uids = []
        for a in soup.select('a.vodlist_thumb[href^="/comics-detail/"]'):
            m = re.search(r'/comics-detail/(\d+)', a['href'])
            if m:
                uids.append(m.group(1))
        return uids

    def get_detail_url(self, bookid):
        return f"https://rmtt7.com/comics-detail/{bookid}"

    def get_chapter_url(self, bookid, chapter_id):
        return f"https://rmtt7.com/comics-read/{bookid}/{chapter_id}"

    def parse_comic_name(self, html):
        soup = BeautifulSoup(html, 'lxml')
        title = soup.select_one('h1.title')
        if title:
            return title.text.strip()
        return ""

    def parse_chapters(self, html):
        soup = BeautifulSoup(html, 'lxml')
        chapter_list = []

        # 肉漫天堂的章节是正序排列
        chapter_links = soup.select('ul.content_playlist li a')

        # 如果找不到章节，尝试其他选择器
        if not chapter_links:
            chapter_links = soup.select('.hl-plays-list a')
        if not chapter_links:
            chapter_links = soup.select('.hl-plays-from a')
        if not chapter_links:
            chapter_links = soup.select('a[href*="/comics-read/"]')

        for idx, a in enumerate(chapter_links):
            if a.has_attr('href'):
                chap_url = 'https://rmtt7.com' + a['href']
                chap_name = a.text.strip().replace('/', '_').replace('\\', '_')
                chap_id = a['href'].split('/')[-1]
                chapter_list.append({
                    'index': idx + 1,
                    'name': chap_name,
                    'url': chap_url,
                    'id': chap_id
                })

        return chapter_list

    def parse_image_urls(self, html, chapter_url):
        soup = BeautifulSoup(html, 'lxml')
        image_urls = []

        img_tags = soup.select('div[style*="width:700px"] img')

        # 如果没有找到图片，尝试其他选择器
        if not img_tags:
            img_tags = soup.select('.hl-content-text img')
        if not img_tags:
            img_tags = soup.select('.hl-fix-content img')
        if not img_tags:
            img_tags = soup.select('img[data-original]')

        for i, img_tag in enumerate(img_tags):
            img_url = img_tag.get('data-original') or img_tag.get('src')
            if not img_url:
                continue
            if img_url.startswith('data:image'):
                continue  # 跳过base64图片
            if not img_url.startswith('http'):
                img_url = 'https:' + img_url if img_url.startswith('//') else 'https://rmtt7.com' + img_url
            image_urls.append((i + 1, img_url))

        return image_urls


# 创建解析器工厂
def create_parser(site):
    """根据站点名称创建对应的解析器"""
    if site == '漫小肆漫画':
        return ManxisiParser()
    elif site == '开心看漫画':
        return KxmanhuaParser()
    elif site == '肉漫天堂':
        return RmttParser()
    else:
        raise ValueError(f"不支持的站点: {site}")


# 主抓取逻辑

def fetch_all_bookids(site, url):
    uids = []
    page = 1
    max_page = 1

    # 创建对应的站点解析器
    parser = create_parser(site)

    print(f"\n正在抓取 {site} 的漫画ID...")

    with tqdm(total=None, desc="页面抓取", unit="页") as pbar:
        while True:
            try:
                # 使用解析器获取当前页的URL
                page_url = parser.get_page_url(url, page)

                # 发送请求
                resp = requests.get(
                    page_url,
                    headers=get_random_headers(parser.host, parser.referer),
                    timeout=15
                )
                resp.raise_for_status()
                html = resp.text

                # 如果是第一页，获取最大页数
                if page == 1:
                    max_page = parser.get_max_page(html)
                    pbar.total = max_page
                    pbar.refresh()

                # 解析当前页的漫画ID
                uids_page = parser.parse_bookids(html)

                if not uids_page:
                    break

                uids.extend(uids_page)
                pbar.set_postfix({"ID数": len(uids), "本页": len(uids_page)})
                pbar.update(1)

                page += 1
                if page > max_page:
                    break

                time.sleep(random.uniform(1, 3))
            except Exception as e:
                logger.error(f"抓取失败: {parser.name} {page} - {e}")
                break

    print(f"共抓取到 {len(uids)} 个漫画ID")
    return uids


def fetch_all_bookids_with_pages(site, url, start_page, end_page):
    """按页码范围抓取BOOKID

    Args:
        site: 站点名称
        url: 基础URL
        start_page: 起始页码
        end_page: 结束页码，0表示抓取到最后一页

    Returns:
        抓取到的BOOKID列表
    """
    uids = []
    page = start_page
    max_page = 1  # 实际最大页数，将在抓取过程中更新

    # 创建对应的站点解析器
    parser = create_parser(site)

    print(f"\n正在抓取 {site} 的漫画ID (页码范围: {start_page}-{end_page if end_page > 0 else '最后'})...")

    with tqdm(total=None, desc="页面抓取", unit="页") as pbar:
        while True:
            try:
                # 使用解析器获取当前页的URL
                page_url = parser.get_page_url(url, page)

                # 发送请求
                resp = requests.get(
                    page_url,
                    headers=get_random_headers(parser.host, parser.referer),
                    timeout=15
                )
                resp.raise_for_status()
                html = resp.text

                # 如果是第一页，获取最大页数
                if page == 1:
                    max_page = parser.get_max_page(html)
                    # 如果设置了结束页码，取较小值
                    if end_page > 0:
                        max_page = min(max_page, end_page)
                    pbar.total = max_page - start_page + 1
                    pbar.refresh()

                # 解析当前页的漫画ID
                uids_page = parser.parse_bookids(html)

                if not uids_page:
                    break

                uids.extend(uids_page)
                pbar.set_postfix({"ID数": len(uids), "本页": len(uids_page)})
                pbar.update(1)

                page += 1
                # 检查是否达到结束页码
                if end_page > 0 and page > end_page:
                    break
                # 检查是否达到最大页码
                if page > max_page:
                    break

                time.sleep(random.uniform(1, 3))
            except Exception as e:
                logger.error(f"抓取失败: {parser.name} {page} - {e}")
                break

    print(f"共抓取到 {len(uids)} 个漫画ID")
    return uids


# 保存到文件

def save_bookids(site, second, third, uids):
    folder = r'D:\ComicsDownloads\三站漫画下载\BOOKID'
    os.makedirs(folder, exist_ok=True)
    filename = f"{site}_{second}_{third}.txt"
    path = os.path.join(folder, filename)
    try:
        with open(path, 'w', encoding='utf-8') as f:
            f.write(','.join(uids))
        print(f"\n已保存 {len(uids)} 个漫画ID到文件: {path}")
    except Exception as e:
        print(f"\n保存文件失败: {e}")


def ask_download():
    print("\n" + "-" * 40)
    print("已获取漫画ID列表")
    print("-" * 40)
    while True:
        ans = input("\n是否下载这些漫画？(Y/N): ").strip().lower()
        if ans in ("y", "n"):
            return ans == "y"
        print("请输入Y或N。")


# 下载图片并转为jpg

def download_and_convert_jpg(url, save_path, max_retries=15):
    """
    下载图片并转换为jpg格式，包含图片有效性检查和重试机制
    - 检查图片大小必须大于0KB
    - 检查图片像素不能低于20x20
    - 检查图片不能是空白图
    - 支持随机延迟重试
    """
    retry_count = 0
    while retry_count < max_retries:
        try:
            # 随机延迟，避免被反爬，重试次数越多延迟越长
            base_delay = 0.5 + (retry_count * 0.3)  # 基础延迟随重试次数增加
            max_base_delay = 2.0 + (retry_count * 0.5)  # 最大基础延迟也随重试次数增加
            delay = random.uniform(base_delay, max_base_delay) * (retry_count + 1)
            time.sleep(delay)

            # 使用随机User-Agent
            headers = get_random_headers()

            # 增加随机参数，避免缓存
            random_param = f"?random={random.randint(1, 1000000)}"
            if '?' in url:
                request_url = f"{url}&_r={random.randint(1, 1000000)}"
            else:
                request_url = f"{url}{random_param}"

            resp = requests.get(request_url, headers=headers, timeout=30)  # 增加超时时间
            resp.raise_for_status()

            # 检查内容大小
            content_size = len(resp.content)
            if content_size <= 0:
                logger.warning(f"图片大小为0KB: {url}, 重试 {retry_count + 1}/{max_retries}")
                retry_count += 1
                continue

            # 检查图片有效性
            try:
                img = Image.open(BytesIO(resp.content))

                # 检查图片尺寸
                width, height = img.size
                if width < 20 or height < 20:
                    logger.warning(f"图片尺寸过小 ({width}x{height}): {url}, 重试 {retry_count + 1}/{max_retries}")
                    retry_count += 1
                    continue

                # 检查是否为空白图片
                if is_blank_image(img):
                    logger.warning(f"检测到空白图片: {url}, 重试 {retry_count + 1}/{max_retries}")
                    retry_count += 1
                    continue

                # 转换并保存
                if img.format != 'JPEG':
                    rgb_img = img.convert('RGB')
                    rgb_img.save(save_path, 'JPEG')
                else:
                    with open(save_path, 'wb') as f:
                        f.write(resp.content)

                logger.debug(f"成功下载图片: {url} -> {save_path}")
                return True
            except Exception as e:
                logger.warning(f"图片处理失败: {url} - {e}, 重试 {retry_count + 1}/{max_retries}")
                retry_count += 1
                continue

        except Exception as e:
            logger.warning(f"下载图片失败: {url} - {e}, 重试 {retry_count + 1}/{max_retries}")
            retry_count += 1

            # 根据异常类型调整延迟
            if isinstance(e, requests.exceptions.ConnectionError) or isinstance(e, requests.exceptions.Timeout):
                # 网络问题，增加更长的延迟
                time.sleep(random.uniform(5, 15))

            continue

    logger.error(f"图片下载失败，已达到最大重试次数: {url}")
    return False


def is_blank_image(img, threshold=0.95):
    """
    检查图片是否为空白图片
    通过计算图片中接近白色或透明的像素比例来判断
    threshold: 空白像素占比阈值，默认0.95（95%）
    """
    try:
        # 转换为RGB模式以统一处理
        if img.mode == 'RGBA':
            # 对于透明图片，检查alpha通道
            if 'A' in img.getbands():
                alpha = img.getchannel('A')
                if sum(alpha.point(lambda x: 0 if x < 10 else 1).getdata()) / (img.width * img.height) < 0.1:
                    # 如果90%以上像素是透明的，认为是空白图
                    return True

            img = img.convert('RGB')

        # 对于RGB图片，检查亮度
        grayscale = img.convert('L')
        histogram = grayscale.histogram()

        # 计算高亮度像素(220-255)的比例
        bright_pixels = sum(histogram[220:])
        total_pixels = img.width * img.height

        # 如果高亮度像素占比超过阈值，认为是空白图
        return bright_pixels / total_pixels > threshold
    except Exception:
        # 如果分析失败，默认认为不是空白图
        return False


# 漫画下载器类
class ComicDownloader:
    def __init__(self, site, bookid, bookname):
        self.site = site
        self.bookid = bookid
        self.bookname = bookname
        self.dirs = setup_directories(site)
        self.log_manager = LogManager(site)
        self.nas_manager = NASManager()
        self.chapter_list = []
        self.chapter_pdfs = []
        self.cover_pdf = None
        self.merged_pdf = None
        self.nas_pdf_path = None

        # 记录漫画ID
        self.log_manager.log_bookid(bookid, bookname)

    def download(self):
        """下载漫画主流程"""
        try:
            # 获取章节列表
            self.chapter_list = self.get_all_chapters()
            if not self.chapter_list:
                logger.error(f"获取章节列表失败: {self.bookname}({self.bookid})")
                return "failed"

            # 检查是否需要更新
            remote_chapters = len(self.chapter_list)
            need_update, local_chapters = self.log_manager.check_update_needed(self.bookid, remote_chapters)

            if not need_update:
                logger.info(f"漫画 {self.bookname}({self.bookid}) 无需更新")
                return "success"

            logger.info(f"开始处理漫画: {self.bookname}({self.bookid}) - {self.site}")
            logger.info(f"本地章节: {local_chapters}, 远程章节: {remote_chapters}")

            # 下载封面并转为PDF
            if local_chapters == 0:  # 首次下载
                logger.info(f"首次下载，准备下载封面: {self.bookname}")
                self.download_cover()

            # 计算需要下载的章节
            if local_chapters > 0:  # 增量更新
                chapters_to_download = self.chapter_list[local_chapters:]
                logger.info(
                    f"增量更新: {self.bookname}({self.bookid}), 下载章节 {local_chapters + 1} 到 {remote_chapters}")

                # 验证断点续传起点
                if local_chapters >= remote_chapters:
                    logger.warning(f"本地章节数({local_chapters})大于等于远程章节数({remote_chapters})，无需更新")
                    return "success"

                # 检查是否有新章节需要下载
                if not chapters_to_download:
                    logger.warning(f"没有新章节需要下载: {self.bookname}({self.bookid})")
                    return "success"
            else:  # 首次下载
                chapters_to_download = self.chapter_list
                logger.info(f"首次下载: {self.bookname}({self.bookid}), 下载全部 {remote_chapters} 章")

            # 多线程下载章节
            total_chapters = len(chapters_to_download)
            logger.info(f"开始下载 {total_chapters} 个章节")
            self.download_chapters(chapters_to_download)

            # 验证章节是否全部下载完成
            if not self.chapter_pdfs:
                logger.error(f"没有章节PDF生成，下载失败: {self.bookname}({self.bookid})")
                return "failed"

            downloaded_chapters = len(self.chapter_pdfs)

            # 修改：允许部分章节下载成功
            result_status = "success"  # 默认成功
            if downloaded_chapters < total_chapters:
                # 如果下载的章节数量太少，认为失败
                if downloaded_chapters < total_chapters * 0.5:  # 少于50%的章节
                    logger.error(
                        f"章节下载严重不完整: {self.bookname}({self.bookid}), 预期 {total_chapters} 章，实际下载 {downloaded_chapters} 章")
                    print(
                        f"\n警告: 《{self.bookname}》章节下载严重不完整，预期 {total_chapters} 章，实际下载 {downloaded_chapters} 章")
                    return "failed"
                else:
                    # 如果下载了大部分章节，继续处理，但标记为部分成功
                    result_status = "partial"
                    logger.warning(
                        f"章节下载部分不完整: {self.bookname}({self.bookid}), 预期 {total_chapters} 章，实际下载 {downloaded_chapters} 章，但仍继续处理")
                    print(
                        f"\n提示: 《{self.bookname}》下载了 {downloaded_chapters}/{total_chapters} 章，继续处理")

            # 合并PDF
            logger.info(f"开始合并PDF: {self.bookname}({self.bookid})")
            if local_chapters > 0:  # 增量更新
                logger.info(f"执行增量更新合并: {self.bookname}({self.bookid})")
                merge_success = self.merge_with_existing_pdf(local_chapters)
            else:  # 首次下载
                logger.info(f"执行首次下载合并: {self.bookname}({self.bookid})")
                merge_success = self.merge_all_pdfs()

            if not merge_success:
                logger.error(f"PDF合并失败: {self.bookname}({self.bookid})")
                return "failed"

            # 上传到NAS
            if self.merged_pdf and os.path.exists(self.merged_pdf):
                logger.info(f"准备上传到NAS: {self.bookname}({self.bookid})")
                upload_success = self.upload_to_nas()
                if not upload_success:
                    logger.error(f"上传到NAS失败: {self.bookname}({self.bookid})")
                    # 即使上传失败，如果之前步骤成功，仍然标记为部分成功
                    if result_status == "success":
                        result_status = "partial"

            # 更新日志 - 修改：记录实际下载的章节数
            actual_chapters = local_chapters + downloaded_chapters
            logger.info(f"更新日志: {self.bookname}({self.bookid}) - {actual_chapters}章/{remote_chapters}章")
            self.log_manager.update_compare_log(
                self.bookid, self.bookname, actual_chapters, remote_chapters,
                status='partial' if downloaded_chapters < total_chapters else 'completed'
            )

            # 清理临时文件
            logger.info(f"清理临时文件: {self.bookname}({self.bookid})")
            self.clean_up()

            logger.info(f"漫画处理完成: {self.bookname}({self.bookid}), 状态: {result_status}")
            return result_status
        except Exception as e:
            logger.error(f"下载漫画失败: {self.bookname}({self.bookid}) - {e}")
            print(f"\n错误: 下载《{self.bookname}》失败 - {e}")
            return "failed"

    def get_all_chapters(self):
        """获取漫画的所有章节"""
        chapter_list = []
        retry_count = 0
        max_retries = 10  # 设置最大重试次数

        # 为不同站点设置不同的重试策略
        site_specific_retries = {
            '漫小肆漫画': 15,
            '开心看漫画': 12,
            '肉漫天堂': 15
        }
        max_retries = site_specific_retries.get(self.site, 10)

        # 创建对应的站点解析器
        parser = create_parser(self.site)

        while retry_count <= max_retries:
            try:
                # 根据重试次数增加延迟
                if retry_count > 0:
                    delay = random.uniform(3.0, 8.0) * min(retry_count, 5)
                    logger.info(
                        f"第{retry_count}次重试获取章节列表: {self.bookname}({self.bookid}), 等待{delay:.1f}秒...")
                    time.sleep(delay)

                # 获取漫画详情页URL
                detail_url = parser.get_detail_url(self.bookid)

                # 获取详情页内容
                headers = get_random_headers(
                    host=parser.host,
                    referer=parser.referer
                )
                resp = requests.get(detail_url, headers=headers, timeout=30)
                resp.raise_for_status()
                html = resp.text

                # 使用解析器解析章节列表
                chapter_list = parser.parse_chapters(html)

                # 检查是否成功获取章节
                if chapter_list:
                    logger.info(f"获取到 {len(chapter_list)} 个章节: {self.bookname}({self.bookid})")

                    # 最终检查章节顺序
                    chapter_list = sorted(chapter_list, key=lambda x: x['index'])
                    return chapter_list
                else:
                    if retry_count >= max_retries:
                        logger.error(f"无法获取章节列表，已达到最大重试次数: {self.bookname}({self.bookid})")
                        return []

                    logger.warning(f"获取章节列表为空，重试: {self.bookname}({self.bookid})")
                    retry_count += 1

                    # 增加额外延迟
                    extra_delay = random.uniform(5, 15)
                    logger.info(f"增加额外延迟 {extra_delay:.1f} 秒")
                    time.sleep(extra_delay)
            except requests.exceptions.RequestException as e:
                retry_count += 1
                logger.error(f"请求章节列表失败: {self.bookname}({self.bookid}) - {e}")

                # 网络错误，增加更长的延迟
                time.sleep(random.uniform(10, 20))

                if retry_count > max_retries:
                    logger.error(f"获取章节列表失败，已达到最大重试次数: {self.bookname}({self.bookid})")
                    return []
            except Exception as e:
                retry_count += 1
                logger.error(f"获取章节列表异常: {self.bookname}({self.bookid}) - {e}")

                # 其他异常，正常延迟
                time.sleep(random.uniform(5, 10))

                if retry_count > max_retries:
                    logger.error(f"获取章节列表失败，已达到最大重试次数: {self.bookname}({self.bookid})")
                    return []

        # 如果所有重试都失败
        return []

    def download_cover(self):
        """下载漫画封面并转为PDF"""
        max_retry_attempts = 20  # 设置一个较大的重试次数上限，实际上会一直尝试
        retry_count = 0
        success = False

        # 检查封面PDF是否已存在（封面级断点续传）
        cover_pdf_path = os.path.join(
            self.dirs['cover_chapter_pdf'],
            f"{self.bookname}-{self.bookid}-{self.site}.pdf"
        )

        if os.path.exists(cover_pdf_path) and os.path.getsize(cover_pdf_path) > 0:
            logger.info(f"封面PDF已存在，跳过下载: {self.bookname}({self.bookid})")
            self.cover_pdf = cover_pdf_path
            return True

        while not success:
            try:
                if retry_count > 0:
                    # 随机延迟，避免被反爬，重试次数越多延迟越长
                    delay = random.uniform(1.0, 3.0) * min(retry_count, 5)
                    logger.info(f"第{retry_count}次重试下载封面 {self.bookname}({self.bookid}), 等待{delay:.1f}秒...")
                    time.sleep(delay)

                cover_url = None
                cover_dir = os.path.join(self.dirs['raw'], f'{self.site}封面图')
                cover_img_path = os.path.join(cover_dir, f"{self.bookname}-{self.bookid}.jpg")

                # 检查封面图片是否已存在且有效（图片级断点续传）
                if os.path.exists(cover_img_path) and os.path.getsize(cover_img_path) > 0:
                    try:
                        # 验证图片有效性
                        with Image.open(cover_img_path) as img:
                            width, height = img.size
                            if width >= 20 and height >= 20 and not is_blank_image(img):
                                logger.info(f"封面图片已存在且有效，跳过下载: {os.path.basename(cover_img_path)}")
                                # 直接转换为PDF
                                if PDFManager.images_to_pdf([cover_img_path], cover_pdf_path):
                                    self.cover_pdf = cover_pdf_path
                                    logger.info(f"封面PDF已创建: {cover_pdf_path}")
                                    return True
                    except:
                        pass  # 如果验证失败，重新下载

                if self.site == '漫小肆漫画':
                    cover_url = f"https://www.wzd1.cc/static/upload/book/{self.bookid}/cover.jpg"
                elif self.site == '开心看漫画':
                    detail_url = f"https://kxmanhua.com/manga/{self.bookid}"
                    resp = requests.get(detail_url, headers=get_random_headers(), timeout=20)
                    resp.raise_for_status()
                    html = resp.text
                    soup = BeautifulSoup(html, 'lxml')
                    cover_div = soup.select_one('div.anime__details__pic.set-bg')
                    if cover_div:
                        cover_url = cover_div.get('data-setbg')
                        if not cover_url:
                            style = cover_div.get('style', '')
                            m = re.search(r'url\(["\']?(.*?)["\']?\)', style)
                            if m:
                                cover_url = m.group(1)
                    if cover_url and not cover_url.startswith('http'):
                        cover_url = 'https:' + cover_url if cover_url.startswith(
                            '//') else 'https://kxmanhua.com' + cover_url
                elif self.site == '肉漫天堂':
                    detail_url = f"https://rmtt7.com/comics-detail/{self.bookid}"
                    resp = requests.get(detail_url, headers=get_random_headers(), timeout=20)
                    resp.raise_for_status()
                    html = resp.text
                    soup = BeautifulSoup(html, 'lxml')
                    cover_a = soup.select_one('a.vodlist_thumb')
                    if cover_a:
                        cover_url = cover_a.get('data-original')
                        if not cover_url:
                            style = cover_a.get('style', '')
                            m = re.search(r'url\(["\']?(.*?)["\']?\)', style)
                            if m:
                                cover_url = m.group(1)
                    if cover_url and not cover_url.startswith('http'):
                        cover_url = 'https:' + cover_url if cover_url.startswith(
                            '//') else 'https://rmtt7.com' + cover_url

                if cover_url:
                    # 下载封面图片
                    if download_and_convert_jpg(cover_url, cover_img_path):
                        # 转换为PDF
                        if PDFManager.images_to_pdf([cover_img_path], cover_pdf_path):
                            self.cover_pdf = cover_pdf_path
                            logger.info(f"封面PDF已创建: {cover_pdf_path}")
                            success = True
                        else:
                            retry_count += 1
                            logger.warning(f"封面PDF创建失败，重试 {retry_count}")
                    else:
                        retry_count += 1
                        logger.warning(f"封面图片下载失败，重试 {retry_count}")
                else:
                    retry_count += 1
                    logger.warning(f"无法获取封面URL，重试 {retry_count}")
            except Exception as e:
                retry_count += 1
                logger.error(f"下载封面异常: {self.bookname}({self.bookid}) - {e}, 重试 {retry_count}")

            # 虽然设置了max_retry_attempts，但实际上会一直重试
            # 这里只是为了避免无限循环导致程序卡死，增加一个较长的休息时间
            if retry_count >= max_retry_attempts:
                logger.warning(f"封面 {self.bookname}({self.bookid}) 已重试{max_retry_attempts}次，休息30秒后继续...")
                time.sleep(30)
                retry_count = 0  # 重置重试计数器，继续尝试

        return success

    def download_chapters(self, chapters):
        """多线程下载章节"""
        # 按章节索引排序
        chapters = sorted(chapters, key=lambda x: x['index'])

        # 创建章节处理队列，确保按顺序处理
        chapter_queue = queue.Queue()
        for chapter in chapters:
            chapter_queue.put(chapter)

        # 创建结果字典，用于存储章节PDF路径
        results = {}

        with ThreadPoolExecutor(max_workers=CHAPTER_WORKERS) as executor:
            # 提交所有章节下载任务
            futures = {}
            for _ in range(min(CHAPTER_WORKERS, len(chapters))):
                if not chapter_queue.empty():
                    chapter = chapter_queue.get()
                    future = executor.submit(self.process_chapter, chapter)
                    futures[future] = chapter

            # 处理完成的任务，并提交新任务
            for future in tqdm(as_completed(futures), total=len(chapters), desc=f"下载章节 {self.bookname}"):
                chapter = futures[future]
                try:
                    pdf_path = future.result()
                    if pdf_path:
                        logger.info(f"章节下载完成: {chapter['name']}({chapter['index']})")
                        # 存储结果，使用章节索引作为键
                        results[chapter['index']] = pdf_path
                    else:
                        logger.error(f"章节下载失败: {chapter['name']}({chapter['index']})")
                except Exception as e:
                    logger.error(f"章节处理异常: {chapter['name']}({chapter['index']}) - {e}")

                # 提交新任务
                if not chapter_queue.empty():
                    next_chapter = chapter_queue.get()
                    future = executor.submit(self.process_chapter, next_chapter)
                    futures[future] = next_chapter

        # 按章节索引排序PDF路径
        self.chapter_pdfs = [results[idx] for idx in sorted(results.keys())]

    def process_chapter(self, chapter):
        """处理单个章节：下载图片并转为PDF"""
        try:
            # 创建章节目录
            chapter_dir = os.path.join(
                self.dirs['raw'],
                f"{self.bookname}-{self.bookid}-{self.site}",
                f"{chapter['index']:03d}-{chapter['name']}-{chapter['id']}"
            )
            os.makedirs(chapter_dir, exist_ok=True)

            # 检查章节PDF是否已存在（章节级断点续传）
            chapter_pdf_path = os.path.join(
                self.dirs['cover_chapter_pdf'],
                f"{self.bookname}-{chapter['index']:03d}-{chapter['name']}-{self.bookid}-{self.site}.pdf"
            )

            if os.path.exists(chapter_pdf_path) and os.path.getsize(chapter_pdf_path) > 0:
                logger.info(f"章节PDF已存在，跳过下载: {chapter['name']}({chapter['index']})")
                return chapter_pdf_path

            # 下载章节图片
            image_paths = self.download_images(chapter, chapter_dir)
            if not image_paths:
                return None

            # 转换为PDF
            success = PDFManager.images_to_pdf(image_paths, chapter_pdf_path)

            if success:
                return chapter_pdf_path
            else:
                return None
        except Exception as e:
            logger.error(f"处理章节失败: {chapter['name']}({chapter['index']}) - {e}")
            return None

    def download_images(self, chapter, chapter_dir):
        """下载章节内的所有图片"""
        image_paths = []
        retry_count = 0
        max_retry_attempts = 30  # 增加重试次数上限
        success = False

        # 增加站点特定失败计数和最大重试次数
        site_specific_retries = {
            '漫小肆漫画': 40,
            '开心看漫画': 35,
            '肉漫天堂': 45
        }

        # 使用站点特定的最大重试次数
        max_retry_attempts = site_specific_retries.get(self.site, 30)

        # 记录已尝试过的URL，避免重复尝试相同的失败URL
        tried_urls = set()

        # 创建对应的站点解析器
        parser = create_parser(self.site)

        while not success:
            try:
                if retry_count > 0:
                    # 随机延迟，避免被反爬，重试次数越多延迟越长
                    # 增加延迟时间，特别是在多次重试后
                    min_delay = 2.0 + (retry_count * 0.5)  # 基础延迟随重试次数增加
                    max_delay = 5.0 + (retry_count * 1.0)  # 最大延迟也随重试次数增加
                    delay = random.uniform(min_delay, max_delay) * min(retry_count + 1, 10)
                    logger.info(
                        f"第{retry_count}次重试下载章节 {chapter['name']}({chapter['index']}), 等待{delay:.1f}秒...")
                    time.sleep(delay)

                # 获取页面内容
                headers = get_random_headers(
                    host=parser.host,
                    referer=parser.get_detail_url(self.bookid)
                )
                resp = requests.get(chapter['url'], headers=headers, timeout=30)
                resp.raise_for_status()
                html = resp.text

                # 使用解析器获取图片URL
                image_urls = parser.parse_image_urls(html, chapter['url'])

                # 检查是否有足够的图片URL
                if len(image_urls) < 3:
                    retry_count += 1
                    logger.warning(
                        f"章节 {chapter['name']}({chapter['index']}) 图片URL数量不足: {len(image_urls)}, 重试 {retry_count}/{max_retry_attempts}")

                    # 如果多次尝试仍然获取不到足够的图片URL，尝试不同的延迟策略
                    if retry_count % 5 == 0:
                        logger.info(f"多次尝试失败，增加额外延迟...")
                        time.sleep(random.uniform(10, 20))  # 长时间延迟，避免IP被封

                    continue

                # 按顺序下载图片，支持图片级断点续传
                image_paths = []
                all_images_downloaded = True
                download_failures = 0  # 记录下载失败的次数

                for idx, img_url in image_urls:
                    # 如果这个URL之前已经尝试过且失败，跳过
                    if img_url in tried_urls:
                        continue

                    img_path = os.path.join(chapter_dir, f"{idx:03d}.jpg")

                    # 检查图片是否已存在且有效（图片级断点续传）
                    if os.path.exists(img_path) and os.path.getsize(img_path) > 0:
                        try:
                            # 验证图片有效性
                            with Image.open(img_path) as img:
                                width, height = img.size
                                if width >= 20 and height >= 20 and not is_blank_image(img):
                                    logger.debug(f"图片已存在且有效，跳过下载: {os.path.basename(img_path)}")
                                    image_paths.append((idx, img_path))
                                    continue
                        except:
                            pass  # 如果验证失败，重新下载

                    # 下载图片，增加重试次数
                    if download_and_convert_jpg(img_url, img_path, max_retries=15):
                        image_paths.append((idx, img_path))
                    else:
                        all_images_downloaded = False
                        download_failures += 1
                        tried_urls.add(img_url)  # 记录失败的URL
                        logger.warning(f"图片下载失败: {img_url}")

                # 确保图片路径按索引排序
                image_paths.sort(key=lambda x: x[0])
                image_paths = [path for _, path in image_paths]

                # 检查是否获取了足够的图片
                # 修改成功条件：只要下载了大部分图片就算成功，不要求全部下载成功
                min_required_images = max(3, len(image_urls) * 0.75)  # 至少需要75%的图片

                if len(image_paths) >= min_required_images:
                    success = True
                    logger.info(
                        f"成功下载章节 {chapter['name']}({chapter['index']}) 的 {len(image_paths)}/{len(image_urls)} 张图片")
                else:
                    retry_count += 1
                    logger.warning(
                        f"章节 {chapter['name']}({chapter['index']}) 图片下载不完整: {len(image_paths)}/{len(image_urls)}, 重试 {retry_count}/{max_retry_attempts}")

                    # 根据失败率调整延迟
                    if download_failures > 0:
                        failure_ratio = download_failures / len(image_urls)
                        if failure_ratio > 0.5:  # 如果失败率超过50%
                            extra_delay = random.uniform(15, 30)
                            logger.info(f"失败率较高 ({failure_ratio:.1%})，增加额外延迟 {extra_delay:.1f}秒")
                            time.sleep(extra_delay)
            except Exception as e:
                retry_count += 1
                logger.error(
                    f"下载图片失败: {chapter['name']}({chapter['index']}) - {e}, 重试 {retry_count}/{max_retry_attempts}")

                # 根据异常类型调整延迟
                if isinstance(e, requests.exceptions.ConnectionError) or isinstance(e, requests.exceptions.Timeout):
                    # 网络问题，增加更长的延迟
                    time.sleep(random.uniform(20, 40))
                else:
                    # 其他异常，正常延迟
                    time.sleep(random.uniform(5, 10))

            # 检查是否达到最大重试次数
            if retry_count >= max_retry_attempts:
                # 如果已经有一些图片，即使不完整也返回
                if len(image_paths) >= 3:
                    logger.warning(
                        f"章节 {chapter['name']}({chapter['index']}) 已重试{max_retry_attempts}次，返回不完整结果: {len(image_paths)}张图片")
                    return image_paths

                logger.warning(
                    f"章节 {chapter['name']}({chapter['index']}) 已重试{max_retry_attempts}次，休息60秒后继续...")
                time.sleep(60)
                retry_count = max_retry_attempts // 2  # 减少重试计数，给予更多机会

        return image_paths

    def merge_all_pdfs(self):
        """合并所有PDF（首次下载）"""
        try:
            # 准备所有PDF路径
            pdf_paths = []
            if self.cover_pdf and os.path.exists(self.cover_pdf):
                pdf_paths.append(self.cover_pdf)

            # 章节PDF应该已经按顺序排列，但我们再次确认
            if not self.chapter_pdfs:
                logger.error(f"没有章节PDF可合并: {self.bookname}({self.bookid})")
                return False

            # 检查章节PDF是否已排序
            chapter_count = len(self.chapter_pdfs)
            logger.info(f"合并 {chapter_count} 个章节PDF")
            for pdf_path in self.chapter_pdfs[:5]:  # 只记录前5个用于调试
                logger.debug(f"章节PDF: {os.path.basename(pdf_path)}")

            pdf_paths.extend(self.chapter_pdfs)

            if not pdf_paths:
                logger.error(f"没有PDF可合并: {self.bookname}({self.bookid})")
                return False

            # 合并PDF
            # 使用实际下载的章节数量，而不是章节列表的长度
            actual_chapter_count = len(self.chapter_pdfs)
            merged_pdf_path = os.path.join(
                self.dirs['merged_pdf'],
                f"{self.bookname}-(1-{actual_chapter_count}话)-{self.site}-{self.bookid}.pdf"
            )

            success = PDFManager.merge_pdfs(pdf_paths, merged_pdf_path)
            if success:
                self.merged_pdf = merged_pdf_path
                return True
            else:
                return False
        except Exception as e:
            logger.error(f"合并PDF失败: {self.bookname}({self.bookid}) - {e}")
            print(f"\n错误: 合并《{self.bookname}》PDF失败 - {e}")
            return False

    def merge_with_existing_pdf(self, local_chapters):
        """与已有PDF合并（增量更新）"""
        try:
            # 尝试从NAS下载旧PDF
            old_pdf_path = None
            if self.nas_manager.connect():
                # 查找旧PDF文件
                nas_dir = os.path.join(NAS_CONFIG['target_dir'], self.site)
                search_pattern = f"{self.bookname}-(1-{local_chapters}话)-{self.site}-{self.bookid}.pdf"

                try:
                    # 获取NAS目录中的文件列表
                    file_list = self.nas_manager.list_files(nas_dir)

                    # 查找匹配的文件 - 通过bookid和site精确匹配
                    for filename in file_list:
                        if filename.endswith(f"-{self.site}-{self.bookid}.pdf"):
                            # 找到匹配的文件
                            remote_path = os.path.join(nas_dir, filename).replace('\\', '/')
                            old_pdf_path = os.path.join(self.dirs['nas_middleware'], filename)

                            # 下载旧PDF
                            if self.nas_manager.download_file(remote_path, old_pdf_path):
                                self.nas_pdf_path = remote_path
                                logger.info(f"从NAS下载旧PDF成功: {filename}")
                                break
                            else:
                                logger.error(f"从NAS下载旧PDF失败: {filename}")
                                old_pdf_path = None
                except Exception as e:
                    logger.error(f"查找NAS上的旧PDF失败: {e}")

            if not old_pdf_path or not os.path.exists(old_pdf_path):
                logger.warning(f"无法从NAS获取旧PDF，将执行完整合并: {self.bookname}({self.bookid})")
                return self.merge_all_pdfs()

            # 章节PDF应该已经按顺序排列，但我们再次确认
            if not self.chapter_pdfs:
                logger.error(f"没有新章节PDF可合并: {self.bookname}({self.bookid})")
                return False

            # 检查章节PDF是否已排序
            new_chapter_count = len(self.chapter_pdfs)
            logger.info(f"增量合并 {new_chapter_count} 个新章节PDF")
            for pdf_path in self.chapter_pdfs[:5]:  # 只记录前5个用于调试
                logger.debug(f"新章节PDF: {os.path.basename(pdf_path)}")

            # 合并PDF
            # 计算实际总章节数 = 本地已有章节 + 新下载章节
            total_chapter_count = local_chapters + new_chapter_count
            merged_pdf_path = os.path.join(
                self.dirs['merged_pdf'],
                f"{self.bookname}-(1-{total_chapter_count}话)-{self.site}-{self.bookid}.pdf"
            )

            # 先放旧PDF，再放新章节PDF
            pdf_paths = [old_pdf_path] + self.chapter_pdfs

            success = PDFManager.merge_pdfs(pdf_paths, merged_pdf_path)
            if success:
                self.merged_pdf = merged_pdf_path
                return True
            else:
                return False
        except Exception as e:
            logger.error(f"与已有PDF合并失败: {self.bookname}({self.bookid}) - {e}")
            print(f"\n错误: 增量合并《{self.bookname}》PDF失败 - {e}")
            return False

    def upload_to_nas(self):
        """上传合并后的PDF到NAS"""
        if not self.merged_pdf or not os.path.exists(self.merged_pdf):
            logger.error(f"没有合并的PDF可上传: {self.bookname}({self.bookid})")
            print(f"\n错误: 《{self.bookname}》没有合并的PDF可上传到NAS")
            return False

        if not self.nas_manager.connect():
            logger.error(f"NAS连接失败，无法上传: {self.bookname}({self.bookid})")
            print(f"\n错误: NAS连接失败，无法上传《{self.bookname}》")
            return False

        try:
            # 准备NAS路径
            nas_dir = os.path.join(NAS_CONFIG['target_dir'], self.site)
            remote_path = os.path.join(nas_dir, os.path.basename(self.merged_pdf))

            # 确保NAS上的目录存在
            if not self.nas_manager.ensure_directory(nas_dir):
                logger.error(f"无法在NAS上创建目录: {nas_dir}")
                print(f"\n错误: 无法在NAS上创建目录: {nas_dir}")
                return False

            # 上传新PDF
            print(f"\n正在上传《{self.bookname}》到NAS...")
            if self.nas_manager.upload_file(self.merged_pdf, remote_path):
                logger.info(f"成功上传到NAS: {remote_path}")
                print(f"\n成功: 《{self.bookname}》已上传到NAS")

                # 如果是增量更新，删除旧PDF
                if self.nas_pdf_path:
                    if self.nas_manager.delete_file(self.nas_pdf_path):
                        logger.info(f"成功删除旧PDF: {self.nas_pdf_path}")
                    else:
                        logger.warning(f"删除旧PDF失败: {self.nas_pdf_path}")

                return True
            else:
                logger.error(f"上传到NAS失败: {self.bookname}({self.bookid})")
                print(f"\n错误: 上传《{self.bookname}》到NAS失败")
                return False
        except Exception as e:
            logger.error(f"上传到NAS异常: {self.bookname}({self.bookid}) - {e}")
            print(f"\n错误: 上传《{self.bookname}》到NAS时发生异常: {e}")
            return False
        finally:
            self.nas_manager.close()

    def clean_up(self):
        """清理临时文件"""
        try:
            # 只有在成功上传到NAS后才清理文件
            if not self.merged_pdf or not os.path.exists(self.merged_pdf):
                logger.info(f"合并PDF不存在，跳过清理: {self.bookname}({self.bookid})")
                return False

            # 清理原始图片目录
            raw_dir = os.path.join(self.dirs['raw'], f"{self.bookname}-{self.bookid}-{self.site}")
            if os.path.exists(raw_dir):
                shutil.rmtree(raw_dir)
                logger.info(f"已清理原始图片目录: {raw_dir}")

            # 清理NAS中间件
            if self.nas_pdf_path:
                middleware_dir = self.dirs['nas_middleware']
                for file in os.listdir(middleware_dir):
                    if file.endswith(f"-{self.site}-{self.bookid}.pdf"):
                        os.remove(os.path.join(middleware_dir, file))
                logger.info(f"已清理NAS中间件文件")

            # 注意：不删除章节PDF和封面PDF，保留这些文件以便后续查看或调试
            # 章节PDF和封面PDF占用空间较小，保留它们可以避免重复下载

            print(f"\n清理: 已删除《{self.bookname}》的临时文件")
            return True
        except Exception as e:
            logger.error(f"清理临时文件失败: {self.bookname}({self.bookid}) - {e}")
            print(f"\n警告: 清理《{self.bookname}》临时文件失败 - {e}")
            return False


# 主程序

def main():
    print("漫画下载器启动中，请稍候...")

    # 确保主目录存在
    main_dir = r'D:\ComicsDownloads\三站漫画下载'
    os.makedirs(main_dir, exist_ok=True)
    print(f"主目录: {main_dir}")

    # 初始化所有站点的目录结构
    initialize_all_directories()

    print("\n" + "=" * 50)
    print("多站漫画下载器 - 独立版")
    print("=" * 50)

    # 显示选项菜单
    while True:
        print("\n请选择操作:")
        options = [
            "下载漫小肆漫画",
            "下载开心看漫画",
            "下载肉漫天堂",
            "下载所有站点",
            "退出程序"
        ]

        for i, option in enumerate(options):
            print(f"{i + 1}. {option}")

        try:
            choice = int(input("\n请输入选项编号: "))
            if choice < 1 or choice > len(options):
                print("输入错误，请重新选择!")
                continue

            if choice == 5:  # 退出程序
                print("\n感谢使用，再见!")
                sys.exit(0)

            # 根据选择确定要下载的站点
            sites_to_download = []
            if choice == 1:
                sites_to_download = ['漫小肆漫画']
            elif choice == 2:
                sites_to_download = ['开心看漫画']
            elif choice == 3:
                sites_to_download = ['肉漫天堂']
            elif choice == 4:
                sites_to_download = ['漫小肆漫画', '开心看漫画', '肉漫天堂']

            # 下载选择的站点
            for site in sites_to_download:
                download_site(site)

        except ValueError:
            print("请输入有效的数字!")
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            sys.exit(0)
        except Exception as e:
            print(f"\n发生错误: {e}")


def download_site(site):
    """下载指定站点的漫画"""
    print(f"\n{'-' * 30}")
    print(f"开始处理 {site}")
    print(f"{'-' * 30}")

    # 选择分类
    result, is_batch = choose_category_for_site(site)

    if is_batch:
        all_tasks = []
        for task in result:
            site, second, third = task[0], task[1], task[2]

            # 检查是否有页码设置
            if len(task) == 6:
                url, start_page, end_page = task[3], task[4], task[5]
                print(
                    f"\n开始抓取：{site} - {second} - {third} (页码范围: {start_page}-{end_page if end_page > 0 else '最后'})...")
                bookids = fetch_all_bookids_with_pages(site, url, start_page, end_page)
            else:
                url = task[3]
                # 特殊处理"输入BOOKID"选项
                if third == "输入BOOKID" and second == "直接下载":
                    print(f"\n使用指定BOOKID: {url}")
                    bookids = [url]  # url实际上是bookid
                else:
                    print(f"\n开始抓取：{site} - {second} - {third}")
                    bookids = fetch_all_bookids(site, url)

            save_bookids(site, second, third, bookids)
            if bookids:
                for bookid in bookids:
                    all_tasks.append((site, bookid))

        if all_tasks and ask_download():
            process_download_tasks(all_tasks)
    else:
        task = result[0]
        site, second, third = task[0], task[1], task[2]

        # 检查是否有页码设置
        if len(task) == 6:
            url, start_page, end_page = task[3], task[4], task[5]
            print(
                f"\n开始抓取：{site} - {second} - {third} (页码范围: {start_page}-{end_page if end_page > 0 else '最后'})...")
            bookids = fetch_all_bookids_with_pages(site, url, start_page, end_page)
        else:
            url = task[3]
            # 特殊处理"输入BOOKID"选项
            if third == "输入BOOKID" and second == "直接下载":
                print(f"\n使用指定BOOKID: {url}")
                bookids = [url]  # url实际上是bookid
            else:
                print(f"\n开始抓取：{site} - {second} - {third}")
                bookids = fetch_all_bookids(site, url)

        save_bookids(site, second, third, bookids)

        if bookids and ask_download():
            all_tasks = [(site, bookid) for bookid in bookids]
            process_download_tasks(all_tasks)


def choose_category_for_site(site):
    """选择指定站点的分类"""

    def input_with_options(prompt, options):
        print("\n" + "-" * 40)
        for i, k in enumerate(options):
            print(f"{i + 1}. {k}")
        print("-" * 40)

        while True:
            try:
                choice = input(prompt + " ")
                idx = int(choice) - 1
                if 0 <= idx < len(options):
                    return idx, options[idx]
            except Exception:
                pass
            print("输入有误，请重新输入。")

    # 只显示特定站点的分类
    print(f"\n选择 {site} 的分类:")
    second_keys = list(CATEGORIES[site].keys())
    second_keys_show = ["全部", "输入BOOKID"] + second_keys + ["返回上一级", "退出"]
    idx2, second_key = input_with_options("请输入序号:", second_keys_show)

    if second_key == "退出":
        sys.exit(0)

    if second_key == "返回上一级":
        return [], False  # 返回空列表和False，表示取消选择

    if second_key == "全部":
        all_tasks = []
        for second in CATEGORIES[site]:
            for third in CATEGORIES[site][second]:
                all_tasks.append((site, second, third, CATEGORIES[site][second][third]))
        return all_tasks, True

    # 处理"输入BOOKID"选项
    if second_key == "输入BOOKID":
        while True:
            bookid = input("\n请输入BOOKID (输入q返回): ").strip()
            if bookid.lower() == 'q':
                break
            if bookid.isdigit():
                # 返回特殊格式，表示直接下载指定BOOKID
                return [(site, "直接下载", "输入BOOKID", bookid)], False
            else:
                print("BOOKID必须是数字，请重新输入。")
        return [], False  # 用户输入q返回

    # 三级分类
    print(f"\n选择子分类 ({second_key}):")
    third_keys = list(CATEGORIES[site][second_key].keys())
    third_keys_show = ["全部", "设置页数"] + third_keys + ["返回上一级", "退出"]
    idx3, third_key = input_with_options("请输入序号:", third_keys_show)

    if third_key == "退出":
        sys.exit(0)

    if third_key == "返回上一级":
        return [], False  # 返回空列表和False，表示取消选择

    if third_key == "全部":
        all_tasks = []
        for third in CATEGORIES[site][second_key]:
            all_tasks.append((site, second_key, third, CATEGORIES[site][second_key][third]))
        return all_tasks, True

    # 处理"设置页数"选项
    if third_key == "设置页数":
        try:
            start_page_input = input("\n请输入起始页码: ").strip()
            if not start_page_input.isdigit() or int(start_page_input) < 1:
                print("起始页码必须是大于等于1的整数，请重新选择。")
                return [], False
            start_page = int(start_page_input)

            end_page_input = input("请输入结束页码 (0表示抓取到最后): ").strip()
            if not end_page_input.isdigit() or int(end_page_input) < 0:
                print("结束页码必须是大于等于0的整数，请重新选择。")
                return [], False
            end_page = int(end_page_input)

            print(f"\n已设置页码范围: {start_page} - {end_page if end_page > 0 else '最后'}")

            # 选择要应用页码设置的分类
            print(f"\n选择要应用页码设置的子分类:")
            third_keys_for_pages = third_keys + ["全部"]
            idx4, selected_third = input_with_options("请输入序号:", third_keys_for_pages)

            if selected_third == "全部":
                all_tasks = []
                for third in CATEGORIES[site][second_key]:
                    url = CATEGORIES[site][second_key][third]
                    # 添加页码信息
                    all_tasks.append((site, second_key, third, url, start_page, end_page))
                return all_tasks, True
            else:
                url = CATEGORIES[site][second_key][selected_third]
                # 返回带页码信息的任务
                return [(site, second_key, selected_third, url, start_page, end_page)], False
        except Exception as e:
            print(f"设置页码时发生错误: {e}")
            return [], False

    url = CATEGORIES[site][second_key][third_key]
    return [(site, second_key, third_key, url)], False


def initialize_all_directories():
    """初始化所有站点的目录结构"""
    sites = ['漫小肆漫画', '开心看漫画', '肉漫天堂']

    # 将日志级别临时设置为更高级别，避免初始化过程中的日志输出
    original_level = logger.level
    logger.setLevel(logging.ERROR)

    # 先创建所有本地目录
    for site in sites:
        dirs = setup_directories(site)
        logger.debug(f"站点 {site} 的目录结构已创建")

        # 初始化日志管理器，确保日志文件存在
        log_manager = LogManager(site)

    # 测试NAS连接和目录创建
    if NAS_CONFIG['enabled']:
        try:
            nas_manager = NASManager()
            if nas_manager.connect():
                logger.debug("NAS连接成功")

                # 一次性创建所有NAS目录
                for site in sites:
                    nas_dir = os.path.join(NAS_CONFIG['target_dir'], site)
                    nas_manager.ensure_directory(nas_dir)

                logger.debug("所有NAS目录已创建")
                nas_manager.close()
            else:
                logger.warning("NAS连接失败，将只保存到本地")
        except Exception as e:
            logger.error(f"NAS初始化异常: {e}")

    # 恢复原始日志级别
    logger.setLevel(original_level)
    logger.debug("所有目录结构初始化完成")


def process_download_tasks(tasks):
    """处理下载任务，支持并发"""
    print("\n" + "=" * 50)
    print("准备下载漫画...")
    print("=" * 50)

    # 临时提高日志级别，减少控制台输出
    original_level = logger.level
    logger.setLevel(logging.ERROR)

    # 初始化NAS连接
    nas_manager = NASManager()
    if NAS_CONFIG['enabled']:
        print("正在连接NAS...")
        if nas_manager.connect():
            # 确保NAS上的目录结构存在
            for site in set([task[0] for task in tasks]):
                nas_dir = os.path.join(NAS_CONFIG['target_dir'], site)
                nas_manager.ensure_directory(nas_dir)
            nas_manager.close()
            print("NAS连接成功，目录已准备")
        else:
            print("NAS连接失败，将只保存到本地")

    # 获取漫画名称
    print("正在获取漫画名称...")
    tasks_with_names = []

    with tqdm(total=len(tasks), desc="获取漫画信息", unit="本") as pbar:
        for site, bookid in tasks:
            try:
                bookname = get_comic_name(site, bookid)
                tasks_with_names.append((site, bookid, bookname))
            except Exception as e:
                logger.error(f"获取漫画名称失败: {site} - {bookid} - {e}")
                # 使用bookid作为bookname，确保任务不会被跳过
                tasks_with_names.append((site, bookid, bookid))
            pbar.update(1)

    # 先检查哪些任务需要更新（断点续传检查）
    print("\n检查哪些漫画需要更新...")
    tasks_to_process = []

    with tqdm(total=len(tasks_with_names), desc="检查更新状态", unit="本") as pbar:
        for site, bookid, bookname in tasks_with_names:
            # 创建日志管理器
            log_manager = LogManager(site)

            # 获取章节列表
            try:
                temp_downloader = ComicDownloader(site, bookid, bookname)
                chapter_list = temp_downloader.get_all_chapters()
                if not chapter_list:
                    pbar.set_postfix({"状态": "获取章节失败"})
                    logger.error(f"无法获取章节列表: {bookname}({bookid}) - {site}")
                    # 即使获取章节失败，也添加到任务列表，后续会重试
                    tasks_to_process.append((site, bookid, bookname))
                    pbar.update(1)
                    continue

                remote_chapters = len(chapter_list)
                need_update, local_chapters = log_manager.check_update_needed(bookid, remote_chapters)

                if need_update:
                    pbar.set_postfix({"状态": f"需更新 {local_chapters}->{remote_chapters}"})
                    logger.info(
                        f"任务需要更新: {bookname}({bookid}) - {site}, 本地章节: {local_chapters}, 远程章节: {remote_chapters}")
                    tasks_to_process.append((site, bookid, bookname))
                else:
                    pbar.set_postfix({"状态": "已是最新"})
                    logger.info(
                        f"任务无需更新: {bookname}({bookid}) - {site}, 本地章节: {local_chapters}, 远程章节: {remote_chapters}")
            except Exception as e:
                pbar.set_postfix({"状态": "检查失败"})
                logger.error(f"检查更新失败: {bookname}({bookid}) - {site} - {e}")
                # 如果检查失败，仍然添加到任务列表，让下载器再次尝试
                tasks_to_process.append((site, bookid, bookname))

            pbar.update(1)

    # 恢复日志级别
    logger.setLevel(original_level)

    if not tasks_to_process:
        print("\n所有漫画都已是最新，无需下载")
        return

    print(f"\n共有 {len(tasks_to_process)} 本漫画需要更新，开始下载...")

    # 统计下载结果
    success_count = 0
    fail_count = 0
    partial_success_count = 0

    # 使用线程池进行下载
    with ThreadPoolExecutor(max_workers=TOTAL_WORKERS) as executor:
        futures = {executor.submit(download_comic_with_retry, site, bookid, bookname): (site, bookid, bookname)
                   for site, bookid, bookname in tasks_to_process}

        for future in tqdm(as_completed(futures), total=len(futures), desc="下载进度", unit="本"):
            site, bookid, bookname = futures[future]
            try:
                result = future.result()
                if result == "success":
                    success_count += 1
                    logger.info(f"漫画下载完成: {bookname}({bookid}) - {site}")
                elif result == "partial":
                    partial_success_count += 1
                    logger.warning(f"漫画部分下载完成: {bookname}({bookid}) - {site}")
                else:
                    fail_count += 1
                    logger.error(f"漫画下载失败: {bookname}({bookid}) - {site}")
            except Exception as e:
                fail_count += 1
                logger.error(f"漫画下载异常: {bookname}({bookid}) - {site} - {e}")
                print(f"\n错误: 下载《{bookname}》时发生异常: {e}")

    print("\n" + "=" * 50)
    if partial_success_count > 0:
        print(f"下载任务完成: 成功 {success_count} 本，部分成功 {partial_success_count} 本，失败 {fail_count} 本")
    else:
        print(f"下载任务完成: 成功 {success_count} 本，失败 {fail_count} 本")
    print("=" * 50)


def download_comic_with_retry(site, bookid, bookname):
    """下载单本漫画，带重试机制"""
    max_retries = 3  # 最大重试次数
    retry_count = 0

    while retry_count <= max_retries:
        try:
            downloader = ComicDownloader(site, bookid, bookname)
            result = downloader.download()

            # 检查结果类型
            if isinstance(result, bool):
                return "success" if result else "failed"
            elif isinstance(result, str):
                return result  # 可能是 "partial"
            else:
                return "failed"
        except Exception as e:
            retry_count += 1
            logger.error(f"下载漫画异常，重试 {retry_count}/{max_retries}: {bookname}({bookid}) - {site} - {e}")

            if retry_count <= max_retries:
                # 增加延迟时间，避免频繁请求
                delay = random.uniform(5, 15) * retry_count
                logger.info(f"等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)
            else:
                logger.error(f"下载漫画失败，已达到最大重试次数: {bookname}({bookid}) - {site}")
                return "failed"

    return "failed"


def get_comic_name(site, bookid):
    """获取漫画名称"""
    try:
        # 创建对应的站点解析器
        parser = create_parser(site)

        # 获取漫画详情页URL
        detail_url = parser.get_detail_url(bookid)

        # 发送请求
        resp = requests.get(detail_url, headers=get_random_headers(), timeout=20)
        resp.raise_for_status()
        html = resp.text

        # 解析漫画名称
        name = parser.parse_comic_name(html)
        if name:
            return name
        else:
            return bookid
    except Exception as e:
        logger.error(f"获取漫画名称失败: {site} - {bookid} - {e}")
        return bookid


def download_comic(site, bookid, bookname):
    """下载单本漫画"""
    downloader = ComicDownloader(site, bookid, bookname)
    return downloader.download()


if __name__ == '__main__':
    try:
        # 确保日志不会干扰控制台输出
        for handler in logging.root.handlers:
            if isinstance(handler, logging.StreamHandler):
                logging.root.removeHandler(handler)

        # 启动主程序
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n程序发生错误: {e}")
        sys.exit(1)