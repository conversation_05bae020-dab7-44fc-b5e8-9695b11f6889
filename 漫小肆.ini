import requests
from bs4 import BeautifulSoup
import re
import datetime
import random
import time
import os
import json
import logging
import shutil
from PIL import Image
from urllib.parse import urljoin
from io import BytesIO
from PyPDF2 import PdfMerger, PdfReader
from tqdm import tqdm
from threading import Lock, Event
from concurrent.futures import ThreadPoolExecutor, as_completed
from smb.SMBConnection import SMBConnection
import hashlib
import sys
from typing import Optional, Tuple
import psutil
import threading


# 新增：用于文件传输的进度条包装器
class ProgressWrapper:
    def __init__(self, file_obj, pbar):
        self._file = file_obj
        self._pbar = pbar

    def read(self, *args, **kwargs):
        chunk = self._file.read(*args, **kwargs)
        if chunk:
            self._pbar.update(len(chunk))
        return chunk

    def write(self, chunk, *args, **kwargs):
        bytes_written = self._file.write(chunk, *args, **kwargs)
        # write may return None in some cases, so we fall back to len(chunk)
        self._pbar.update(bytes_written or len(chunk))
        return bytes_written

    def __getattr__(self, attr):
        # Delegate other attribute access to the wrapped file object
        return getattr(self._file, attr)


# 检查依赖项
def check_dependencies():
    """检查所有必要的依赖项是否已安装"""
    missing_deps = []

    # 检查reportlab库（用于章节元数据）
    try:
        import reportlab
    except ImportError:
        missing_deps.append("reportlab")

    # 如果有缺失的依赖项，提示安装
    if missing_deps:
        print("检测到缺失的依赖项:")
        for dep in missing_deps:
            print(f" - {dep}")
        print("\n请使用以下命令安装缺失的依赖项:")
        print(f"pip install {' '.join(missing_deps)}")

        # 询问是否自动安装
        if input("是否自动安装缺失的依赖项? (y/n): ").strip().lower() == 'y':
            import subprocess
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_deps)
                print("依赖项安装成功!")
                # 重新导入
                if "reportlab" in missing_deps:
                    import reportlab
            except Exception as e:
                print(f"自动安装失败: {str(e)}")
                print("请手动安装依赖项后再运行程序。")
                sys.exit(1)
        else:
            print("请手动安装依赖项后再运行程序。")
            sys.exit(1)


# ========== 用户配置区 ==========
# （起始页和结束页已由运行时输入，不再需要START_PAGE和END_PAGE配置）

# 魔法数字配置
IMAGE_MAX_RETRY = 100  # 图片最大重试次数
IMAGE_TIMEOUT_SECONDS = 600  # 单张图片下载超时时间（秒）

# 新增：图片过滤和重新下载配置
IMAGE_MIN_SIZE_KB = 1  # 图片最小有效大小(KB)，小于此值被视为无效图片
SINGLE_CHAPTER_SKIP_RATE = 0.1  # 单章节跳过率阈值，超过此值视为高跳过率章节
HIGH_SKIP_CHAPTERS_RATIO = 0.1  # 高跳过率章节比例阈值，超过此值触发重新下载
OVERALL_SKIP_RATE = 0.05  # 整体图片跳过率阈值，超过此值触发重新下载
CHAPTER_SUCCESS_RATE = 0.95  # 章节下载成功率阈值，低于此值放弃当前下载

# 会话记录配置
SESSION_RECORD_FILE = "D:/ComicsDownloads/漫小肆/漫小肆漫画下载日志/未完成的任务记录.json"  # 会话记录文件路径

# 验证机制配置
ENABLE_CHAPTER_CONTINUITY_CHECK = True  # 启用章节序号连续性检查
ENABLE_PREDOWNLOAD_VERIFICATION = True  # 启用预下载验证机制
ENABLE_CHAPTER_METADATA = True  # 启用章节元数据校验
ENABLE_PHASE_VALIDATION = True  # 启用分段验证
CHAPTER_GAP_THRESHOLD = 2  # 章节序号允许的最大间隔（大于此值视为缺章）
MISSING_CHAPTER_THRESHOLD = 0.05  # 允许缺失章节的比例阈值（大于此值会触发重新下载）

# 用户代理列表（用于模拟浏览器请求）
USER_AGENTS = [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:138.0) Gecko/20100101 Firefox/138.0',
]

# ====== 线程配置 ======
# 线程模式：自动(True)或手动(False)
THREAD_AUTO_MODE = True  # 设置为True启用自动调整线程数，设置为False则使用手动设置的固定线程数

# 手动线程设置（仅在THREAD_AUTO_MODE=False时生效）
MANUAL_TOTAL_WORKERS = 5  # 总线程数（同时下载的漫画数量）
MANUAL_CHAPTER_WORKERS = 5  # 每个漫画的章节下载线程数

# 自动线程设置参数（仅在THREAD_AUTO_MODE=True时生效）
TOTAL_WORKERS = 5  # 自动模式下的初始总线程数
CHAPTER_WORKERS = 5  # 自动模式下的初始章节线程数
MAX_TOTAL_WORKERS = 10  # 总线程数上限
MIN_TOTAL_WORKERS = 1  # 总线程数下限
MAX_CHAPTER_WORKERS = 10  # 章节线程数上限
MIN_CHAPTER_WORKERS = 2  # 章节线程数下限

# 线程调整间隔（秒）
THREAD_ADJUST_INTERVAL = 15  # 自动调整线程的时间间隔（秒）
THREAD_STATUS_INTERVAL = 60  # 线程状态报告的时间间隔（秒）

# ====== 自适应线程相关变量 ======
current_total_workers = TOTAL_WORKERS if THREAD_AUTO_MODE else MANUAL_TOTAL_WORKERS  # 总线程数
current_workers = CHAPTER_WORKERS if THREAD_AUTO_MODE else MANUAL_CHAPTER_WORKERS  # 章节线程数

error_count = 0
success_count = 0
adjust_lock = threading.Lock()

# NAS存储配置
NAS_CONFIG = {
    'enabled': True,  # 是否启用NAS上传功能
    'server_ip': '**************',  # NAS服务器IP地址
    'server_name': 'D1581-fnos-91',  # NAS服务器名称
    'share_name': '小说',  # NAS共享文件夹名称
    'username': 'admin',  # NAS登录用户名
    'password': 'ly120220',  # NAS登录密码
    'target_dir': '漫画/韩漫自爬',  # NAS上的目标目录
    'client_name': 'ComicDownloader'  # 客户端名称（用于连接NAS）
}

# ====== 网页抓取配置 ======
# 网页抓取最大重试次数（如页面抓取失败时的最大重试次数）
SCRAPE_MAX_RETRY = 5  # 默认5次，可根据网络情况调整
# 网页抓取每次重试的最小/最大随机延迟（秒）
SCRAPE_RETRY_DELAY_MIN = 5  # 单位：秒，重试时的最小等待时间
SCRAPE_RETRY_DELAY_MAX = 20  # 单位：秒，重试时的最大等待时间
# 网页请求超时时间（秒）
SCRAPE_REQUEST_TIMEOUT = 20  # 单位：秒，单次requests.get的超时时间
# 网页抓取主循环最大重试次数（如get_all_chapters等，页面整体重试次数）
SCRAPE_MAIN_LOOP_MAX_RETRY = 3  # 默认3次，整体抓取失败时的最大重试次数
# ====== 网页抓取配置结束 ======

# ====== 主循环抓取频率配置 ======
MAIN_LOOP_SLEEP_MIN = 2  # 主循环每次页面抓取后的最小等待时间（秒）
MAIN_LOOP_SLEEP_MAX = 5  # 主循环每次页面抓取后的最大等待时间（秒）
# ====== 主循环抓取频率配置结束 ======

# ========== 配置区结束 ==========

# 全局上传锁 - 定义为全局锁
UPLOAD_LOCK = Lock()
COMPARE_LOG_LOCK = Lock()

# 全局事件用于控制超时处理
USER_INTERRUPT_EVENT = Event()  # 全局超时跳过标志，设置后将跳过所有超时图片


def get_random_headers():
    return {
        'User-Agent': random.choice(USER_AGENTS),
        'Referer': 'https://www.ikanwzd.cc/',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    }


def scrape_book_ids(url, max_retry=SCRAPE_MAX_RETRY):
    books = []
    for attempt in range(1, max_retry + 1):
        try:
            # 使用配置区的重试延迟
            time.sleep(random.uniform(SCRAPE_RETRY_DELAY_MIN, SCRAPE_RETRY_DELAY_MAX))
            response = requests.get(url, headers=get_random_headers(), timeout=SCRAPE_REQUEST_TIMEOUT)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                book_list = soup.find('ul', {'id': 'update_30'})
                if book_list:
                    for li in book_list.find_all('li'):
                        link = li.find('a', href=re.compile(r'/book/\d+'))
                        if link:
                            book_id = re.search(r'/book/(\d+)', link['href']).group(1)
                            title = re.sub(r'[\\/*?:"<>|]', '', link.text.strip())
                            books.append({'id': book_id, 'title': title})
                break  # 成功则跳出重试循环
            else:
                print(f"第{attempt}次抓取失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"第{attempt}次抓取失败: {str(e)}")
        if attempt < max_retry:
            delay = random.uniform(SCRAPE_RETRY_DELAY_MIN, SCRAPE_RETRY_DELAY_MAX)
            print(f"等待{delay:.1f}秒后重试...({attempt}/{max_retry})")
            time.sleep(delay)
    print(f"\n已处理页面：{url}")
    return books


class ComicDownloader:
    chapter_locks = {}  # 静态变量：章节级别锁

    @staticmethod
    def get_chapter_lock(chapter_key):
        if chapter_key not in ComicDownloader.chapter_locks:
            ComicDownloader.chapter_locks[chapter_key] = Lock()
        return ComicDownloader.chapter_locks[chapter_key]

    @staticmethod
    def sanitize_filename(filename):
        """
        清理文件名，确保同时满足Windows和Debian的文件系统要求
        Windows不允许: \\ / : * ? " < > |
        Debian/Linux通常避免: / (已包含在Windows限制中) 和其他控制字符
        """
        # 移除Windows和Linux都不允许的特殊字符
        filename = re.sub(r'[\\/*?:"<>|\t\r\n]', '', filename)

        # 移除所有控制字符(ASCII 0-31)和删除字符(ASCII 127)
        filename = ''.join(c for c in filename if ord(c) >= 32 and ord(c) != 127)

        # 移除前导和尾随的空格和点号(Windows不允许)
        filename = filename.strip(' .')

        # 确保文件名不为空
        if not filename:
            filename = "unnamed"

        return filename

    def __init__(self, book_id, user_agents, chapter_workers, compare_log_ref, compare_log_lock):
        self.book_id = book_id
        # 根据线程模式设置章节下载线程数
        self.chapter_workers = chapter_workers  # 由外部传入的线程数，已考虑自动/手动模式
        self.download_dir = "D:/ComicsDownloads/漫小肆"
        self.temp_dir = os.path.join(self.download_dir, "漫小肆章节图片下载", f"comic_temp_{self.book_id}")
        self.output_dir = os.path.join(self.download_dir, 'ComicPDFs')
        self.merged_dir = os.path.join(self.download_dir, '漫小肆漫画PDF')
        self.log_dir = os.path.join(self.download_dir, '漫小肆漫画下载日志')
        self.cover_dir = os.path.join(self.download_dir, '漫小肆封面图')
        self.nas_cache_dir = os.path.join(self.download_dir, '漫小肆漫画NAS中间件', str(self.book_id))
        self.compare_log_path = os.path.join(self.log_dir, '漫小肆漫画下载对比.log')

        # 确保所有目录存在
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.merged_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)
        os.makedirs(self.cover_dir, exist_ok=True)
        os.makedirs(self.nas_cache_dir, exist_ok=True)

        self.base_url = f"https://www.ikanwzd.cc/book/{self.book_id}"
        self.comic_name = "未命名漫画"
        self.chapter_titles = []
        self.chapter_sort_keys = []
        self.chapter_count = 0
        self.first_chapter = 0
        self.last_chapter = 0
        self.is_first_download = False  # 标记是否首次下载
        self.cleanup_done = False  # 新增：清理完成标志

        self.user_agents = user_agents
        self.ua_index = 0
        self.session = requests.Session()
        self.logger = self.setup_logger()

        # 使用共享的日志字典和锁
        self.compare_log = compare_log_ref
        self.compare_log_lock = compare_log_lock

    def setup_logger(self):
        logger = logging.getLogger(f"book_{self.book_id}")

        # 避免日志重复输出：如果logger已存在，则清除所有处理程序
        if logger.handlers:
            for handler in logger.handlers[:]:
                logger.removeHandler(handler)

        logger.setLevel(logging.INFO)
        log_path = os.path.join(self.log_dir, f"{self.book_id}_download_log.txt")
        file_handler = logging.FileHandler(log_path, mode='a', encoding='utf-8')
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))

        # 设置propagate=False，避免日志消息向上传递到root logger
        logger.propagate = False

        logger.addHandler(file_handler)
        return logger

    @staticmethod
    def test_nas_connection():
        if not NAS_CONFIG.get('enabled', False):
            print("NAS上传未启用，跳过连接测试。")
            return True
        conn = None
        try:
            conn = SMBConnection(
                NAS_CONFIG['username'],
                NAS_CONFIG['password'],
                NAS_CONFIG['client_name'],
                NAS_CONFIG['server_name'],
                use_ntlm_v2=True
            )
            server_ip = NAS_CONFIG['server_ip']
            print(f"尝试连接NAS服务器: {server_ip}")
            if conn.connect(server_ip, 445, timeout=600):
                print("NAS连接测试成功！")
                return True
            else:
                print("NAS连接失败！")
                return False
        except Exception as e:
            print(f"NAS连接测试异常: {str(e)}")
            return False
        finally:
            if conn:
                conn.close()

    def get_next_ua(self):
        ua = random.choice(self.user_agents)
        return {"User-Agent": ua}

    def get_soup(self, url):
        retry_count = 0
        max_retry = SCRAPE_MAX_RETRY  # 使用配置区参数
        while retry_count < max_retry:
            try:
                headers = self.get_next_ua()
                response = self.session.get(url, headers=headers, timeout=SCRAPE_REQUEST_TIMEOUT)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return BeautifulSoup(response.text, 'html.parser')
            except Exception as e:
                retry_count += 1
                if retry_count < max_retry:
                    sleep_time = random.uniform(SCRAPE_RETRY_DELAY_MIN, SCRAPE_RETRY_DELAY_MAX)
                    tqdm.write(f"[{url}] 请求失败: {str(e)}, 第{retry_count}次重试, 等待{sleep_time:.1f}秒...")
                    time.sleep(sleep_time)
                else:
                    tqdm.write(f"[{url}] 请求失败: {str(e)}, 已达到最大重试次数({max_retry})")
        return None

    def get_all_chapters(self):
        # 添加重试逻辑，最多重试SCRAPE_MAIN_LOOP_MAX_RETRY次
        max_retries = SCRAPE_MAIN_LOOP_MAX_RETRY
        for retry_count in range(max_retries):
            soup = self.get_soup(self.base_url)
            if soup:
                break
            else:
                tqdm.write(f"[{self.book_id}] ❌ 获取页面失败，第{retry_count + 1}次重试中...")
                time.sleep(random.uniform(SCRAPE_RETRY_DELAY_MIN, SCRAPE_RETRY_DELAY_MAX))  # 随机等待

        if not soup:
            tqdm.write(f"[{self.book_id}] ❌ 获取页面失败，已重试{max_retries}次")
            return []

        # 获取漫画名称
        title_element = soup.select_one('div.book-detail h1.title') or soup.select_one(
            'h1.comic-title') or soup.select_one('title')
        if title_element:
            raw_title = title_element.text.strip()
            self.comic_name = self.sanitize_filename(raw_title.split(',')[0])[:50]

        chapter_list = soup.select('div#chapterlistload ul.view-win-list li a[href]')
        seen, chapters = set(), []
        for i, link in enumerate(chapter_list):
            href = link.get('href')
            if href:
                full_url = urljoin(self.base_url, href)
                # 使用sanitize_filename方法清理标题
                title = self.sanitize_filename(link.text.strip())
                chapter_title = f"{i + 1:03d}_{title}"
                if full_url not in seen:
                    seen.add(full_url)
                    chapters.append((chapter_title, full_url, i + 1))

        if chapters:
            self.first_chapter = chapters[0][2]
            self.last_chapter = chapters[-1][2]

        self.chapter_titles = [ch[0] for ch in chapters]
        self.chapter_sort_keys = [ch[2] for ch in chapters]
        self.chapter_count = len(chapters)
        self.logger.info(f"[{self.book_id}] 🔎 发现 {self.chapter_count} 个章节")
        tqdm.write(f"[{self.book_id}] 发现 {self.chapter_count} 个章节")

        # 预下载验证机制：验证章节序号是否连续
        if ENABLE_PREDOWNLOAD_VERIFICATION:
            self.verify_chapter_list(chapters)

        return chapters

    def verify_chapter_list(self, chapters):
        """预下载验证：检查章节列表是否完整，序号是否连续"""
        if not chapters or len(chapters) < 2:
            return True

        # 创建章节映射表
        chapter_map = {}
        for title, url, sort_key in chapters:
            chapter_map[sort_key] = {"title": title, "url": url}

        # 检查章节序号是否连续
        sort_keys = sorted(chapter_map.keys())
        gaps = []
        for i in range(1, len(sort_keys)):
            gap = sort_keys[i] - sort_keys[i - 1]
            if gap > CHAPTER_GAP_THRESHOLD:
                gaps.append((sort_keys[i - 1], sort_keys[i], gap))

        if gaps:
            self.logger.warning(f"检测到章节序号不连续: {gaps}")
            tqdm.write(f"[{self.book_id}] ⚠️ 检测到章节序号不连续:")
            for prev, curr, gap in gaps:
                tqdm.write(f"  - 从章节 {prev} 到章节 {curr}，间隔 {gap}")

            # 计算缺失比例
            missing_count = sum(gap - 1 for _, _, gap in gaps)
            total_expected = sort_keys[-1] - sort_keys[0] + 1
            missing_ratio = missing_count / total_expected

            if missing_ratio > MISSING_CHAPTER_THRESHOLD:
                tqdm.write(
                    f"[{self.book_id}] ⚠️ 缺失章节比例 {missing_ratio:.2%} 超过阈值 {MISSING_CHAPTER_THRESHOLD:.2%}")
                tqdm.write(f"[{self.book_id}] ⚠️ 建议检查网站章节列表是否完整")

                # 尝试获取网站最新章节数据
                self.try_fetch_latest_chapters()

            # 记录章节映射表到日志
            self.logger.info(f"章节映射表: {chapter_map}")

        return len(gaps) == 0

    def try_fetch_latest_chapters(self):
        """尝试重新获取最新的章节列表，确保数据是最新的"""
        try:
            tqdm.write(f"[{self.book_id}] 尝试重新获取最新章节数据...")
            # 清除会话缓存
            self.session = requests.Session()

            # 使用不同的User-Agent
            old_ua_index = self.ua_index
            while self.ua_index == old_ua_index:
                self.ua_index = random.randint(0, len(self.user_agents) - 1)

            # 增加延迟，避免被网站识别为爬虫
            time.sleep(random.uniform(2, 5))

            # 重新获取章节列表
            soup = self.get_soup(self.base_url)
            if not soup:
                tqdm.write(f"[{self.book_id}] ❌ 重新获取章节列表失败")
                return False

            chapter_list = soup.select('div#chapterlistload ul.view-win-list li a[href]')
            if not chapter_list:
                tqdm.write(f"[{self.book_id}] ❌ 未找到章节列表元素")
                return False

            # 计算新获取的章节数量
            new_chapter_count = len(set(link.get('href') for link in chapter_list if link.get('href')))

            if new_chapter_count > self.chapter_count:
                tqdm.write(
                    f"[{self.book_id}] ⚠️ 检测到网站章节数量变化: 原{self.chapter_count}章 → 现{new_chapter_count}章")
                tqdm.write(f"[{self.book_id}] ⚠️ 建议重新运行下载任务以获取最新章节")
                self.logger.warning(f"检测到网站章节数量变化: 原{self.chapter_count}章 → 现{new_chapter_count}章")
                return True
            elif new_chapter_count < self.chapter_count:
                tqdm.write(
                    f"[{self.book_id}] ⚠️ 检测到网站章节数量减少: 原{self.chapter_count}章 → 现{new_chapter_count}章")
                tqdm.write(f"[{self.book_id}] ⚠️ 网站可能移除了部分章节，请手动检查")
                self.logger.warning(f"检测到网站章节数量减少: 原{self.chapter_count}章 → 现{new_chapter_count}章")
                return False
            else:
                tqdm.write(f"[{self.book_id}] ✅ 网站章节数量未变化，仍为{self.chapter_count}章")
                return True

        except Exception as e:
            tqdm.write(f"[{self.book_id}] ❌ 重新获取章节数据异常: {str(e)}")
            self.logger.error(f"重新获取章节数据异常: {str(e)}")
            return False

    def check_update_needed(self):
        # 使用日志锁保护共享状态
        with self.compare_log_lock:
            current_entry = self.compare_log.get(str(self.book_id), {})
            local_chapters = current_entry.get("local_chapters", 0)
            website_chapters = self.chapter_count

            # 打印详细信息用于调试
            debug_msg = f"对比日志信息: 本地章节={local_chapters}, 网站章节={website_chapters}"
            tqdm.write(f"[{self.book_id}] {debug_msg}")
            self.logger.info(f"[{self.book_id}] {debug_msg}")

            # 优先检查是否首次下载
            if local_chapters == 0:
                self.is_first_download = True
                new_chapter_indexes = list(range(website_chapters))
                return True, "首次下载", new_chapter_indexes
            # 然后检查是否有更新
            elif website_chapters > local_chapters:
                new_chapter_indexes = list(range(local_chapters, website_chapters))
                return True, f"有更新（本地:{local_chapters} 网站:{website_chapters}）", new_chapter_indexes
            else:
                return False, "无需更新", []

    @staticmethod
    def compute_file_hash(file_path):
        """计算文件的SHA256哈希值"""
        hash_sha256 = hashlib.sha256()
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            return None

    def update_compare_log(self, merged_pdf_path=None):
        try:
            pdf_size = 0
            pdf_hash = ""
            if merged_pdf_path and os.path.exists(merged_pdf_path):
                pdf_size = os.path.getsize(merged_pdf_path)
                pdf_hash = self.compute_file_hash(merged_pdf_path)
            # 使用日志锁保护共享状态
            with self.compare_log_lock:
                # 更新或创建新条目
                self.compare_log[str(self.book_id)] = {
                    "comic_name": self.comic_name,
                    "website_chapters": self.chapter_count,
                    "local_chapters": self.chapter_count,
                    "last_updated": datetime.datetime.now().isoformat(),
                    "pdf_file": os.path.basename(merged_pdf_path) if merged_pdf_path else "",
                    "pdf_size": pdf_size,
                    "pdf_hash": pdf_hash
                }

            # 使用全局锁保存整个日志
            with COMPARE_LOG_LOCK:
                with open(self.compare_log_path, 'w', encoding='utf-8') as f:
                    json.dump(self.compare_log, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"更新日志失败: {str(e)}")
            tqdm.write(f"[{self.book_id}] 更新日志失败: {str(e)}")
            # 回退到保存当前状态
            try:
                pdf_size = 0
                pdf_hash = ""
                if merged_pdf_path and os.path.exists(merged_pdf_path):
                    pdf_size = os.path.getsize(merged_pdf_path)
                    pdf_hash = self.compute_file_hash(merged_pdf_path)
                self.compare_log[str(self.book_id)] = {
                    "comic_name": self.comic_name,
                    "website_chapters": self.chapter_count,
                    "local_chapters": self.chapter_count,
                    "last_updated": datetime.datetime.now().isoformat(),
                    "pdf_file": os.path.basename(merged_pdf_path) if merged_pdf_path else "",
                    "pdf_size": pdf_size,
                    "pdf_hash": pdf_hash
                }
                with COMPARE_LOG_LOCK:
                    with open(self.compare_log_path, 'w', encoding='utf-8') as f:
                        json.dump(self.compare_log, f, indent=2, ensure_ascii=False)
            except:
                self.logger.error("回退日志保存也失败")

    def find_images_in_chapter(self, chapter_url):
        # 添加重试逻辑，最多重试3次
        max_retries = 3
        for retry_count in range(max_retries):
            soup = self.get_soup(chapter_url)
            if soup:
                break
            else:
                tqdm.write(f"[{self.book_id}] ❌ 获取章节页面失败，第{retry_count + 1}次重试中...")
                time.sleep(random.uniform(2, 5))  # 随机等待2-5秒后重试

        if not soup:
            tqdm.write(f"[{self.book_id}] ❌ 获取章节页面失败，已重试{max_retries}次")
            return []

        imgs = soup.select('img[data-original], img[data-src], img[src]')
        img_urls = [img.get('data-original') or img.get('data-src') or img.get('src') for img in imgs]
        return [urljoin(self.base_url, url) for url in img_urls if url and url.startswith('http')]

    def check_image_count(self, img_list_or_dir, min_count=1):
        """校验图片数量是否大于等于min_count。img_list_or_dir可以是图片url列表或图片文件夹路径。"""
        if isinstance(img_list_or_dir, list):
            return len(img_list_or_dir) >= min_count
        elif isinstance(img_list_or_dir, str) and os.path.exists(img_list_or_dir):
            return len([f for f in os.listdir(img_list_or_dir) if f.endswith('.jpg')]) >= min_count
        return False

    def download_image_until_success(self, url, idx, chapter_title, chapter_dir):
        global error_count, success_count
        img_path = os.path.join(chapter_dir, f"{idx + 1:03d}.jpg")
        if os.path.exists(img_path):
            return None

        start_time = time.time()
        retry_count = 0
        max_retry = IMAGE_MAX_RETRY  # 使用配置区的最大重试次数

        while retry_count < max_retry:
            try:
                headers = self.get_next_ua()
                response = self.session.get(url, headers=headers, timeout=30)
                if response.status_code == 200:
                    img_data = response.content
                    success_count += 1  # 统计成功
                    return img_data
                else:
                    error_count += 1  # 统计失败
                    raise Exception(f"状态码 {response.status_code}")
            except Exception as e:
                elapsed = time.time() - start_time
                error_count += 1  # 统计失败

                # 如果已经超时，记录日志并根据策略决定是否继续
                if elapsed > IMAGE_TIMEOUT_SECONDS:  # 使用配置区的超时时间
                    tqdm.write(f"[{self.book_id}] 图片下载超时: {url}")
                    tqdm.write(f"[{self.book_id}] 章节: {chapter_title}, 图片索引: {idx + 1}")
                    tqdm.write(f"[{self.book_id}] 已尝试 {retry_count + 1}/{max_retry} 次, 耗时 {elapsed:.1f} 秒")

                    # 检查是否已经设置了全局放弃标志
                    if USER_INTERRUPT_EVENT.is_set():
                        tqdm.write(f"[{self.book_id}] 全局放弃标志已设置，跳过此图片")
                        return None

                    # 如果超时时间超过了最大超时时间的1.5倍，自动放弃
                    if elapsed > IMAGE_TIMEOUT_SECONDS * 1.5:
                        tqdm.write(f"[{self.book_id}] 严重超时，自动放弃图片: {url}")
                        return None

                # 进行递增等待（等待时间随重试次数增加）
                sleep_time = random.uniform(2, 5) * (1 + retry_count * 0.2)  # 等待时间随重试次数增加
                tqdm.write(
                    f"[{self.book_id}] 图片下载失败: {str(e)}, 第{retry_count + 1}/{max_retry}次重试, 等待{sleep_time:.1f}秒...")
                time.sleep(sleep_time)
                retry_count += 1

        # 达到最大重试次数
        tqdm.write(f"[{self.book_id}] 达到最大重试次数({max_retry})，放弃图片: {url}")
        return None

    def download_images(self, img_urls, chapter_title):
        if len(img_urls) == 0:
            self.logger.warning(f"{chapter_title} 图片数量为 0，跳过！")
            return [], {"total": 0, "downloaded": 0, "skipped": 0, "skip_rate": 1.0}

        # 清理章节标题，确保没有非法字符
        chapter_title = self.sanitize_filename(chapter_title)
        chapter_dir = os.path.join(self.temp_dir, chapter_title)
        os.makedirs(chapter_dir, exist_ok=True)

        existing_imgs = {f for f in os.listdir(chapter_dir) if f.endswith('.jpg')}
        downloaded_files = []
        missing_count = 0
        skipped_timeout_count = 0
        skipped_max_retry_count = 0

        # 计算章节线程数总数
        chapter_threads_total = current_workers * current_total_workers
        # 创建进度条，desc中加入三项（新描述）
        pbar = tqdm(
            total=len(img_urls),
            desc=f"📥 [下载] {self.comic_name}-{chapter_title} | 漫画:{current_total_workers} 章节:{current_workers}",
            position=0,
            leave=True,
            colour='cyan'
        )

        for idx, url in enumerate(img_urls):
            filename = f"{idx + 1:03d}.jpg"
            filepath = os.path.join(chapter_dir, filename)

            if filename in existing_imgs:
                downloaded_files.append(filepath)
                pbar.update(1)
                pbar.set_description(
                    f"📥 [下载] {self.comic_name}-{chapter_title} | 漫画:{current_total_workers} 章节:{current_workers}")
                continue

            # 下载图片并校验
            img_data = None
            for _ in range(IMAGE_MAX_RETRY):
                img_data = self.download_image_until_success(url, idx, chapter_title, chapter_dir)
                if img_data is None:
                    # 跳过的情况可能是超时或达到最大重试次数
                    if _ == IMAGE_MAX_RETRY - 1:
                        skipped_max_retry_count += 1
                        self.logger.warning(f"{chapter_title} 第{idx + 1}张图片达到最大重试次数，跳过")
                    else:
                        skipped_timeout_count += 1
                        self.logger.warning(f"{chapter_title} 第{idx + 1}张图片下载超时，跳过")
                    break
                with open(filepath, 'wb') as f:
                    f.write(img_data)
                if os.path.exists(filepath):
                    downloaded_files.append(filepath)
                    pbar.update(1)
                    pbar.set_description(
                        f"📥 [下载] {self.comic_name}-{chapter_title} | 漫画:{current_total_workers} 章节:{current_workers}")
                    break
                else:
                    tqdm.write(f"[{self.book_id}] {chapter_title} 图片写入失败，重试...")
            else:
                missing_count += 1
                self.logger.warning(f"放弃下载图片: {url}")
            pbar.set_description(
                f"📥 [下载] {self.comic_name}-{chapter_title} | 漫画:{current_total_workers} 章节:{current_workers}")

        pbar.close()

        # 统计详细信息
        total_expected = len(img_urls)
        total_downloaded = len(downloaded_files)
        total_skipped = skipped_timeout_count + skipped_max_retry_count
        skip_rate = total_skipped / total_expected if total_expected > 0 else 1.0

        stats = {
            "total": total_expected,
            "downloaded": total_downloaded,
            "skipped": total_skipped,
            "skip_rate": skip_rate
        }

        if missing_count > 0 or skipped_timeout_count > 0 or skipped_max_retry_count > 0:
            self.logger.warning(
                f"{chapter_title} 统计: 总图片{total_expected}张, 下载成功{total_downloaded}张, 跳过{total_skipped}张(超时:{skipped_timeout_count}, 重试上限:{skipped_max_retry_count}), 缺失{missing_count}张, 跳过率:{skip_rate:.2%}")
            tqdm.write(
                f"[{self.book_id}] {chapter_title} 统计: 总图片{total_expected}张, 下载成功{total_downloaded}张, 跳过{total_skipped}张(超时:{skipped_timeout_count}, 重试上限:{skipped_max_retry_count}), 缺失{missing_count}张, 跳过率:{skip_rate:.2%}")

        tqdm.write(f"[{self.book_id}] {chapter_title} 图片下载完成")
        return downloaded_files, stats

    def process_chapter(self, chapter_info):
        """处理单个章节的下载和转换过程"""
        idx, chapter_title, chapter_url, sort_key = chapter_info  # 添加sort_key用于排序
        self.logger.info(f"开始处理章节: {chapter_title}")

        try:
            # 获取图片URLs
            img_urls = self.find_images_in_chapter(chapter_url)
            if not img_urls:
                self.logger.warning(f"{chapter_title} 未获取到图片链接")
                return None, sort_key, {"total": 0, "downloaded": 0, "skipped": 0, "skip_rate": 1.0}  # 返回排序键和统计信息

            # 下载图片
            downloaded_files, stats = self.download_images(img_urls, chapter_title)
            if not downloaded_files:
                self.logger.warning(f"{chapter_title} 图片下载失败")
                return None, sort_key, stats  # 返回排序键和统计信息

            # 过滤失败的下载图片，同时传递原始URL列表
            filtered_files = self.filter_failed_images(downloaded_files, img_urls)
            if not filtered_files:
                self.logger.warning(f"{chapter_title} 过滤后无有效图片")
                tqdm.write(f"[{self.book_id}] {chapter_title} 过滤后无有效图片，跳过处理")
                return None, sort_key, stats

            # 更新统计信息
            filtered_ratio = len(filtered_files) / len(downloaded_files) if downloaded_files else 0
            tqdm.write(
                f"[{self.book_id}] {chapter_title} 图片过滤率: {(1 - filtered_ratio):.1%} (保留{len(filtered_files)}/{len(downloaded_files)}张)")

            # 验证章节内容完整性
            is_content_valid, content_msg = self.verify_chapter_content(chapter_title, chapter_url, filtered_files)
            if not is_content_valid:
                # 检查失败原因是否仅为图片质量问题
                if "图片质量问题" in content_msg and "内容不完整" not in content_msg:
                    # 图片质量问题只记录警告，不影响处理
                    self.logger.warning(f"{chapter_title} 图片质量提示: {content_msg}")
                    tqdm.write(f"[{self.book_id}] ℹ️ {chapter_title} 图片质量提示: {content_msg}")
                    # 继续处理，不需要额外提示
                else:
                    # 真正的内容不完整问题
                    self.logger.warning(f"{chapter_title} 内容验证失败: {content_msg}")
                    tqdm.write(f"[{self.book_id}] ⚠️ {chapter_title} 内容验证失败: {content_msg}")

                    # 如果内容不完整但仍有一些图片，尝试继续处理
                    if len(filtered_files) > 0:
                        tqdm.write(f"[{self.book_id}] 尝试使用不完整内容继续处理...")
                    else:
                        return None, sort_key, stats
            else:
                tqdm.write(f"[{self.book_id}] ✅ {chapter_title} 内容验证通过: {content_msg}")

            # 转换为PDF
            pdf_path = self.convert_images_to_pdf(filtered_files, chapter_title, sort_key)
            return pdf_path, sort_key, stats  # 返回PDF路径、排序键和统计信息
        except Exception as e:
            self.logger.error(f"章节处理失败: {chapter_title} - {str(e)}")
            return None, sort_key, {"total": 0, "downloaded": 0, "skipped": 0, "skip_rate": 1.0}  # 返回排序键和统计信息

    def convert_images_to_pdf(self, image_files, chapter_title, sort_key):
        book_folder = os.path.join(self.output_dir, str(self.book_id))
        os.makedirs(book_folder, exist_ok=True)

        # 清理章节标题，确保没有非法字符
        chapter_title = self.sanitize_filename(chapter_title)
        pdf_name = f"{sort_key:04d}_{self.comic_name}-{self.book_id}-{chapter_title}.pdf"
        pdf_path = os.path.join(book_folder, pdf_name)

        if os.path.exists(pdf_path):
            return pdf_path

        try:
            # 只有在完全没有图片文件时才报错，只要有一张也要尝试生成
            if not image_files:
                self.logger.error(f"{chapter_title} 没有可用的图片文件，不生成PDF")
                tqdm.write(f"[{self.book_id}] {chapter_title} 没有可用的图片文件，不生成PDF")
                return None

            # 由于已经在process_chapter中过滤过图片，这里直接使用
            images = []
            for img_path in image_files:
                try:
                    # 再次检查文件是否存在
                    if not os.path.exists(img_path):
                        self.logger.warning(f"{chapter_title} 处理时图片已被删除: {img_path}")
                        continue

                    img = Image.open(img_path).convert("RGB")
                    images.append(img)
                except Exception as e:
                    self.logger.error(f"{chapter_title} 打开图片失败: {img_path} - {e}")

            # 再次检查处理后的图片数量，只要有1张就生成PDF
            if not images:
                self.logger.error(f"{chapter_title} 处理后无有效图片，不生成PDF")
                tqdm.write(f"[{self.book_id}] {chapter_title} 处理后无有效图片，不生成PDF")
                return None

            # 添加章节元数据
            if ENABLE_CHAPTER_METADATA:
                metadata = {
                    '/Title': f"{self.comic_name} - 第{sort_key}章 - {chapter_title}",
                    '/Author': "漫小肆下载器",
                    '/Subject': f"章节序号: {sort_key}, 总章节数: {self.chapter_count}",
                    '/Keywords': f"漫画,{self.comic_name},章节,{sort_key},{self.book_id}",
                    '/Producer': f"漫小肆下载器 v1.0",
                    '/CreationDate': datetime.datetime.now().strftime("D:%Y%m%d%H%M%S"),
                }

                # 创建一个带有元数据的PDF
                from reportlab.lib.pagesizes import letter
                from reportlab.pdfgen import canvas
                from reportlab.lib.utils import ImageReader
                import io

                try:
                    # 创建临时PDF文件
                    temp_pdf_path = pdf_path + ".temp"
                    c = canvas.Canvas(temp_pdf_path, pagesize=letter)

                    # 设置元数据
                    if '/Title' in metadata:
                        c.setTitle(metadata['/Title'])
                    if '/Author' in metadata:
                        c.setAuthor(metadata['/Author'])
                    if '/Subject' in metadata:
                        c.setSubject(metadata['/Subject'])
                    if '/Keywords' in metadata:
                        c.setKeywords(metadata['/Keywords'])
                    # 其它自定义元数据可用c._doc.info['/xxx'] = value
                    for key, value in metadata.items():
                        if key not in ['/Title', '/Author', '/Subject', '/Keywords']:
                            try:
                                c._doc.info[key] = value
                            except Exception:
                                pass

                    # 添加图片到PDF
                    for img in images:
                        img_width, img_height = img.size
                        # 调整页面大小以匹配图片
                        c.setPageSize((img_width, img_height))
                        # 将图片绘制到PDF
                        c.drawImage(ImageReader(img), 0, 0, width=img_width, height=img_height)
                        c.showPage()

                    c.save()

                    # 重命名临时文件为最终文件
                    if os.path.exists(temp_pdf_path):
                        if os.path.exists(pdf_path):
                            os.remove(pdf_path)
                        os.rename(temp_pdf_path, pdf_path)
                        tqdm.write(f"[{self.book_id}] {chapter_title} PDF生成成功 (带元数据, {len(images)}张图片)")

                        # 记录PDF大小到日志
                        if os.path.exists(pdf_path):
                            try:
                                pdf_size = os.path.getsize(pdf_path)
                                # 这个只记录单章节PDF大小，不用调用record_chapter_pdf_size
                                self.logger.info(f"{chapter_title} PDF大小: {pdf_size / 1024 / 1024:.2f}MB")
                            except Exception as e:
                                self.logger.error(f"获取PDF大小失败: {str(e)}")

                        return pdf_path
                except Exception as e:
                    self.logger.error(f"使用元数据生成PDF失败: {str(e)}，回退到标准方法")
                    tqdm.write(f"[{self.book_id}] 元数据PDF生成失败，回退到标准方法: {str(e)}")
                    # 如果临时文件存在但出错，删除它
                    if 'temp_pdf_path' in locals() and os.path.exists(temp_pdf_path):
                        try:
                            os.remove(temp_pdf_path)
                        except:
                            pass

            # 关键：不要加resolution/quality参数
            images[0].save(pdf_path, save_all=True, append_images=images[1:])
            # 添加PDF生成成功的提示信息
            tqdm.write(f"[{self.book_id}] {chapter_title} PDF生成成功 ({len(images)}张图片)")

            # 记录PDF大小到日志
            if os.path.exists(pdf_path):
                try:
                    pdf_size = os.path.getsize(pdf_path)
                    # 这个只记录单章节PDF大小，不用调用record_chapter_pdf_size
                    self.logger.info(f"{chapter_title} PDF大小: {pdf_size / 1024 / 1024:.2f}MB")
                except Exception as e:
                    self.logger.error(f"获取PDF大小失败: {str(e)}")

            return pdf_path
        except Exception as e:
            self.logger.error(f"{chapter_title} PDF ❌ 生成失败: {str(e)}")
            tqdm.write(f"[{self.book_id}] {chapter_title} PDF生成失败: {str(e)}")
            return None

    def merge_pdfs_first_download(self, pdf_files) -> tuple:
        from PyPDF2 import PdfMerger, PdfReader
        import os
        # 合并前效验
        chapter_numbers = []
        last_chapter_num = None
        for pdf_path in pdf_files:
            if not os.path.exists(pdf_path):
                tqdm.write(f"[首次下载效验][{self.book_id}] 缺失PDF: {pdf_path}")
                return None, "", None
            try:
                with open(pdf_path, 'rb') as f:
                    reader = PdfReader(f)
                    page_count = len(reader.pages)
                    size = os.path.getsize(pdf_path)
                    if page_count < 1:
                        tqdm.write(f"[首次下载效验][{self.book_id}] PDF页数异常: {pdf_path} 页数={page_count}")
                        return None, "", None
                    if size < 10*1024:
                        tqdm.write(f"[首次下载效验][{self.book_id}] PDF体积过小: {pdf_path} 体积={size/1024:.1f}KB")
                        return None, "", None
                    filename = os.path.basename(pdf_path)
                    if '封面' not in filename:
                        match = re.match(r"(\d+)_", filename)
                        if match:
                            chapter_num = int(match.group(1))
                            chapter_numbers.append(chapter_num)
                    tqdm.write(f"[首次下载效验][{self.book_id}] PDF: {filename} 体积: {size/1024/1024:.2f}MB 页数: {page_count} 正常")
            except Exception as e:
                tqdm.write(f"[首次下载效验][{self.book_id}] PDF无法读取: {pdf_path} 错误: {str(e)}")
                return None, "", None
        # 检查章节序号连续性
        if chapter_numbers:
            chapter_numbers.sort()
            for i in range(1, len(chapter_numbers)):
                gap = chapter_numbers[i] - chapter_numbers[i-1]
                if gap > 1:
                    tqdm.write(f"[首次下载效验][{self.book_id}] 章节序号不连续: {chapter_numbers[i-1]}->{chapter_numbers[i]} gap={gap}")
                    return None, "", None
        # ...原有合并逻辑...
        merger = PdfMerger()
        chapter_bookmarks = []
        has_cover = False
        page_offset = 0
        total_size = 0
        for pdf_path in pdf_files:
            try:
                with open(pdf_path, 'rb') as f:
                    reader = PdfReader(f)
                    size = os.path.getsize(pdf_path)
                    total_size += size
                    page_count = len(reader.pages)
                    filename = os.path.basename(pdf_path)
                    if '封面' in filename:
                        bookmark_title = "封面"
                        has_cover = True
                    else:
                        # 优化正则，优先提取章节号和标题
                        # 支持如 001_12-第12话-标题.pdf、001_第12话-标题.pdf、001_标题.pdf
                        match = re.match(r"(\d+)_((?:\d+-)?第?(\d+)话?-?)?(.+?)?\.pdf", filename)
                        if match:
                            chapter_num = int(match.group(1))
                            # 优先用 group(3) 作为章节号，group(4) 作为标题
                            chapter_no = match.group(3)
                            chapter_title = match.group(4) if match.group(4) else ""
                            if chapter_no:
                                if chapter_title:
                                    bookmark_title = f"第{chapter_no}章 - {chapter_title}"
                                else:
                                    bookmark_title = f"第{chapter_no}章"
                            else:
                                # 没有章节号，降级为第N章或只用标题
                                if chapter_title:
                                    bookmark_title = f"第{chapter_num}章 - {chapter_title}"
                                else:
                                    bookmark_title = f"第{chapter_num}章"
                        else:
                            match = re.match(r"(\d+)_", filename)
                            if match:
                                chapter_num = int(match.group(1))
                                bookmark_title = f"第{chapter_num}章"
                            else:
                                bookmark_title = filename.split('.')[0]
                    chapter_bookmarks.append((bookmark_title, page_offset))
                    page_offset += page_count
                    merger.append(pdf_path)
                    tqdm.write(f"[首次下载][{self.book_id}] PDF: {filename} 体积: {size/1024/1024:.2f}MB 页数: {page_count}")
            except Exception as e:
                tqdm.write(f"[首次下载][{self.book_id}] PDF文件无效: {pdf_path} - {str(e)}，跳过")
        if len(merger.pages) < 2:
            tqdm.write(f"[首次下载][{self.book_id}] 合并后页数不足")
            return None, "", None
        # 生成合并PDF名
        if self.last_chapter >= self.first_chapter:
            range_text = f"({self.first_chapter}-{self.last_chapter}话)"
        else:
            range_text = f"({self.chapter_count}话)"
        merged_name = self.sanitize_filename(f"{self.comic_name}+{range_text}-漫小肆漫画({self.book_id}).pdf")
        merged_path = os.path.join(self.merged_dir, merged_name)
        os.makedirs(self.merged_dir, exist_ok=True)
        # 写入书签（只写新章节和封面）
        try:
            for title, page in chapter_bookmarks:
                merger.add_outline_item(title, page)
            tqdm.write(f"[首次下载][{self.book_id}] 写入书签数: {len(chapter_bookmarks)}")
        except Exception as e:
            tqdm.write(f"[首次下载][{self.book_id}] 添加书签失败: {str(e)}")
        merger.write(merged_path)
        merger.close()
        # 合并后效验
        if not self.verify_pdf_content_first_download(merged_path):
            tqdm.write(f"[首次下载][{self.book_id}] 合并后效验失败")
            return None, "", None
        return merged_path, "", False

    def add_bookmarks_from_reader(self, merger, reader, offset=0, parent=None):
        # 递归提取并修正书签页码
        def _add_outline_items(outline, parent=None):
            for item in outline:
                if isinstance(item, list):
                    _add_outline_items(item, parent)
                else:
                    title = getattr(item, 'title', None) or item.get('/Title', '无标题') if isinstance(item, dict) else '无标题'
                    try:
                        page_num = reader.get_destination_page_number(item)
                    except Exception:
                        page_num = 0
                    try:
                        merger.add_outline_item(title, page_num + offset, parent=parent)
                    except Exception as e:
                        tqdm.write(f"[增量更新书签][{self.book_id}] 添加书签失败: {title} - {str(e)}")
        if hasattr(reader, 'outline') and reader.outline:
            _add_outline_items(reader.outline, parent)

    def extract_single_level_bookmarks(self, reader):
        bookmarks = []
        if hasattr(reader, 'outline') and reader.outline:
            for item in reader.outline:
                if isinstance(item, list):
                    continue  # 只处理单级
                title = getattr(item, 'title', None) or item.get('/Title', '无标题') if isinstance(item, dict) else '无标题'
                try:
                    page_num = reader.get_destination_page_number(item)
                except Exception:
                    page_num = 0
                bookmarks.append((title, page_num))
        return bookmarks

    def merge_pdfs_incremental(self, pdf_files) -> tuple:
        from PyPDF2 import PdfMerger, PdfReader
        import os
        # 合并前效验：NAS旧PDF
        with self.compare_log_lock:
            current_entry = self.compare_log.get(str(self.book_id), {})
            old_pdf_name = current_entry.get("pdf_file", "")
        nas_pdf_path = os.path.join(self.nas_cache_dir, old_pdf_name) if old_pdf_name else None
        if not old_pdf_name or not os.path.exists(nas_pdf_path):
            tqdm.write(f"[增量更新效验][{self.book_id}] 未找到NAS旧PDF，无法增量合并")
            return None, "", None
        try:
            with open(nas_pdf_path, 'rb') as f:
                reader = PdfReader(f)
                page_count = len(reader.pages)
                size = os.path.getsize(nas_pdf_path)
                if page_count < 1:
                    tqdm.write(f"[增量更新效验][{self.book_id}] 旧PDF页数异常: {nas_pdf_path} 页数={page_count}")
                    return None, old_pdf_name, None
                if size < 10*1024:
                    tqdm.write(f"[增量更新效验][{self.book_id}] 旧PDF体积过小: {nas_pdf_path} 体积={size/1024:.1f}KB")
                    return None, old_pdf_name, None
                tqdm.write(f"[增量更新效验][{self.book_id}] 旧PDF: {old_pdf_name} 体积: {size/1024/1024:.2f}MB 页数: {page_count} 正常")
                # 新增：提取旧PDF书签
                old_bookmarks = self.extract_single_level_bookmarks(reader)
        except Exception as e:
            tqdm.write(f"[增量更新效验][{self.book_id}] 旧PDF无法读取: {nas_pdf_path} 错误: {str(e)}")
            return None, old_pdf_name, None
        # 合并前效验：新增章节PDF
        chapter_numbers = []
        for pdf_path in pdf_files:
            if not os.path.exists(pdf_path):
                tqdm.write(f"[增量更新效验][{self.book_id}] 缺失新增PDF: {pdf_path}")
                return None, old_pdf_name, None
            try:
                with open(pdf_path, 'rb') as f:
                    reader = PdfReader(f)
                    page_count = len(reader.pages)
                    size = os.path.getsize(pdf_path)
                    if page_count < 1:
                        tqdm.write(f"[增量更新效验][{self.book_id}] 新增PDF页数异常: {pdf_path} 页数={page_count}")
                        return None, old_pdf_name, None
                    if size < 10*1024:
                        tqdm.write(f"[增量更新效验][{self.book_id}] 新增PDF体积过小: {pdf_path} 体积={size/1024:.1f}KB")
                        return None, old_pdf_name, None
                    filename = os.path.basename(pdf_path)
                    match = re.match(r"(\d+)_", filename)
                    if match:
                        chapter_num = int(match.group(1))
                        chapter_numbers.append(chapter_num)
                    tqdm.write(f"[增量更新效验][{self.book_id}] 新增PDF: {filename} 体积: {size/1024/1024:.2f}MB 页数: {page_count} 正常")
            except Exception as e:
                tqdm.write(f"[增量更新效验][{self.book_id}] 新增PDF无法读取: {pdf_path} 错误: {str(e)}")
                return None, old_pdf_name, None
        # 检查新增章节序号连续性
        if chapter_numbers:
            chapter_numbers.sort()
            for i in range(1, len(chapter_numbers)):
                gap = chapter_numbers[i] - chapter_numbers[i-1]
                if gap > 1:
                    tqdm.write(f"[增量更新效验][{self.book_id}] 新增章节序号不连续: {chapter_numbers[i-1]}->{chapter_numbers[i]} gap={gap}")
                    return None, old_pdf_name, None
        # ...原有合并逻辑...
        merger = PdfMerger()
        total_size = 0
        # 先加旧PDF
        try:
            with open(nas_pdf_path, 'rb') as f:
                old_reader = PdfReader(f)
                size = os.path.getsize(nas_pdf_path)
                total_size += size
                old_page_count = len(old_reader.pages)
                merger.append(nas_pdf_path)
                tqdm.write(f"[增量更新][{self.book_id}] 旧PDF: {old_pdf_name} 体积: {size/1024/1024:.2f}MB 页数: {old_page_count}")
        except Exception as e:
            tqdm.write(f"[增量更新][{self.book_id}] 旧PDF无效: {str(e)}")
            return None, old_pdf_name, None
        # 新增章节PDF
        chapter_bookmarks = []
        page_offset = old_page_count
        for pdf_path in pdf_files:
            try:
                with open(pdf_path, 'rb') as f:
                    reader = PdfReader(f)
                    size = os.path.getsize(pdf_path)
                    total_size += size
                    pc = len(reader.pages)
                    filename = os.path.basename(pdf_path)
                    # 使用首次合并的正则和逻辑，保证章节名和页码都正确
                    match = re.match(r"(\d+)_((?:\d+-)?第?(\d+)话?-?)?(.+?)?\.pdf", filename)
                    if match:
                        chapter_num = int(match.group(1))
                        chapter_no = match.group(3)
                        chapter_title = match.group(4) if match.group(4) else ""
                        # 去除章节名中的漫画名和BOOKID（更宽松）
                        if chapter_title:
                            # 去除漫画名
                            if self.comic_name in chapter_title:
                                chapter_title = chapter_title.replace(self.comic_name, '').strip('-_ ')
                            # 更彻底地去除BOOKID（无论前后有无"-"或"_"）
                            chapter_title = re.sub(rf'[-_]?{self.book_id}[-_]?', '', chapter_title)
                            chapter_title = chapter_title.strip('-_ ')
                        if chapter_no:
                            if chapter_title:
                                bookmark_title = f"第{chapter_no}章 - {chapter_title}"
                            else:
                                bookmark_title = f"第{chapter_no}章"
                        else:
                            if chapter_title:
                                bookmark_title = f"第{chapter_num}章 - {chapter_title}"
                            else:
                                bookmark_title = f"第{chapter_num}章"
                    else:
                        match = re.match(r"(\d+)_", filename)
                        if match:
                            chapter_num = int(match.group(1))
                            bookmark_title = f"第{chapter_num}章"
                        else:
                            bookmark_title = filename.split('.')[0]
                    chapter_bookmarks.append((bookmark_title, page_offset))
                    page_offset += pc
                    merger.append(pdf_path)
                    tqdm.write(f"[增量更新][{self.book_id}] 新章节PDF: {filename} 体积: {size/1024/1024:.2f}MB 页数: {pc}")
            except Exception as e:
                tqdm.write(f"[增量更新][{self.book_id}] 新章节PDF无效: {pdf_path} - {str(e)}，跳过")
        if len(merger.pages) < 2:
            tqdm.write(f"[增量更新][{self.book_id}] 合并后页数不足")
            return None, old_pdf_name, None
        # 合并PDF名
        if self.last_chapter >= self.first_chapter:
            range_text = f"({self.first_chapter}-{self.last_chapter}话)"
        else:
            range_text = f"({self.chapter_count}话)"
        merged_name = self.sanitize_filename(f"{self.comic_name}+{range_text}-漫小肆漫画({self.book_id}).pdf")
        merged_path = os.path.join(self.merged_dir, merged_name)
        os.makedirs(self.merged_dir, exist_ok=True)
        # 合并书签（先加旧PDF书签，再加新增章节书签，offset=旧PDF页数）
        try:
            # 过滤掉封面书签，只统计实际章节
            old_chapter_bookmarks = [b for b in old_bookmarks if '封面' not in b[0]]
            for title, page in old_bookmarks:
                if page < len(merger.pages):
                    merger.add_outline_item(title, page)
            for title, page in chapter_bookmarks:
                if page < len(merger.pages):
                    merger.add_outline_item(title, page)
            tqdm.write(f"[增量更新][{self.book_id}] 写入书签数: 旧PDF章节{len(old_chapter_bookmarks)}+新增章节{len(chapter_bookmarks)}")
        except Exception as e:
            tqdm.write(f"[增量更新][{self.book_id}] 添加书签失败: {str(e)}")
        merger.write(merged_path)
        merger.close()
        # 合并后效验
        if not self.verify_pdf_content_incremental(merged_path):
            tqdm.write(f"[增量更新][{self.book_id}] 合并后效验失败")
            return None, old_pdf_name, None
        return merged_path, old_pdf_name, False

    def clean_up_first_download(self, merged_pdf_path):
        # 首次下载清理逻辑
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                tqdm.write(f"[首次下载清理][{self.book_id}] 已删除临时图片目录")
            comic_folder = os.path.join(self.output_dir, str(self.book_id))
            if os.path.exists(comic_folder):
                shutil.rmtree(comic_folder, ignore_errors=True)
                tqdm.write(f"[首次下载清理][{self.book_id}] 已删除章节PDF文件夹")
            if merged_pdf_path and os.path.exists(merged_pdf_path):
                os.remove(merged_pdf_path)
                tqdm.write(f"[首次下载清理][{self.book_id}] 已删除合并PDF文件")
        except Exception as e:
            tqdm.write(f"[首次下载清理][{self.book_id}] 清理异常: {str(e)}")

    def clean_up_incremental(self, merged_pdf_path):
        # 增量更新清理逻辑
        try:
            comic_folder = os.path.join(self.output_dir, str(self.book_id))
            if os.path.exists(comic_folder):
                shutil.rmtree(comic_folder, ignore_errors=True)
                tqdm.write(f"[增量更新清理][{self.book_id}] 已删除新增章节PDF文件夹")
            if merged_pdf_path and os.path.exists(merged_pdf_path):
                os.remove(merged_pdf_path)
                tqdm.write(f"[增量更新清理][{self.book_id}] 已删除合并PDF文件")
            if os.path.exists(self.nas_cache_dir):
                shutil.rmtree(self.nas_cache_dir, ignore_errors=True)
                tqdm.write(f"[增量更新清理][{self.book_id}] 已清理NAS缓存目录")
        except Exception as e:
            tqdm.write(f"[增量更新清理][{self.book_id}] 清理异常: {str(e)}")

    def verify_pdf_content_first_download(self, pdf_path):
        # 首次下载合并后效验
        try:
            from PyPDF2 import PdfReader
            if not os.path.exists(pdf_path):
                tqdm.write(f"[首次下载效验][{self.book_id}] 合并PDF不存在")
                return False
            with open(pdf_path, 'rb') as f:
                reader = PdfReader(f)
                page_count = len(reader.pages)
                # 只校验页数，不再校验书签数
                size = os.path.getsize(pdf_path)
                tqdm.write(f"[首次下载效验][{self.book_id}] 合并PDF: 体积: {size/1024/1024:.2f}MB 页数: {page_count}")
                if page_count < 2:
                    return False
            return True
        except Exception as e:
            tqdm.write(f"[首次下载效验][{self.book_id}] 合并后效验异常: {str(e)}")
            return False

    def verify_pdf_content_incremental(self, pdf_path):
        # 增量更新合并后效验
        try:
            from PyPDF2 import PdfReader
            if not os.path.exists(pdf_path):
                tqdm.write(f"[增量更新效验][{self.book_id}] 合并PDF不存在")
                return False
            with open(pdf_path, 'rb') as f:
                reader = PdfReader(f)
                page_count = len(reader.pages)
                # 只校验页数，不再校验书签数
                size = os.path.getsize(pdf_path)
                tqdm.write(f"[增量更新效验][{self.book_id}] 合并PDF: 体积: {size/1024/1024:.2f}MB 页数: {page_count}")
                if page_count < 2:
                    return False
            return True
        except Exception as e:
            tqdm.write(f"[增量更新效验][{self.book_id}] 合并后效验异常: {str(e)}")
            return False

    def upload_to_nas_first_download(self, local_pdf_path):
        # 首次下载上传前效验与上传
        if not self.verify_pdf_content_first_download(local_pdf_path):
            tqdm.write(f"[首次下载上传][{self.book_id}] 上传前效验失败")
            return False
        # ...原有上传逻辑...
        return self.upload_to_nas(local_pdf_path)

    def upload_to_nas_incremental(self, local_pdf_path):
        # 增量更新上传前效验与上传
        if not self.verify_pdf_content_incremental(local_pdf_path):
            tqdm.write(f"[增量更新上传][{self.book_id}] 上传前效验失败")
            return False
        # ...原有上传逻辑...
        return self.upload_to_nas(local_pdf_path)

    def download_nas_file(self, filename, save_path):
        if not NAS_CONFIG.get('enabled', False):
            self.logger.error("NAS上传未启用，无法下载文件")
            return False

        conn = None
        try:
            conn = SMBConnection(
                NAS_CONFIG['username'],
                NAS_CONFIG['password'],
                NAS_CONFIG['client_name'],
                NAS_CONFIG['server_name'],
                use_ntlm_v2=True
            )
            server_ip = NAS_CONFIG['server_ip']
            share_name = NAS_CONFIG['share_name']
            remote_path = os.path.join(
                NAS_CONFIG['target_dir'],
                filename
            ).replace('\\', '/')

            if not conn.connect(server_ip, 445, timeout=600):
                self.logger.error("NAS连接失败")
                return False

            # Get file size for progress bar
            file_attributes = conn.getAttributes(share_name, remote_path)
            file_size = file_attributes.file_size

            tqdm.write(f"[{self.book_id}] 正在从NAS下载文件: {filename}")
            with open(save_path, 'wb') as f:
                with tqdm(
                    total=file_size,
                    desc=f"📥 [下载旧版] {os.path.basename(save_path)}",
                    unit='B',
                    unit_scale=True,
                    unit_divisor=1024,
                    position=2,  # 使用位置2，避免与上传(1)和主下载(0)冲突
                    leave=False,
                    colour='yellow'  # Different color
                ) as pbar:
                    wrapped_file = ProgressWrapper(f, pbar)
                    conn.retrieveFile(share_name, remote_path, wrapped_file)

            if os.path.exists(save_path):
                tqdm.write(f"[{self.book_id}] NAS文件下载成功: {filename}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"下载NAS文件失败: {str(e)}")
            return False
        finally:
            try:
                if conn:
                    conn.close()
            except Exception as e:
                self.logger.error(f"关闭连接异常: {str(e)}")

    def delete_nas_file(self, filename):
        """从NAS删除文件"""
        if not NAS_CONFIG.get('enabled', False):
            self.logger.info("NAS上传未启用，跳过删除操作")
            return True

        if not filename:
            self.logger.warning("没有指定要删除的文件名，跳过删除操作")
            return True

        conn = None
        try:
            conn = SMBConnection(
                NAS_CONFIG['username'],
                NAS_CONFIG['password'],
                NAS_CONFIG['client_name'],
                NAS_CONFIG['server_name'],
                use_ntlm_v2=True
            )
            server_ip = NAS_CONFIG['server_ip']
            share_name = NAS_CONFIG['share_name']
            remote_path = os.path.join(
                NAS_CONFIG['target_dir'],
                filename
            ).replace('\\', '/')

            if not conn.connect(server_ip, 445, timeout=600):
                self.logger.error(f"NAS连接失败，无法删除文件 {filename}")
                return False

            # 检查文件是否存在
            try:
                conn.getAttributes(share_name, remote_path)
            except:
                self.logger.info(f"文件 {filename} 在NAS上不存在或已被删除")
                return True

            # 删除文件
            conn.deleteFiles(share_name, remote_path)
            self.logger.info(f"✅ NAS文件删除成功: {filename}")
            tqdm.write(f"[{self.book_id}] ✅ NAS旧PDF文件已删除: {filename}")
            return True
        except Exception as e:
            self.logger.error(f"删除NAS文件失败: {filename} - {str(e)}")
            return False
        finally:
            try:
                if conn:
                    conn.close()
            except Exception as e:
                self.logger.error(f"关闭连接异常: {str(e)}")

    def upload_to_nas(self, local_pdf_path):
        if not NAS_CONFIG.get('enabled', False):
            return True

        # 使用锁确保同一时间只有一个上传操作
        with UPLOAD_LOCK:
            # 添加：验证本地PDF是否完整可读
            try:
                if not os.path.exists(local_pdf_path):
                    tqdm.write(f"[{self.book_id}] ❌ PDF文件不存在，跳过上传")
                    self.record_failed_id("PDF文件不存在")
                    return False

                # 使用之前缓存的验证结果，避免重复打开PDF文件
                pdf_validation = getattr(self, 'cached_pdf_validation', None)

                # 如果没有缓存验证结果或缓存路径不匹配，进行验证
                if pdf_validation is None or pdf_validation.get('path') != local_pdf_path:
                    # 初始化验证结果
                    pdf_validation = {
                        'is_valid': False,
                        'page_count': 0,
                        'chapter_count': 0,
                        'outline_count': 0
                    }

                    # 使用单个with语句处理所有PDF操作，避免文件句柄提前关闭
                    with open(local_pdf_path, 'rb') as test_file:
                        reader = PdfReader(test_file)

                        # 验证PDF文件是否可读且至少有2页(封面页+章节页)
                        pdf_validation['page_count'] = len(reader.pages)
                        if pdf_validation['page_count'] < 2:
                            tqdm.write(
                                f"[{self.book_id}] ❌ PDF页数不足，仅有{pdf_validation['page_count']}页，至少需要2页，跳过上传")
                            self.record_failed_id(f"PDF页数不足({pdf_validation['page_count']}页)")
                            return False

                        # 验证PDF章节书签数量
                        has_cover = False
                        try:
                            if reader.outline:
                                def count_outline_items(outline):
                                    count = 0
                                    titles = []
                                    for item in outline:
                                        if isinstance(item, list):
                                            sub_count, sub_titles = count_outline_items(item)
                                            count += sub_count
                                            titles.extend(sub_titles)
                                        else:
                                            if hasattr(item, '/Title'):
                                                count += 1
                                                titles.append(item['/Title'] if '/Title' in item else "未知书签")
                                    return count, titles
                                pdf_validation['outline_count'], bookmark_titles = count_outline_items(reader.outline)
                                for title in bookmark_titles:
                                    if '封面' in title.lower():
                                        has_cover = True
                                        break
                                pdf_validation['chapter_count'] = pdf_validation['outline_count']
                                if has_cover:
                                    pdf_validation['chapter_count'] -= 1  # 减去封面
                                tqdm.write(
                                    f"[{self.book_id}] PDF书签数: {pdf_validation['outline_count']}，章节数: {pdf_validation['chapter_count']}")
                                # 章节数与网站章节数的比较已删除
                                pdf_validation['is_valid'] = True
                            else:
                                tqdm.write(f"[{self.book_id}] ⚠️ PDF没有书签信息")
                        except Exception as e:
                            tqdm.write(f"[{self.book_id}] ⚠️ 读取PDF书签失败: {str(e)}，继续验证其他方面")
                else:
                    tqdm.write(f"[{self.book_id}] 使用缓存的PDF验证结果")
                    if 'chapter_count' in pdf_validation and 'outline_count' in pdf_validation:
                        tqdm.write(
                            f"[{self.book_id}] 缓存验证: PDF书签数: {pdf_validation['outline_count']}，章节数: {pdf_validation['chapter_count']}")
                    elif 'outline_error' in pdf_validation:
                        tqdm.write(
                            f"[{self.book_id}] ⚠️ 缓存中的PDF书签读取失败: {pdf_validation['outline_error']}，继续验证其他方面")

                # 获取当前合并PDF大小
                current_size = os.path.getsize(local_pdf_path)

                # 计算预期的文件大小范围（所有章节PDF总大小的95%-105%）
                expected_size = 0
                comic_folder = os.path.join(self.output_dir, str(self.book_id))

                # 判断是否为增量合并（即有旧PDF）
                old_pdf_size = 0
                old_pdf_name = None
                nas_pdf_path = None
                with self.compare_log_lock:
                    current_entry = self.compare_log.get(str(self.book_id), {})
                    old_pdf_name = current_entry.get("pdf_file", "")
                if old_pdf_name:
                    nas_pdf_path = os.path.join(self.nas_cache_dir, old_pdf_name)
                    if os.path.exists(nas_pdf_path):
                        old_pdf_size = os.path.getsize(nas_pdf_path)

                # 统计新增章节PDF体积
                new_pdfs_size = 0
                if os.path.exists(comic_folder):
                    chapter_pdfs = [os.path.join(comic_folder, f) for f in os.listdir(comic_folder) if f.endswith('.pdf')]
                    for pdf_file in chapter_pdfs:
                        if os.path.exists(pdf_file):
                            new_pdfs_size += os.path.getsize(pdf_file)

                # 增量合并时，expected_size=旧PDF体积+新增章节PDF体积；否则只用新增章节PDF体积
                if old_pdf_size > 0:
                    expected_size = old_pdf_size + new_pdfs_size
                    tqdm.write(f"[{self.book_id}] 增量校验: 旧PDF体积: {old_pdf_size/1024/1024:.2f}MB, 新增章节PDF体积: {new_pdfs_size/1024/1024:.2f}MB, 合计: {expected_size/1024/1024:.2f}MB")
                else:
                    expected_size = new_pdfs_size
                    tqdm.write(f"[{self.book_id}] 首次校验: 新增章节PDF体积: {expected_size/1024/1024:.2f}MB")

                # 验证PDF大小是否符合预期
                if expected_size > 0:
                    min_size = int(expected_size * 0.95)  # 允许5%的下浮动
                    max_size = int(expected_size * 1.05)  # 允许5%的上浮动

                    if current_size < min_size or current_size > max_size:
                        tqdm.write(
                            f"[{self.book_id}] ❌ PDF大小异常: 当前{current_size / 1024 / 1024:.2f}MB, 预期{expected_size / 1024 / 1024:.2f}MB(±5%), 跳过上传")
                        self.record_failed_id(f"PDF大小异常({current_size}字节, 预期{expected_size}±5%)")
                        return False
                    else:
                        tqdm.write(
                            f"[{self.book_id}] ✅ PDF大小验证通过: {current_size / 1024 / 1024:.2f}MB (预期{expected_size / 1024 / 1024:.2f}MB±5%)")
                else:
                    # 4. 兜底：使用最小大小限制
                    min_expected_size = 5000 * 1024  # 至少5000KB (5MB)
                    if current_size < min_expected_size:
                        tqdm.write(f"[{self.book_id}] ❌ PDF文件过小({current_size / 1024 / 1024:.2f}MB < 5MB)，跳过上传")
                        self.record_failed_id(f"PDF文件过小({current_size / 1024 / 1024:.2f}MB)")
                        return False
                    else:
                        tqdm.write(
                            f"[{self.book_id}] ✅ PDF大小仅检查最小限制: {current_size / 1024 / 1024:.2f}MB > 5MB，验证通过")
            except Exception as e:
                tqdm.write(f"[{self.book_id}] ❌ PDF文件验证失败: {str(e)}，跳过上传")
                self.record_failed_id(f"PDF验证异常: {str(e)}")
                return False

            for attempt in range(1, 4):
                conn = None
                try:
                    tqdm.write(f"[{self.book_id}] 开始第{attempt}次尝试上传到NAS")
                    self.logger.info(f"开始第{attempt}次尝试上传到NAS")

                    # 上传前体积对比
                    last_pdf_size = 0
                    last_pdf_hash = ""
                    with self.compare_log_lock:
                        last_entry = self.compare_log.get(str(self.book_id), {})
                        last_pdf_size = last_entry.get("pdf_size", 0)
                        last_pdf_hash = last_entry.get("pdf_hash", "")
                    current_pdf_size = 0
                    current_pdf_hash = ""
                    if os.path.exists(local_pdf_path):
                        current_pdf_size = os.path.getsize(local_pdf_path)
                        current_pdf_hash = self.compute_file_hash(local_pdf_path)
                    if current_pdf_hash and last_pdf_hash and current_pdf_hash == last_pdf_hash:
                        tqdm.write(f"[{self.book_id}] ⚠️ 本次PDF hash与上次相同，跳过上传，保留本地文件")
                        self.logger.warning(f"本次PDF hash与上次相同，跳过上传，保留本地文件")
                        # 不更新对比日志，因为没有上传成功
                        self.clean_up(local_pdf_path, False, False)
                        tqdm.write(f"[{self.book_id}] ⚠️ 处理完成（未上传，hash未变）")
                        self.logger.info(f"处理完成（未上传，hash未变）")
                        return False
                    # 到这里说明体积合格，可以上传
                    conn = SMBConnection(
                        NAS_CONFIG['username'],
                        NAS_CONFIG['password'],
                        NAS_CONFIG['client_name'],
                        NAS_CONFIG['server_name'],
                        use_ntlm_v2=True
                    )
                    server_ip = NAS_CONFIG['server_ip']
                    share_name = NAS_CONFIG['share_name']
                    remote_path = os.path.join(
                        NAS_CONFIG['target_dir'],
                        os.path.basename(local_pdf_path)
                    ).replace('\\', '/')

                    if not conn.connect(server_ip, 445, timeout=600):
                        self.logger.error(f"第{attempt}次NAS连接失败")
                        # 此处不需要关闭conn，因为如果connect失败，conn.sock会是None
                        time.sleep(2)
                        continue

                    tqdm.write(f"[{self.book_id}] 正在上传文件: {os.path.basename(local_pdf_path)}")
                    with open(local_pdf_path, 'rb') as file:
                        file_size = os.path.getsize(local_pdf_path)
                        with tqdm(
                            total=file_size,
                            desc=f"📤 [上传] {self.comic_name}",
                            unit='B',
                            unit_scale=True,
                            unit_divisor=1024,
                            position=1,
                            leave=False,
                            colour='green'
                        ) as upload_pbar:
                            wrapped_file = ProgressWrapper(file, upload_pbar)
                            conn.storeFile(share_name, remote_path, wrapped_file)

                    # 验证上传是否成功 - 检查文件是否已上传到NAS
                    file_info = conn.getAttributes(share_name, remote_path)
                    if file_info.file_size == os.path.getsize(local_pdf_path):
                        tqdm.write(f"[{self.book_id}] ✅ NAS上传成功，上传文件大小正确")
                        self.logger.info("NAS上传成功")

                        # 更新对比日志
                        with self.compare_log_lock:
                            self.compare_log[str(self.book_id)] = {
                                "comic_name": self.comic_name,
                                "website_chapters": self.chapter_count,
                                "local_chapters": self.chapter_count,
                                "last_updated": datetime.datetime.now().isoformat(),
                                "pdf_file": os.path.basename(local_pdf_path),
                                "pdf_size": current_pdf_size,
                                "pdf_hash": current_pdf_hash,
                                "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            }
                            self.save_compare_log()

                        # 清理临时文件
                        self.clean_up(local_pdf_path, True, True)
                        self.cleanup_done = True  # 新增：标记清理已完成

                        tqdm.write(f"[{self.book_id}] ✅ 处理完成")
                        self.logger.info(f"处理完成 (上传成功)")
                        return True
                    else:
                        tqdm.write(
                            f"[{self.book_id}] ❌ NAS上传大小不匹配: NAS={file_info.file_size}, 本地={os.path.getsize(local_pdf_path)}")
                        self.record_failed_id("NAS上传文件大小不匹配")
                        # 即使验证失败，也返回False，让finally块关闭连接
                except Exception as e:
                    self.logger.error(f"第{attempt}次上传失败: {str(e)}")
                    if attempt == 3:  # 最后一次尝试失败，放弃
                        tqdm.write(f"[{self.book_id}] ❌ NAS上传失败，保留本地文件和NAS旧PDF")
                        self.record_failed_id(f"NAS上传失败: {str(e)}")
                        self.clean_up(local_pdf_path, False, False)
                finally:
                    if conn:
                        try:
                            conn.close()
                        except Exception as e_close:
                            self.logger.error(f"关闭NAS连接时出错: {e_close}")

                # 如果代码执行到这里，说明上传或验证失败了，准备重试
                if attempt < 3:
                    tqdm.write(f"[{self.book_id}] 将在2秒后重试上传...")
                    time.sleep(2)

            # 所有尝试均失败
            return False

    def clean_up(self, merged_pdf_path, is_incremental=False, upload_success=False):
        # 只有在上传成功后才删除临时文件
        if upload_success:
            # 删除临时图片目录
            try:
                if os.path.exists(self.temp_dir):
                    shutil.rmtree(self.temp_dir, ignore_errors=True)
                    tqdm.write(f"[{self.book_id}] 已删除临时图片目录")
                else:
                    tqdm.write(f"[{self.book_id}] 临时图片目录不存在，无需删除")
            except Exception as e:
                tqdm.write(f"[{self.book_id}] ❌ 删除临时图片目录失败：{str(e)}")

            # 清理NAS缓存目录
            try:
                if os.path.exists(self.nas_cache_dir):
                    deleted_count = 0
                    for f in os.listdir(self.nas_cache_dir):
                        file_path = os.path.join(self.nas_cache_dir, f)
                        if os.path.isfile(file_path):
                            try:
                                os.remove(file_path)
                                deleted_count += 1
                            except Exception as e2:
                                self.logger.error(f"删除NAS缓存文件失败: {f} - {str(e2)}")
                    tqdm.write(f"[{self.book_id}] 已清理NAS中间件目录，删除了{deleted_count}个文件")
                else:
                    tqdm.write(f"[{self.book_id}] NAS中间件目录不存在，无需清理")
            except Exception as e:
                tqdm.write(f"[{self.book_id}] ❌ 清理NAS中间件目录失败：{str(e)}")

            # 删除章节PDF文件
            try:
                comic_folder = os.path.join(self.output_dir, str(self.book_id))
                if os.path.exists(comic_folder):
                    pdf_count = len([f for f in os.listdir(comic_folder) if f.endswith('.pdf')])
                    shutil.rmtree(comic_folder, ignore_errors=True)
                    tqdm.write(f"[{self.book_id}] 已删除章节PDF文件夹，包含{pdf_count}个PDF文件")
                else:
                    tqdm.write(f"[{self.book_id}] 章节PDF文件夹不存在，无需删除")
            except Exception as e:
                tqdm.write(f"[{self.book_id}] ❌ 删除章节PDF文件夹失败：{str(e)}")

            # 删除合并的PDF文件
            try:
                if merged_pdf_path and os.path.exists(merged_pdf_path):
                    file_size = os.path.getsize(merged_pdf_path)
                    os.remove(merged_pdf_path)
                    tqdm.write(f"[{self.book_id}] 已删除合并PDF文件，大小：{file_size / 1024 / 1024:.2f}MB")
                else:
                    tqdm.write(f"[{self.book_id}] 合并PDF文件不存在或路径无效，无需删除")
            except Exception as e:
                tqdm.write(f"[{self.book_id}] ❌ 删除合并PDF文件失败：{str(e)}")

            # 清理整个章节图片下载临时目录（仅当无其它任务在用时）
            try:
                base_temp_dir = os.path.join(self.download_dir, "漫小肆章节图片下载")
                if os.path.exists(base_temp_dir):
                    # 检查是否还有其他任务正在使用临时目录
                    temp_dirs = [d for d in os.listdir(base_temp_dir) if os.path.isdir(os.path.join(base_temp_dir, d))]
                    if len(temp_dirs) <= 1:  # 只有当前任务或无任务
                        shutil.rmtree(base_temp_dir, ignore_errors=True)
                        tqdm.write(f"[{self.book_id}] 已彻底清理章节图片下载临时目录")
                    else:
                        tqdm.write(f"[{self.book_id}] 还有其他任务在使用临时目录，跳过彻底清理")
                else:
                    tqdm.write(f"[{self.book_id}] 章节图片下载临时目录不存在，无需清理")
            except Exception as e:
                tqdm.write(f"[{self.book_id}] ❌ 清理章节图片下载临时目录失败：{str(e)}")
        else:
            # 上传失败，保留所有临时文件
            if merged_pdf_path and os.path.exists(merged_pdf_path):
                try:
                    file_size = os.path.getsize(merged_pdf_path)
                    tqdm.write(
                        f"[{self.book_id}] ⚠️ 上传失败，保留所有临时文件和合并PDF: {merged_pdf_path}，大小：{file_size / 1024 / 1024:.2f}MB")
                except:
                    tqdm.write(f"[{self.book_id}] ⚠️ 上传失败，保留所有临时文件和合并PDF: {merged_pdf_path}")
            else:
                tqdm.write(f"[{self.book_id}] ⚠️ 上传失败，保留所有临时文件，但合并PDF文件不存在或路径无效")

    def create_cover_pdf(self, cover_path):
        """将封面图片转换为PDF文件"""
        if not cover_path or not os.path.exists(cover_path):
            self.logger.warning("封面图片不存在，跳过封面PDF创建")
            tqdm.write(f"[{self.book_id}] 封面图片不存在，跳过封面PDF创建")
            return None

        try:
            # 创建封面PDF文件名和路径
            comic_folder = os.path.join(self.output_dir, str(self.book_id))
            os.makedirs(comic_folder, exist_ok=True)
            cover_pdf_path = os.path.join(comic_folder, "000_封面.pdf")

            # 如果封面PDF已存在则直接返回
            if os.path.exists(cover_pdf_path):
                return cover_pdf_path

            # 再次检查文件是否存在
            if not os.path.exists(cover_path):
                self.logger.warning(f"处理时封面图片已被删除: {cover_path}")
                tqdm.write(f"[{self.book_id}] 处理时封面图片已被删除")
                return None

            # 打开封面图片并转换为PDF
            try:
                img = Image.open(cover_path).convert("RGB")
                img.save(cover_pdf_path, format="PDF", resolution=100.0)
                if os.path.exists(cover_pdf_path):
                    return cover_pdf_path
                else:
                    self.logger.error("封面PDF创建成功但文件不存在")
                    tqdm.write(f"[{self.book_id}] 封面PDF创建成功但文件不存在")
                    return None
            except Exception as e:
                self.logger.error(f"封面图片处理失败: {str(e)}")
                tqdm.write(f"[{self.book_id}] 封面图片处理失败: {str(e)}")
                return None
        except Exception as e:
            self.logger.error(f"创建封面PDF失败: {str(e)}")
            tqdm.write(f"[{self.book_id}] 创建封面PDF失败: {str(e)}")
            return None

    def download_cover(self):
        """下载封面图片并返回本地路径"""
        # 检查是否已经有封面图
        cover_name = self.sanitize_filename(f"{self.comic_name}-{self.book_id}.jpg")
        cover_path = os.path.join(self.cover_dir, cover_name)

        if os.path.exists(cover_path):
            return cover_path

        soup = self.get_soup(self.base_url)
        if not soup:
            return None

        cover_img = soup.select_one('div.banner_border_bg img.banner_detail_bg')
        if not cover_img or not cover_img.get('src'):
            return None

        img_url = urljoin(self.base_url, cover_img['src'])
        try:
            headers = self.get_next_ua()
            response = self.session.get(img_url, headers=headers, timeout=30)
            if response.status_code == 200:
                with open(cover_path, 'wb') as f:
                    f.write(response.content)
                return cover_path
        except Exception as e:
            self.logger.error(f"封面图下载失败：{e}")

        return None

    def check_pdf_pages(self, pdf_path, min_pages=1):
        try:
            reader = PdfReader(pdf_path)
            return len(reader.pages) >= min_pages
        except Exception as e:
            tqdm.write(f"检查PDF页数失败: {pdf_path} - {e}")
            return False

    def ensure_cover_pdf(self):
        """无限重试直到封面图片和PDF都存在且PDF为1页"""
        while True:
            cover_path = self.download_cover()
            cover_dir = self.cover_dir
            if cover_path and os.path.exists(cover_path):
                # 检查封面图片文件夹下有图片
                if any(f.endswith('.jpg') for f in os.listdir(cover_dir)):
                    cover_pdf = self.create_cover_pdf(cover_path)
                    if cover_pdf and os.path.exists(cover_pdf):
                        try:
                            with open(cover_pdf, 'rb') as pdf_file:
                                reader = PdfReader(pdf_file)
                                if len(reader.pages) == 1:
                                    return cover_pdf
                        except Exception as e:
                            tqdm.write(f"[{self.book_id}] 读取封面PDF失败: {str(e)}")
            tqdm.write(f"[{self.book_id}] 封面图片或PDF缺失或页数异常，重试下载...")

    def ensure_chapter_pdf(self, chapter_title, chapter_url, sort_key):
        # 清理章节标题，确保没有非法字符
        chapter_title = self.sanitize_filename(chapter_title)
        chapter_key = f"{self.book_id}_{chapter_title}"
        lock = self.get_chapter_lock(chapter_key)
        while True:
            with lock:
                try:
                    img_urls = self.find_images_in_chapter(chapter_url)
                    if not self.check_image_count(img_urls, min_count=1):
                        tqdm.write(
                            f"[{self.book_id}] {chapter_title} 图片数量不足（{len(img_urls) if img_urls else 0}），重试...")
                        continue
                    downloaded_files, stats = self.download_images(img_urls, chapter_title)
                    chapter_dir = os.path.join(self.temp_dir, chapter_title)
                    if not self.check_image_count(chapter_dir, min_count=1):
                        tqdm.write(
                            f"[{self.book_id}] {chapter_title} 图片数量不足（{len([f for f in os.listdir(chapter_dir) if f.endswith('.jpg')]) if os.path.exists(chapter_dir) else 0}），重试...")
                        continue

                    # 过滤掉不存在的图片文件
                    valid_files = [f for f in downloaded_files if os.path.exists(f)]
                    if not valid_files:
                        tqdm.write(f"[{self.book_id}] {chapter_title} 没有有效图片文件，重试...")
                        continue

                    pdf_path = self.convert_images_to_pdf(valid_files, chapter_title, sort_key)
                    if pdf_path and os.path.exists(pdf_path) and self.check_pdf_pages(pdf_path, min_pages=1):
                        return pdf_path, sort_key, stats
                    tqdm.write(f"[{self.book_id}] {chapter_title} PDF生成失败或页数异常，重试...")
                except Exception as e:
                    tqdm.write(f"[{self.book_id}] {chapter_title} 生成PDF时出错，重试... {e}")
                    continue

    def download_comic(self):
        # 先获取章节信息（包含漫画名称）
        max_retries = 3
        chapters = None

        # 分段验证 - 阶段1：获取章节列表
        if ENABLE_PHASE_VALIDATION:
            tqdm.write(f"[{self.book_id}] 📋 阶段1: 获取章节列表")

        for retry_count in range(max_retries):
            chapters = self.get_all_chapters()
            if chapters:
                break
            else:
                tqdm.write(f"[{self.book_id}] ❌ 获取章节失败，第{retry_count + 1}次重试中...")
                time.sleep(random.uniform(3, 6))  # 随机等待3-6秒后重试

        if not chapters:
            tqdm.write(f"[{self.book_id}] ❌ 获取章节失败，请检查连接！已重试{max_retries}次")
            self.record_failed_id("获取章节列表失败")
            return None

        tqdm.write(f"[{self.book_id}] {self.comic_name} 开始处理...")
        need_update, reason, to_download_indexes = self.check_update_needed()
        if not need_update:
            tqdm.write(f"[{self.book_id}] {self.comic_name} 无更新，跳过下载")
            return None

        tqdm.write(f"[{self.book_id}] {reason} | 需下载章节: {len(to_download_indexes)}个")

        # 首次下载流程
        if self.is_first_download:
            tqdm.write(f"[{self.book_id}] 首次下载，开始处理...")

            # 分段验证 - 阶段2：下载封面
            if ENABLE_PHASE_VALIDATION:
                tqdm.write(f"[{self.book_id}] 📋 阶段2: 下载封面")

            # 下载封面
            cover_pdf = None
            tqdm.write(f"[{self.book_id}] 处理封面图片...")
            try:
                cover_pdf = self.ensure_cover_pdf()
                if cover_pdf:
                    tqdm.write(f"[{self.book_id}] ✅ 封面PDF创建成功")
                else:
                    tqdm.write(f"[{self.book_id}] ❌ 封面PDF创建失败")
                    self.record_failed_id("封面PDF创建失败")
                    # 不放弃下载，仍然继续
            except Exception as e:
                tqdm.write(f"[{self.book_id}] ❌ 封面PDF处理异常: {str(e)}")
                self.record_failed_id(f"封面PDF处理异常: {str(e)}")
                # 不放弃下载，仍然继续

            # 分段验证 - 阶段3：下载章节
            if ENABLE_PHASE_VALIDATION:
                tqdm.write(f"[{self.book_id}] 📋 阶段3: 下载章节")

            # 下载所有章节
            tqdm.write(f"[{self.book_id}] 开始下载章节...(线程数: {self.chapter_workers})")
            chapter_tasks = []
            for idx in range(len(chapters)):  # 首次下载需要下载所有章节
                _, chapter_url, sort_key = chapters[idx]
                chapter_title = self.chapter_titles[idx]
                chapter_tasks.append((idx, chapter_title, chapter_url, sort_key))

            # 下载章节PDF，确保全部下载成功
            pdf_results = {}
            pdf_download_success = True

            # 用于统计图片下载情况
            total_images = 0
            total_skipped_images = 0
            chapters_with_high_skip_rate = 0
            all_chapter_stats = []

            for idx, chapter_title, chapter_url, sort_key in chapter_tasks:
                try:
                    pdf_path, sort_key, stats = self.process_chapter((idx, chapter_title, chapter_url, sort_key))
                    # 累计统计图片数据
                    total_images += stats["total"]
                    total_skipped_images += stats["skipped"]
                    all_chapter_stats.append(stats)

                    # 检查单章节跳过率
                    if stats["skip_rate"] >= SINGLE_CHAPTER_SKIP_RATE:  # 使用配置参数判断高跳过率章节
                        chapters_with_high_skip_rate += 1
                        tqdm.write(f"[{self.book_id}] ⚠️ 章节 {chapter_title} 跳过率过高: {stats['skip_rate']:.2%}")

                    if pdf_path and os.path.exists(pdf_path):
                        pdf_results[sort_key] = pdf_path
                    else:
                        tqdm.write(f"[{self.book_id}] ❌ 章节 {chapter_title} PDF生成失败")
                        pdf_download_success = False
                except Exception as e:
                    tqdm.write(f"[{self.book_id}] ❌ 章节 {chapter_title} 处理异常: {str(e)}")
                    pdf_download_success = False

            # 分段验证 - 阶段3验证
            if ENABLE_PHASE_VALIDATION:
                tqdm.write(f"[{self.book_id}] 📋 阶段3验证: 检查下载章节完整性")
                tqdm.write(
                    f"[{self.book_id}] 章节下载成功率: {len(pdf_results)}/{len(chapter_tasks)} ({len(pdf_results) / len(chapter_tasks):.1%})")

            # 检查整体跳过率
            overall_skip_rate = total_skipped_images / total_images if total_images > 0 else 1.0
            tqdm.write(
                f"[{self.book_id}] 整体图片统计: 总图片{total_images}张, 跳过{total_skipped_images}张, 整体跳过率: {overall_skip_rate:.2%}")

            # 检查是否满足跳过条件
            should_skip = False
            skip_reason = ""

            # 条件1: 章节跳过图片数量占到总章节图片数量的阈值
            if chapters_with_high_skip_rate / len(chapter_tasks) >= HIGH_SKIP_CHAPTERS_RATIO and len(chapter_tasks) > 0:
                should_skip = True
                skip_reason = f"90%以上的章节跳过率过高 ({chapters_with_high_skip_rate}/{len(chapter_tasks)}章)"

            # 条件2: 跳过的所有章节图片总和占到所有章节图片总数的阈值
            if overall_skip_rate >= OVERALL_SKIP_RATE:
                should_skip = True
                skip_reason = f"整体图片跳过率达到{overall_skip_rate:.2%}，超过{OVERALL_SKIP_RATE:.0%}"

            if should_skip:
                tqdm.write(f"[{self.book_id}] ❌ 跳过更新，原因: {skip_reason}")
                # 记录到失败ID明细
                self.record_failed_id(f"跳过原因: {skip_reason}")
                # 从下载对比中删除
                self.remove_from_compare_log()
                return None

            # 检查是否所有章节都下载成功
            if not pdf_download_success or len(pdf_results) != len(chapter_tasks):
                tqdm.write(f"[{self.book_id}] ⚠️ 部分章节下载失败，已下载 {len(pdf_results)}/{len(chapter_tasks)} 章")
                # 尝试重新下载失败的章节
                tqdm.write(f"[{self.book_id}] 尝试重新下载失败的章节...")
                for idx, chapter_title, chapter_url, sort_key in chapter_tasks:
                    if sort_key not in pdf_results:
                        try:
                            pdf_path, sort_key, stats = self.process_chapter(
                                (idx, chapter_title, chapter_url, sort_key))
                            if pdf_path and os.path.exists(pdf_path):
                                pdf_results[sort_key] = pdf_path
                                tqdm.write(f"[{self.book_id}] ✅ 章节 {chapter_title} 重新下载成功，统计信息: {stats}")
                        except Exception as e:
                            tqdm.write(f"[{self.book_id}] ❌ 章节 {chapter_title} 重新下载失败: {str(e)}")

            # 再次检查下载情况
            if len(pdf_results) != len(chapter_tasks):
                tqdm.write(f"[{self.book_id}] ⚠️ 仍有章节下载失败，已下载 {len(pdf_results)}/{len(chapter_tasks)} 章")
                # 如果下载的章节太少，可能需要放弃此次下载
                if len(pdf_results) < len(chapter_tasks) * CHAPTER_SUCCESS_RATE:  # 使用配置参数判断下载成功率
                    tqdm.write(f"[{self.book_id}] ❌ 下载成功率过低，放弃本次下载")
                    # 记录到失败ID明细
                    self.record_failed_id(f"下载成功率过低({len(pdf_results)}/{len(chapter_tasks)})")
                    return None
                else:
                    tqdm.write(f"[{self.book_id}] ⚠️ 尽管有失败章节，但成功率较高，继续处理")

            # 按顺序组织PDF文件列表
            pdf_files = [pdf_results[key] for key in sorted(pdf_results) if key in pdf_results]
            if cover_pdf:
                pdf_files.insert(0, cover_pdf)

            # 计算预期的PDF数量
            expected_count = len(pdf_files)
            tqdm.write(f"[{self.book_id}] 准备合并 {expected_count} 个PDF文件")

            # 分段验证 - 阶段4：合并PDF
            if ENABLE_PHASE_VALIDATION:
                tqdm.write(f"[{self.book_id}] 📋 阶段4: 合并PDF")

            # 合并PDF
            try:
                if self.is_first_download:
                    merged_pdf_path, old_pdf_name, _ = self.merge_pdfs_first_download(pdf_files)
                else:
                    merged_pdf_path, old_pdf_name, _ = self.merge_pdfs_incremental(pdf_files)
                if not merged_pdf_path:
                    tqdm.write(f"[{self.book_id}] ❌ PDF合并失败")
                    # 记录到失败ID明细
                    self.record_failed_id("PDF合并失败")
                    return None
            except Exception as e:
                tqdm.write(f"[{self.book_id}] ❌ PDF合并异常: {str(e)}")
                self.record_failed_id(f"PDF合并异常: {str(e)}")
                return None

            # 分段验证 - 阶段5：上传到NAS
            if ENABLE_PHASE_VALIDATION:
                tqdm.write(f"[{self.book_id}] 📋 阶段5: 上传到NAS")

            # 上传到NAS
            tqdm.write(f"[{self.book_id}] 正在上传到NAS...")
            upload_success = self.upload_to_nas(merged_pdf_path)
            if upload_success:
                tqdm.write(f"[{self.book_id}] ✅ {self.comic_name} 上传完成")
                # 上传成功后，删除NAS上的旧PDF（首次下载无旧PDF）
                if old_pdf_name:
                    self.delete_nas_file(old_pdf_name)
                # update_compare_log已经在upload_to_nas方法中调用
                if not self.cleanup_done:
                    self.clean_up(merged_pdf_path, False, upload_success)
                # 上传成功后，从失败ID列表中移除
                self.remove_from_failed_ids()
            else:
                tqdm.write(f"[{self.book_id}] ❌ NAS上传失败，保留本地文件和NAS旧PDF")
                self.clean_up(merged_pdf_path, False, False)
                # 上传失败也记录到失败列表
                self.record_failed_id("NAS上传失败")
                return None

            tqdm.write(f"[{self.book_id}] ✅ {self.comic_name} 处理完成")
            return self.book_id

        # 增量更新流程
        else:
            tqdm.write(f"[{self.book_id}] 增量更新，开始处理...")

            # 分段验证 - 阶段1：准备章节任务
            if ENABLE_PHASE_VALIDATION:
                tqdm.write(f"[{self.book_id}] 📋 阶段1: 准备增量更新章节任务")

            # 准备章节任务
            chapter_tasks = []
            for idx in to_download_indexes:
                if idx < len(chapters):
                    _, chapter_url, sort_key = chapters[idx]
                    chapter_title = self.chapter_titles[idx]
                    chapter_tasks.append((idx, chapter_title, chapter_url, sort_key))
                else:
                    tqdm.write(f"[{self.book_id}] ❌ 索引{idx}超出章节范围")
                    self.record_failed_id(f"索引{idx}超出章节范围")

            # 检查章节任务是否为空
            if not chapter_tasks:
                tqdm.write(f"[{self.book_id}] ❌ 无有效章节任务，放弃更新")
                self.record_failed_id("无有效章节任务")
                return None

            # --- 优化：并行预取旧版PDF ---
            # 获取旧版PDF信息
            with self.compare_log_lock:
                current_entry = self.compare_log.get(str(self.book_id), {})
                old_pdf_name = current_entry.get("pdf_file", "")

            # 在后台线程中预取旧版PDF
            old_pdf_future = None
            if old_pdf_name:
                nas_pdf_path = os.path.join(self.nas_cache_dir, old_pdf_name)
                # 使用单独的线程池执行下载，避免阻塞主流程
                prefetch_executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix=f'prefetch-{self.book_id}')
                tqdm.write(f"[{self.book_id}] 后台预取旧版PDF: {old_pdf_name}")
                old_pdf_future = prefetch_executor.submit(self.download_nas_file, old_pdf_name, nas_pdf_path)
                prefetch_executor.shutdown(wait=False)  # 让线程池在后台运行

            # 无限重试循环：直到合并后PDF体积大于旧PDF体积
            max_retry_attempts = 2  # 设置最大重试次数，防止无限循环
            retry_count = 0

            while True:
                retry_count += 1
                if retry_count > max_retry_attempts:
                    tqdm.write(f"[{self.book_id}] ⚠️ 达到最大重试次数({max_retry_attempts})，放弃增量更新")
                    # 记录到失败ID明细
                    self.record_failed_id("合并PDF体积异常，达到最大重试次数")
                    return None

                # 分段验证 - 阶段2：下载新增章节
                if ENABLE_PHASE_VALIDATION:
                    tqdm.write(f"[{self.book_id}] 📋 阶段2: 下载新增章节 (第{retry_count}次尝试)")

                # 下载新增章节
                pdf_results = {}
                pdf_download_success = True

                # 用于统计图片下载情况
                total_images = 0
                total_skipped_images = 0
                chapters_with_high_skip_rate = 0
                all_chapter_stats = []

                for idx, chapter_title, chapter_url, sort_key in chapter_tasks:
                    try:
                        # 每次重试都强制重新下载
                        if retry_count > 1:
                            # 清理章节目录，强制重新下载
                            chapter_dir = os.path.join(self.temp_dir, self.sanitize_filename(chapter_title))
                            if os.path.exists(chapter_dir):
                                try:
                                    shutil.rmtree(chapter_dir)
                                    tqdm.write(f"[{self.book_id}] 清理章节目录: {chapter_title}")
                                except Exception as e:
                                    tqdm.write(f"[{self.book_id}] 清理章节目录失败: {chapter_title} - {str(e)}")

                        pdf_path, sort_key, stats = self.process_chapter((idx, chapter_title, chapter_url, sort_key))
                        # 累计统计图片数据
                        total_images += stats["total"]
                        total_skipped_images += stats["skipped"]
                        all_chapter_stats.append(stats)

                        # 检查单章节跳过率
                        if stats["skip_rate"] >= SINGLE_CHAPTER_SKIP_RATE:  # 使用配置参数判断高跳过率章节
                            chapters_with_high_skip_rate += 1
                            tqdm.write(f"[{self.book_id}] ⚠️ 章节 {chapter_title} 跳过率过高: {stats['skip_rate']:.2%}")

                        if pdf_path and os.path.exists(pdf_path):
                            pdf_results[sort_key] = pdf_path
                        else:
                            tqdm.write(f"[{self.book_id}] ❌ 章节 {chapter_title} PDF生成失败")
                            pdf_download_success = False
                    except Exception as e:
                        tqdm.write(f"[{self.book_id}] ❌ 章节 {chapter_title} 处理异常: {str(e)}")
                        pdf_download_success = False

                # 分段验证 - 阶段2验证
                if ENABLE_PHASE_VALIDATION:
                    tqdm.write(f"[{self.book_id}] 📋 阶段2验证: 检查新增章节下载完整性")
                    tqdm.write(
                        f"[{self.book_id}] 新增章节下载成功率: {len(pdf_results)}/{len(chapter_tasks)} ({len(pdf_results) / len(chapter_tasks):.1%})")

                # 检查整体跳过率
                overall_skip_rate = total_skipped_images / total_images if total_images > 0 else 1.0
                tqdm.write(
                    f"[{self.book_id}] 整体图片统计: 总图片{total_images}张, 跳过{total_skipped_images}张, 整体跳过率: {overall_skip_rate:.2%}")

                # 检查是否满足跳过条件
                should_skip = False
                skip_reason = ""

                # 条件1: 章节跳过图片数量占到总章节图片数量的阈值
                if chapters_with_high_skip_rate / len(chapter_tasks) >= HIGH_SKIP_CHAPTERS_RATIO and len(
                        chapter_tasks) > 0:
                    should_skip = True
                    skip_reason = f"90%以上的章节跳过率过高 ({chapters_with_high_skip_rate}/{len(chapter_tasks)}章)"

                # 条件2: 跳过的所有章节图片总和占到所有章节图片总数的阈值
                if overall_skip_rate >= OVERALL_SKIP_RATE:
                    should_skip = True
                    skip_reason = f"整体图片跳过率达到{overall_skip_rate:.2%}，超过{OVERALL_SKIP_RATE:.0%}"

                if should_skip:
                    tqdm.write(f"[{self.book_id}] ❌ 跳过更新，原因: {skip_reason}")
                    # 记录到失败ID明细
                    self.record_failed_id(f"跳过原因: {skip_reason}")
                    # 从下载对比中删除
                    self.remove_from_compare_log()
                    return None

                # 检查是否所有新增章节都下载成功
                if not pdf_download_success or len(pdf_results) != len(chapter_tasks):
                    tqdm.write(
                        f"[{self.book_id}] ⚠️ 部分新增章节下载失败，已下载 {len(pdf_results)}/{len(chapter_tasks)} 章")
                    # 尝试重新下载失败的章节
                    tqdm.write(f"[{self.book_id}] 尝试重新下载失败的章节...")
                    for idx, chapter_title, chapter_url, sort_key in chapter_tasks:
                        if sort_key not in pdf_results:
                            try:
                                pdf_path, sort_key, stats = self.process_chapter(
                                    (idx, chapter_title, chapter_url, sort_key))
                                if pdf_path and os.path.exists(pdf_path):
                                    pdf_results[sort_key] = pdf_path
                                    tqdm.write(f"[{self.book_id}] ✅ 章节 {chapter_title} 重新下载成功")
                            except Exception as e:
                                tqdm.write(f"[{self.book_id}] ❌ 章节 {chapter_title} 重新下载失败: {str(e)}")

                # 再次检查下载情况
                if len(pdf_results) != len(chapter_tasks):
                    tqdm.write(
                        f"[{self.book_id}] ⚠️ 仍有新增章节下载失败，已下载 {len(pdf_results)}/{len(chapter_tasks)} 章")
                    # 如果下载的章节太少，可能需要放弃此次更新
                    if len(pdf_results) < len(chapter_tasks) * CHAPTER_SUCCESS_RATE:  # 使用配置参数判断下载成功率
                        tqdm.write(f"[{self.book_id}] ❌ 下载成功率过低，放弃本次更新")
                        # 记录到失败ID明细
                        self.record_failed_id("下载成功率过低")
                        return None
                    else:
                        tqdm.write(f"[{self.book_id}] ⚠️ 尽管有失败章节，但成功率较高，继续处理")

                # --- 优化：等待后台预取任务完成 ---
                if old_pdf_future:
                    tqdm.write(f"[{self.book_id}] 等待后台旧版PDF预取任务完成...")
                    try:
                        # 等待后台下载完成，并获取结果
                        download_success = old_pdf_future.result(timeout=1200) # 增加等待超时
                        if not download_success:
                            tqdm.write(f"[{self.book_id}] ❌ 后台预取旧版PDF失败，增量更新中止。")
                            self.record_failed_id("后台预取旧版PDF失败")
                            return None
                        else:
                            tqdm.write(f"[{self.book_id}] ✅ 旧版PDF预取成功")
                    except Exception as e:
                        tqdm.write(f"[{self.book_id}] ❌ 等待旧版PDF预取时发生错误: {e}")
                        self.record_failed_id(f"等待旧版PDF预取时发生错误: {e}")
                        return None

                # 按顺序组织PDF文件列表
                pdf_files = [pdf_results[key] for key in sorted(pdf_results) if key in pdf_results]

                # 增量合并时不需要封面
                expected_count = len(pdf_files)
                tqdm.write(f"[{self.book_id}] 准备增量合并 {expected_count} 个新增章节PDF文件")

                # 分段验证 - 阶段3：合并PDF
                if ENABLE_PHASE_VALIDATION:
                    tqdm.write(f"[{self.book_id}] 📋 阶段3: 合并PDF (第{retry_count}次尝试)")

                # 合并PDF，is_incremental=True
                try:
                    merged_pdf_path, old_pdf_name, needs_retry = self.merge_pdfs_incremental(pdf_files)

                    # 检查是否需要重新下载（PDF体积异常）
                    if needs_retry:
                        tqdm.write(f"[{self.book_id}] ⚠️ 合并PDF体积异常，需要重新下载 (第{retry_count}次)")

                        # 删除当前合并的PDF
                        if merged_pdf_path and os.path.exists(merged_pdf_path):
                            try:
                                os.remove(merged_pdf_path)
                                tqdm.write(f"[{self.book_id}] 已删除体积异常的合并PDF")
                            except Exception as e:
                                tqdm.write(f"[{self.book_id}] 删除体积异常的合并PDF失败: {str(e)}")

                        # 清理章节PDF文件，强制重新下载
                        comic_folder = os.path.join(self.output_dir, str(self.book_id))
                        if os.path.exists(comic_folder):
                            try:
                                shutil.rmtree(comic_folder)
                                tqdm.write(f"[{self.book_id}] 已清理章节PDF文件夹，强制重新下载")
                            except Exception as e:
                                tqdm.write(f"[{self.book_id}] 清理章节PDF文件夹失败: {str(e)}")

                        # 继续循环，重新下载
                        continue

                    if not merged_pdf_path:
                        # 合并失败
                        if retry_count < max_retry_attempts:
                            tqdm.write(f"[{self.book_id}] ❌ PDF增量合并失败，尝试重新下载 (第{retry_count}次)")
                            continue
                        else:
                            tqdm.write(f"[{self.book_id}] ❌ PDF增量合并失败，尝试全量合并...")
                            # 如果增量合并失败，尝试全量合并
                            pdf_files = self.redownload_all_chapters_and_cover()
                            expected_count = len(pdf_files)
                            tqdm.write(f"[{self.book_id}] 准备全量合并 {expected_count} 个PDF文件")
                            try:
                                merged_pdf_path, old_pdf_name, _ = self.merge_pdfs(pdf_files, is_incremental=False,
                                                                                   all_pdf_files=pdf_files)
                                if not merged_pdf_path:
                                    tqdm.write(f"[{self.book_id}] ❌ PDF全量合并也失败，放弃处理")
                                    # 记录到失败ID明细
                                    self.record_failed_id("全量PDF合并失败")
                                    return None
                            except Exception as e:
                                tqdm.write(f"[{self.book_id}] ❌ 全量合并异常: {str(e)}")
                                self.record_failed_id(f"全量合并异常: {str(e)}")
                                return None
                    # 合并成功，跳出循环
                    break
                except Exception as e:
                    tqdm.write(f"[{self.book_id}] ❌ PDF合并异常: {str(e)}")
                    if retry_count < max_retry_attempts:
                        tqdm.write(f"[{self.book_id}] 尝试重新下载 (第{retry_count}次)")
                        continue
                    else:
                        tqdm.write(f"[{self.book_id}] ❌ 达到最大重试次数，放弃处理")
                        # 记录到失败ID明细
                        self.record_failed_id("PDF合并异常，达到最大重试次数")
                        return None

            # 分段验证 - 阶段4：上传到NAS
            if ENABLE_PHASE_VALIDATION:
                tqdm.write(f"[{self.book_id}] 📋 阶段4: 上传到NAS")

            # 上传到NAS
            tqdm.write(f"[{self.book_id}] 正在上传到NAS...")
            upload_success = self.upload_to_nas(merged_pdf_path)
            if upload_success:
                tqdm.write(f"[{self.book_id}] ✅ {self.comic_name} 上传完成")
                # 上传成功后，删除NAS上的旧PDF（如有）
                if old_pdf_name:
                    self.delete_nas_file(old_pdf_name)
                # update_compare_log已经在upload_to_nas方法中调用
                if not self.cleanup_done:
                    self.clean_up(merged_pdf_path, True, upload_success)
                # 上传成功后，从失败ID列表中删除
                self.remove_from_failed_ids()
            else:
                tqdm.write(f"[{self.book_id}] ❌ NAS上传失败，保留本地文件和NAS旧PDF")
                self.clean_up(merged_pdf_path, True, False)
                # 记录到失败ID明细
                self.record_failed_id("增量更新NAS上传失败")
                return None

            tqdm.write(f"[{self.book_id}] ✅ {self.comic_name} 处理完成")
            return self.book_id

    def record_failed_id(self, reason="未知原因"):
        """记录下载失败的ID和原因"""
        failed_ids_path = os.path.join(self.log_dir, "失败ID明细.txt")

        try:
            failed_entries = {}

            # 先读取已有内容
            if os.path.exists(failed_ids_path):
                with open(failed_ids_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        if ',' in line:
                            try:
                                parts = line.strip().split(',', 1)
                                failed_id = parts[0].strip()
                                failed_reason = parts[1].strip() if len(parts) > 1 else "未知原因"
                                failed_entries[failed_id] = failed_reason
                            except:
                                pass

            # 更新当前ID
            failed_entries[self.book_id] = reason

            # 写回文件
            with open(failed_ids_path, 'w', encoding='utf-8') as f:
                for bid, bid_reason in failed_entries.items():
                    f.write(f"{bid},{bid_reason}\n")

            tqdm.write(f"[{self.book_id}] 已在失败ID明细中")
            return True

        except Exception as e:
            tqdm.write(f"[{self.book_id}] ❌ 记录失败ID时出错: {str(e)}")
            return False

    def save_compare_log(self):
        """保存对比日志到文件"""
        try:
            with COMPARE_LOG_LOCK:
                with open(self.compare_log_path, 'w', encoding='utf-8') as f:
                    json.dump(self.compare_log, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            tqdm.write(f"[{self.book_id}] ❌ 保存对比日志失败: {str(e)}")
            self.logger.error(f"保存对比日志失败: {str(e)}")
            return False

    def remove_from_failed_ids(self):
        """从失败ID列表中移除当前ID"""
        failed_ids_path = os.path.join(self.log_dir, "失败ID明细.txt")
        try:
            # 如果失败列表不存在，无需操作
            if not os.path.exists(failed_ids_path):
                return

            # 读取现有失败ID
            failed_entries = {}
            with open(failed_ids_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if ',' in line:
                        try:
                            parts = line.strip().split(',', 1)
                            failed_id = parts[0].strip()
                            failed_reason = parts[1].strip() if len(parts) > 1 else "未知原因"
                            failed_entries[failed_id] = failed_reason
                        except:
                            pass

            # 从失败列表中移除当前ID
            if self.book_id in failed_entries:
                # 保存成功移除记录到日志
                detail_log_path = os.path.join(self.log_dir, "失败ID详情.log")
                with open(detail_log_path, 'a', encoding='utf-8') as f:
                    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    f.write(
                        f"[{timestamp}] BOOKID: {self.book_id}, 漫画名: {self.comic_name}, 已成功下载并从失败列表移除\n")

                # 从字典中移除
                del failed_entries[self.book_id]

                # 写回文件
                with open(failed_ids_path, 'w', encoding='utf-8') as f:
                    for bid, bid_reason in failed_entries.items():
                        f.write(f"{bid},{bid_reason}\n")

                tqdm.write(f"[{self.book_id}] 已从失败ID列表中移除")
            else:
                tqdm.write(f"[{self.book_id}] 未在失败ID列表中找到")
        except Exception as e:
            self.logger.error(f"从失败ID列表移除时出错: {str(e)}")
            tqdm.write(f"[{self.book_id}] ❌ 从失败ID列表移除时出错: {str(e)}")

    def remove_from_compare_log(self):
        """从漫小肆漫画下载对比中删除此BOOKID的信息"""
        try:
            # 使用日志锁保护共享状态
            with self.compare_log_lock:
                if str(self.book_id) in self.compare_log:
                    del self.compare_log[str(self.book_id)]
                    tqdm.write(f"[{self.book_id}] 已从下载对比中删除")

                    # 保存更新后的日志
                    with COMPARE_LOG_LOCK:
                        with open(self.compare_log_path, 'w', encoding='utf-8') as f:
                            json.dump(self.compare_log, f, indent=2, ensure_ascii=False)
                else:
                    tqdm.write(f"[{self.book_id}] 未在下载对比中找到")
        except Exception as e:
            tqdm.write(f"[{self.book_id}] 从下载对比中删除时出错: {str(e)}")

    def record_chapter_pdf_size(self, pdf_size):
        """记录章节PDF总大小到BOOKID日志，供后续验证使用"""
        pdf_size_log_path = os.path.join(self.log_dir, "pdf_sizes.json")

        try:
            # 读取现有大小日志
            pdf_sizes = {}
            if os.path.exists(pdf_size_log_path):
                try:
                    with open(pdf_size_log_path, 'r', encoding='utf-8') as f:
                        pdf_sizes = json.load(f)
                except Exception as e:
                    self.logger.error(f"读取PDF大小日志失败: {str(e)}")
                    tqdm.write(f"[{self.book_id}] 读取PDF大小日志失败: {str(e)}")

            # 更新当前BOOKID的大小信息
            pdf_sizes[str(self.book_id)] = {
                "comic_name": self.comic_name,
                "pdf_size": pdf_size,
                "chapter_count": self.chapter_count,
                "recorded_time": datetime.datetime.now().isoformat()
            }

            # 保存回文件
            with open(pdf_size_log_path, 'w', encoding='utf-8') as f:
                json.dump(pdf_sizes, f, indent=2, ensure_ascii=False)

            self.logger.info(f"已记录章节PDF总大小: {pdf_size} 字节 ({pdf_size / 1024 / 1024:.2f}MB)")
            tqdm.write(f"[{self.book_id}] 已记录章节PDF总大小: {pdf_size / 1024 / 1024:.2f}MB")
            return True

        except Exception as e:
            self.logger.error(f"记录PDF大小到日志失败: {str(e)}")
            tqdm.write(f"[{self.book_id}] 记录PDF大小到日志失败: {str(e)}")
            return False

    def get_chapter_pdf_size_from_log(self):
        """从日志中读取章节PDF总大小"""
        pdf_size_log_path = os.path.join(self.log_dir, "pdf_sizes.json")

        try:
            if not os.path.exists(pdf_size_log_path):
                return 0

            with open(pdf_size_log_path, 'r', encoding='utf-8') as f:
                pdf_sizes = json.load(f)

            if str(self.book_id) in pdf_sizes:
                pdf_size = pdf_sizes[str(self.book_id)].get("pdf_size", 0)
                if pdf_size > 0:
                    self.logger.info(f"从日志读取到章节PDF总大小: {pdf_size} 字节 ({pdf_size / 1024 / 1024:.2f}MB)")
                    tqdm.write(f"[{self.book_id}] 从日志读取到章节PDF总大小: {pdf_size / 1024 / 1024:.2f}MB")
                    return pdf_size

            return 0
        except Exception as e:
            self.logger.error(f"读取日志中的PDF大小失败: {str(e)}")
            tqdm.write(f"[{self.book_id}] 读取日志中的PDF大小失败: {str(e)}")
            return 0

    def verify_pdf_content(self, pdf_path):
        """验证PDF内容的完整性"""
        try:
            if not os.path.exists(pdf_path):
                self.logger.error(f"PDF文件不存在: {pdf_path}")
                return False, "文件不存在"

            with open(pdf_path, 'rb') as f:
                reader = PdfReader(f)
                page_count = len(reader.pages)

                # 只校验页数，不再校验书签数
                if page_count < 2:  # 至少需要封面+一个章节
                    self.logger.error(f"PDF页数不足: {page_count}页")
                    return False, f"页数不足({page_count}页)"
                # 其它内容校验不变
                file_size = os.path.getsize(pdf_path)
                expected_size = 0
                if hasattr(self, 'total_chapter_pdf_size') and self.total_chapter_pdf_size > 0:
                    expected_size = self.total_chapter_pdf_size
                else:
                    expected_size = self.get_chapter_pdf_size_from_log()
                if expected_size > 0:
                    min_size = int(expected_size * 0.95)
                    max_size = int(expected_size * 1.05)

                    if file_size < min_size or file_size > max_size:
                        self.logger.warning(f"PDF大小异常: {file_size}字节, 预期{expected_size}±5%")
                        return False, f"文件大小异常({file_size / 1024 / 1024:.2f}MB, 预期{expected_size / 1024 / 1024:.2f}MB±5%)"
                else:
                    # 如果没有参考大小，使用每页平均大小估算
                    avg_page_size = file_size / page_count
                    if avg_page_size < 10 * 1024:  # 每页平均小于10KB，可能有问题
                        self.logger.warning(f"PDF平均页面大小过小: {avg_page_size / 1024:.2f}KB/页")
                        return False, f"平均页面大小过小({avg_page_size / 1024:.2f}KB/页)"

                # 4. 尝试检查PDF中的图像质量
                try:
                    # 随机抽查几页
                    sample_pages = min(5, page_count)
                    sample_indices = random.sample(range(page_count),
                                                   sample_pages) if page_count > sample_pages else range(page_count)

                    for idx in sample_indices:
                        page = reader.pages[idx]
                        if '/Resources' in page and '/XObject' in page['/Resources']:
                            xobjects = page['/Resources']['/XObject'].get_object()
                            # 检查是否有图像对象
                            has_image = False
                            for obj in xobjects:
                                if xobjects[obj]['/Subtype'] == '/Image':
                                    has_image = True
                                    break

                            if not has_image:
                                self.logger.warning(f"PDF第{idx + 1}页没有图像对象")
                                return False, f"第{idx + 1}页没有图像内容"
                except Exception as e:
                    self.logger.error(f"检查PDF图像失败: {str(e)}")

                # 所有检查通过
                return True, f"验证通过(页数:{page_count}, 大小:{file_size / 1024 / 1024:.2f}MB)"

        except Exception as e:
            self.logger.error(f"验证PDF内容失败: {str(e)}")
            return False, f"验证异常: {str(e)}"

    def verify_chapter_content(self, chapter_title, chapter_url, image_files):
        """验证章节内容是否完整，通过比对网站内容与下载的图片数量"""
        try:
            # 清理章节标题，确保没有非法字符
            chapter_title = self.sanitize_filename(chapter_title)

            # 获取网站上的图片数量
            img_urls = self.find_images_in_chapter(chapter_url)
            website_image_count = len(img_urls) if img_urls else 0

            # 获取本地下载的图片数量
            local_image_count = len(image_files) if image_files else 0

            # 检查图片质量 - 只作为建议，不影响完整性验证结果
            quality_issues = []
            if local_image_count > 0:
                # 检查图片大小分布
                sizes = []
                for img_path in image_files:
                    if os.path.exists(img_path):
                        sizes.append(os.path.getsize(img_path))

                if sizes:
                    avg_size = sum(sizes) / len(sizes)
                    min_size = min(sizes)
                    max_size = max(sizes)

                    # 检查大小异常的图片
                    small_images = [size for size in sizes if size < avg_size * 0.3]  # 小于平均大小30%的图片
                    if small_images and len(small_images) > len(sizes) * 0.2:  # 如果超过20%的图片明显偏小
                        quality_issues.append(f"发现{len(small_images)}张明显偏小的图片，可能质量较低")

                    # 检查大小差异过大 - 只是提示，不影响验证结果
                    if max_size > min_size * 10 and len(sizes) > 3:
                        quality_issues.append(
                            f"图片大小差异过大(最小:{min_size / 1024:.1f}KB, 最大:{max_size / 1024:.1f}KB)")

                # 随机抽查图片内容
                if len(image_files) > 5:
                    sample_count = min(3, len(image_files))
                    samples = random.sample(image_files, sample_count)
                    for sample in samples:
                        try:
                            with Image.open(sample) as img:
                                width, height = img.size
                                # 检查分辨率是否过低
                                if width < 400 or height < 600:
                                    quality_issues.append(f"发现低分辨率图片({width}x{height})")
                                    break
                        except:
                            pass

            # 如果网站图片数量为0，可能是网络问题，使用其他方法估计
            if website_image_count == 0:
                self.logger.warning(f"无法获取网站图片数量: {chapter_title}")
                # 尝试重新获取
                for retry in range(3):
                    time.sleep(random.uniform(1, 3))
                    img_urls = self.find_images_in_chapter(chapter_url)
                    website_image_count = len(img_urls) if img_urls else 0
                    if website_image_count > 0:
                        break

                if website_image_count == 0:
                    # 如果仍然为0，使用本地图片数量作为参考
                    self.logger.warning(f"多次尝试后仍无法获取网站图片数量，使用本地数量: {local_image_count}")
                    # 如果有质量问题，作为单独的警告返回，但不影响完整性验证
                    if quality_issues:
                        return True, f"使用本地图片数量({local_image_count})作为参考 [图片质量提示: {', '.join(quality_issues)}]"
                    return True, f"使用本地图片数量({local_image_count})作为参考"

            # 计算下载完整率
            if website_image_count > 0:
                completion_rate = local_image_count / website_image_count

                # 记录统计信息
                self.logger.info(
                    f"章节 {chapter_title} 图片统计: 网站{website_image_count}张, 本地{local_image_count}张, 完整率{completion_rate:.2%}")

                # 判断是否完整
                if completion_rate < 0.9:  # 完整率低于90%视为不完整
                    self.logger.warning(
                        f"章节 {chapter_title} 内容不完整: 网站{website_image_count}张, 本地{local_image_count}张, 完整率{completion_rate:.2%}")
                    return False, f"内容不完整(网站{website_image_count}张, 本地{local_image_count}张, 完整率{completion_rate:.2%})"
                else:
                    # 即使完整率达标，如果有质量问题，作为单独的警告返回
                    if quality_issues:
                        self.logger.info(f"章节 {chapter_title} 图片质量提示: {', '.join(quality_issues)}")
                        # 返回True表示内容完整，但在消息中包含质量提示
                        return True, f"内容完整性验证通过(网站{website_image_count}张, 本地{local_image_count}张, 完整率{completion_rate:.2%}) [图片质量提示: {', '.join(quality_issues)}]"
                    return True, f"内容完整性验证通过(网站{website_image_count}张, 本地{local_image_count}张, 完整率{completion_rate:.2%})"
            else:
                # 无法确定网站图片数量，但本地有图片
                if local_image_count > 0:
                    # 如果有质量问题，作为单独的警告返回，但不影响完整性验证
                    if quality_issues:
                        self.logger.info(f"章节 {chapter_title} 图片质量提示: {', '.join(quality_issues)}")
                        return True, f"无法确定网站图片数量，本地有{local_image_count}张图片 [图片质量提示: {', '.join(quality_issues)}]"
                    return True, f"无法确定网站图片数量，本地有{local_image_count}张图片"
                else:
                    return False, "无法获取网站图片数量且本地无图片"

        except Exception as e:
            self.logger.error(f"验证章节内容完整性失败: {str(e)}")
            return False, f"验证异常: {str(e)}"

    def filter_failed_images(self, image_files, img_urls=None):
        """
        过滤掉下载失败或损坏的图片文件
        image_files: 本地图片文件路径列表
        img_urls: 可选，对应的原始图片URL列表
        """
        valid_images = []
        invalid_count = 0
        invalid_images = []  # 记录无效图片信息
        retry_urls = []  # 需要重试下载的URL

        # 创建文件名到URL的映射（如果提供了URL）
        url_map = {}
        if img_urls and len(img_urls) > 0:
            # 改进URL映射逻辑，处理不同的文件名格式
            for i, url in enumerate(img_urls):
                # 方法1：从URL中提取文件名
                filename = os.path.basename(url).split('?')[0]  # 去除URL参数
                url_map[filename] = url

                # 方法2：使用序号作为映射关键字
                # 为了处理下载时重命名的情况，也添加基于序号的映射
                index_filename = f"{i + 1:03d}.jpg"  # 例如 001.jpg, 002.jpg
                url_map[index_filename] = url

                # 方法3：添加不带前导零的映射
                simple_index = f"{i + 1}.jpg"  # 例如 1.jpg, 2.jpg
                url_map[simple_index] = url

        for img_path in image_files:
            try:
                # 检查文件是否存在
                if not os.path.exists(img_path):
                    self.logger.warning(f"图片文件不存在: {img_path}")
                    invalid_count += 1

                    # 尝试找到对应的URL，添加到重试列表
                    img_filename = os.path.basename(img_path)
                    img_url = url_map.get(img_filename, "")

                    # 如果直接映射找不到，尝试提取序号再匹配
                    if not img_url:
                        try:
                            # 尝试从文件名提取序号 (例如从 "059.jpg" 提取 59)
                            file_num = int(os.path.splitext(img_filename)[0].lstrip('0'))
                            index_key = f"{file_num:03d}.jpg"  # 转换为标准格式 "059.jpg"
                            img_url = url_map.get(index_key, "")

                            # 如果还是找不到，尝试使用序号直接在img_urls列表中查找
                            if not img_url and 0 < file_num <= len(img_urls):
                                img_url = img_urls[file_num - 1]  # 列表索引从0开始，所以减1
                        except (ValueError, IndexError):
                            pass

                    if img_url:
                        retry_urls.append(img_url)
                        invalid_images.append({"path": img_path, "reason": "文件不存在", "url": img_url})
                    else:
                        # 记录找不到URL的情况
                        self.logger.warning(f"找不到图片 {img_filename} 对应的URL")
                        invalid_images.append({"path": img_path, "reason": "文件不存在", "url": "找不到对应URL"})
                    continue

                # 检查文件大小是否合理
                file_size = os.path.getsize(img_path)
                if file_size < (IMAGE_MIN_SIZE_KB * 1024):  # 使用配置参数，小于这个大小的图片被视为损坏
                    self.logger.warning(f"图片文件过小: {img_path}, 大小: {file_size}字节")
                    invalid_count += 1

                    # 尝试找到对应的URL，添加到重试列表
                    img_filename = os.path.basename(img_path)
                    img_url = url_map.get(img_filename, "")

                    # 如果直接映射找不到，尝试提取序号再匹配
                    if not img_url:
                        try:
                            # 尝试从文件名提取序号 (例如从 "059.jpg" 提取 59)
                            file_num = int(os.path.splitext(img_filename)[0].lstrip('0'))
                            index_key = f"{file_num:03d}.jpg"  # 转换为标准格式 "059.jpg"
                            img_url = url_map.get(index_key, "")

                            # 如果还是找不到，尝试使用序号直接在img_urls列表中查找
                            if not img_url and 0 < file_num <= len(img_urls):
                                img_url = img_urls[file_num - 1]  # 列表索引从0开始，所以减1
                        except (ValueError, IndexError):
                            pass

                    if img_url:
                        retry_urls.append(img_url)
                        invalid_images.append(
                            {"path": img_path, "reason": f"文件过小({file_size}字节)", "url": img_url})
                    else:
                        # 记录找不到URL的情况
                        self.logger.warning(f"找不到图片 {img_filename} 对应的URL")
                        invalid_images.append(
                            {"path": img_path, "reason": f"文件过小({file_size}字节)", "url": "找不到对应URL"})
                    continue

                # 尝试打开图片验证完整性
                try:
                    with Image.open(img_path) as img:
                        img.verify()  # 验证图片完整性
                    valid_images.append(img_path)
                except Exception as e:
                    self.logger.warning(f"图片文件损坏: {img_path}, 错误: {str(e)}")
                    invalid_count += 1

                    # 尝试找到对应的URL，添加到重试列表
                    img_filename = os.path.basename(img_path)
                    img_url = url_map.get(img_filename, "")

                    # 如果直接映射找不到，尝试提取序号再匹配
                    if not img_url:
                        try:
                            # 尝试从文件名提取序号 (例如从 "059.jpg" 提取 59)
                            file_num = int(os.path.splitext(img_filename)[0].lstrip('0'))
                            index_key = f"{file_num:03d}.jpg"  # 转换为标准格式 "059.jpg"
                            img_url = url_map.get(index_key, "")

                            # 如果还是找不到，尝试使用序号直接在img_urls列表中查找
                            if not img_url and 0 < file_num <= len(img_urls):
                                img_url = img_urls[file_num - 1]  # 列表索引从0开始，所以减1
                        except (ValueError, IndexError):
                            pass

                    if img_url:
                        retry_urls.append(img_url)
                        invalid_images.append({"path": img_path, "reason": f"文件损坏({str(e)})", "url": img_url})
                    else:
                        # 记录找不到URL的情况
                        self.logger.warning(f"找不到图片 {img_filename} 对应的URL")
                        invalid_images.append(
                            {"path": img_path, "reason": f"文件损坏({str(e)})", "url": "找不到对应URL"})
                    continue

            except Exception as e:
                self.logger.error(f"检查图片文件时出错: {img_path}, 错误: {str(e)}")
                invalid_count += 1

                # 尝试找到对应的URL，添加到重试列表
                img_filename = os.path.basename(img_path)
                img_url = url_map.get(img_filename, "")
                if img_url and img_url != "未知URL":
                    retry_urls.append(img_url)

                invalid_images.append({"path": img_path, "reason": f"检查出错({str(e)})", "url": img_url})
                continue

        if invalid_count > 0:
            self.logger.info(f"已过滤 {invalid_count} 个无效图片文件，剩余 {len(valid_images)} 个有效图片")
            tqdm.write(f"[{self.book_id}] 已过滤 {invalid_count} 个无效图片文件，剩余 {len(valid_images)} 个有效图片")

            # 记录无效图片详情到日志
            for i, img_info in enumerate(invalid_images):
                tqdm.write(
                    f"  - 无效图片{i + 1}: {os.path.basename(img_info['path'])} | 原因: {img_info['reason']} | URL: {img_info['url']}")

            # 将无效图片信息写入到专门的日志文件
            try:
                invalid_log_path = os.path.join(self.log_dir, f"invalid_images_{self.book_id}.json")
                invalid_log = {}
                if os.path.exists(invalid_log_path):
                    try:
                        with open(invalid_log_path, 'r', encoding='utf-8') as f:
                            invalid_log = json.load(f)
                    except:
                        pass

                # 更新无效图片记录
                chapter_key = f"{self.book_id}_chapter_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
                invalid_log[chapter_key] = {
                    "comic_name": self.comic_name,
                    "book_id": self.book_id,
                    "time": datetime.datetime.now().isoformat(),
                    "invalid_images": invalid_images
                }

                with open(invalid_log_path, 'w', encoding='utf-8') as f:
                    json.dump(invalid_log, f, indent=2, ensure_ascii=False)
            except Exception as e:
                self.logger.error(f"记录无效图片信息失败: {str(e)}")

            # 尝试重新下载失败的图片
            if retry_urls:
                tqdm.write(f"[{self.book_id}] 尝试重新下载 {len(retry_urls)} 个失败的图片...")
                retry_files, retry_stats = self.download_images(retry_urls,
                                                                f"重试下载-{datetime.datetime.now().strftime('%H%M%S')}")
                if retry_files:
                    # 检查重新下载的图片
                    good_retry_files = []
                    for retry_file in retry_files:
                        try:
                            if os.path.exists(retry_file) and os.path.getsize(retry_file) >= 1024:
                                try:
                                    with Image.open(retry_file) as img:
                                        img.verify()
                                    good_retry_files.append(retry_file)
                                except:
                                    pass
                        except:
                            pass

                    if good_retry_files:
                        tqdm.write(f"[{self.book_id}] ✅ 成功重新下载 {len(good_retry_files)} 个图片")
                        valid_images.extend(good_retry_files)
                    else:
                        tqdm.write(f"[{self.book_id}] ❌ 重新下载失败，没有有效图片")
                else:
                    tqdm.write(f"[{self.book_id}] ❌ 重新下载失败")

        return valid_images

    def print_progress_first_download(self, total_chapters, total_images, total_pages, total_size, total_bookmarks, skip_rate):
        tqdm.write(f"[首次下载统计][{self.book_id}] 总章节: {total_chapters} 总图片: {total_images} 总页数: {total_pages} 总体积: {total_size/1024/1024:.2f}MB 总书签: {total_bookmarks} 跳过率: {skip_rate:.2%}")

    def print_progress_incremental(self, new_chapters, new_images, new_pages, new_size, new_bookmarks, skip_rate, total_chapters, total_pages, total_size, total_bookmarks):
        tqdm.write(f"[增量更新统计][{self.book_id}] 本次新增章节: {new_chapters} 新增图片: {new_images} 新增页数: {new_pages} 新增体积: {new_size/1024/1024:.2f}MB 新增书签: {new_bookmarks} 跳过率: {skip_rate:.2%}")
        tqdm.write(f"[增量更新统计][{self.book_id}] 合并后总章节: {total_chapters} 总页数: {total_pages} 总体积: {total_size/1024/1024:.2f}MB 总书签: {total_bookmarks}")

    def download_and_verify_old_pdf(self, nas_pdf_path):
        from PyPDF2 import PdfReader
        import os
        if not nas_pdf_path or not os.path.exists(nas_pdf_path):
            tqdm.write(f"[增量更新][{self.book_id}] 旧PDF文件不存在: {nas_pdf_path}")
            return False
        try:
            with open(nas_pdf_path, 'rb') as f:
                reader = PdfReader(f)
                page_count = len(reader.pages)
                if page_count < 1:
                    tqdm.write(f"[增量更新][{self.book_id}] 旧PDF页数异常: {nas_pdf_path} 页数={page_count}")
                    return False
        except Exception as e:
            tqdm.write(f"[增量更新][{self.book_id}] 旧PDF无法读取: {nas_pdf_path} 错误: {str(e)}")
            return False
        return True

    def download_and_verify_new_chapters(self, pdf_files):
        from PyPDF2 import PdfReader
        import os
        for pdf_path in pdf_files:
            if not os.path.exists(pdf_path):
                tqdm.write(f"[增量更新][{self.book_id}] 新增章节PDF不存在: {pdf_path}")
                return False
            try:
                with open(pdf_path, 'rb') as f:
                    reader = PdfReader(f)
                    page_count = len(reader.pages)
                    if page_count < 1:
                        tqdm.write(f"[增量更新][{self.book_id}] 新增章节PDF页数异常: {pdf_path} 页数={page_count}")
                        return False
            except Exception as e:
                tqdm.write(f"[增量更新][{self.book_id}] 新增章节PDF无法读取: {pdf_path} 错误: {str(e)}")
                return False
        return True

    # 在增量更新主流程分支中，严格限制：只有旧PDF和新增章节PDF都下载并效验通过后，才进入合并、上传等后续步骤。
    # 伪代码示例：
    # if not self.is_first_download:
    #     if not self.download_and_verify_old_pdf(nas_pdf_path):
    #         tqdm.write(f"[增量更新][{self.book_id}] 旧PDF校验失败，终止流程")
    #         return
    #     if not self.download_and_verify_new_chapters(pdf_files):
    #         tqdm.write(f"[增量更新][{self.book_id}] 新增章节校验失败，终止流程")
    #         return
    #     # 只有都成功，才继续合并、上传、清理等后续分支


def adjust_total_workers():
    global current_total_workers, MIN_TOTAL_WORKERS, MAX_TOTAL_WORKERS
    with adjust_lock:
        # 获取当前系统状态
        cpu = psutil.cpu_percent()
        mem = psutil.virtual_memory().percent
        old_total_workers = current_total_workers

        # 更激进的调整策略
        if cpu > 85 or mem > 85:  # 负载过高，大幅降低线程
            current_total_workers = max(MIN_TOTAL_WORKERS, current_total_workers - 2)
        elif cpu > 70 or mem > 70:  # 负载较高，小幅降低线程
            current_total_workers = max(MIN_TOTAL_WORKERS, current_total_workers - 1)
        elif (cpu < 40 and mem < 50) and current_total_workers < MAX_TOTAL_WORKERS:  # 负载较低，增加线程
            current_total_workers = min(MAX_TOTAL_WORKERS, current_total_workers + 1)

        # 不再打印调整信息，只记录到日志
        if current_total_workers != old_total_workers:
            logging.debug(
                f"漫画总线程数调整: {old_total_workers} → {current_total_workers} (CPU: {cpu}%, 内存: {mem}%)")


def adjust_workers_by_performance():
    global current_workers, MIN_CHAPTER_WORKERS, MAX_CHAPTER_WORKERS
    with adjust_lock:
        # 获取当前系统状态
        cpu = psutil.cpu_percent()
        mem = psutil.virtual_memory().percent
        old_workers = current_workers

        # 根据系统性能调整章节线程数
        if cpu > 80 or mem > 80:
            current_workers = max(MIN_CHAPTER_WORKERS, current_workers - 2)  # 大幅减少
        elif cpu > 65 or mem > 65:
            current_workers = max(MIN_CHAPTER_WORKERS, current_workers - 1)  # 小幅减少
        elif (cpu < 30 and mem < 40) and current_workers < MAX_CHAPTER_WORKERS:
            current_workers = min(MAX_CHAPTER_WORKERS, current_workers + 1)  # 小幅增加

        # 不再打印调整信息，只记录到日志
        if current_workers != old_workers:
            logging.debug(f"章节线程性能调整: {old_workers} → {current_workers} (CPU: {cpu}%, 内存: {mem}%)")


def adjust_workers():
    """根据下载成功率动态调整章节线程数"""
    global current_workers, error_count, success_count, MIN_CHAPTER_WORKERS, MAX_CHAPTER_WORKERS
    with adjust_lock:
        total_requests = error_count + success_count
        if total_requests < 200:  # 样本量不足，不调整
            return

        error_rate = error_count / total_requests
        old_workers = current_workers

        # 调整策略
        if error_rate > 0.15:  # 错误率很高，大幅减少
            current_workers = max(MIN_CHAPTER_WORKERS, current_workers - 2)
        elif error_rate > 0.05:  # 错误率较高，小幅减少
            current_workers = max(MIN_CHAPTER_WORKERS, current_workers - 1)
        elif error_rate < 0.01 and current_workers < MAX_CHAPTER_WORKERS:  # 错误率很低，增加
            current_workers = min(MAX_CHAPTER_WORKERS, current_workers + 1)

        if old_workers != current_workers:
            # 使用tqdm.write让用户看到调整
            tqdm.write(f"自适应线程调整(网络): 章节线程数 {old_workers} -> {current_workers} (错误率: {error_rate:.2%})")

        # 重置计数器
        error_count = 0
        success_count = 0


# 会话记录管理函数
def save_session(session_id, task_list, completed_tasks=None):
    """
    保存下载会话信息
    session_id: 会话ID
    task_list: 任务列表（书籍ID列表）
    completed_tasks: 已完成任务列表（默认为空集合）
    """
    if completed_tasks is None:
        completed_tasks = set()

    # 确保任务列表和已完成任务是字符串列表
    task_list = [str(task) for task in task_list]
    completed_tasks = [str(task) for task in completed_tasks]

    session_data = {
        "session_id": session_id,
        "start_time": datetime.datetime.now().isoformat(),
        "total_tasks": len(task_list),
        "task_list": task_list,
        "completed_tasks": list(completed_tasks),
        "last_update": datetime.datetime.now().isoformat()
    }

    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(SESSION_RECORD_FILE), exist_ok=True)

        # 读取现有会话记录
        all_sessions = []
        if os.path.exists(SESSION_RECORD_FILE):
            try:
                with open(SESSION_RECORD_FILE, 'r', encoding='utf-8') as f:
                    all_sessions = json.load(f)
                    if not isinstance(all_sessions, list):
                        tqdm.write(f"会话记录文件格式错误，将备份并重置")
                        backup_path = f"{SESSION_RECORD_FILE}.bak.{int(time.time())}"
                        os.rename(SESSION_RECORD_FILE, backup_path)
                        all_sessions = []
            except (json.JSONDecodeError, Exception) as e:
                tqdm.write(f"读取会话记录出错：{str(e)}，将备份并重置")
                backup_path = f"{SESSION_RECORD_FILE}.bak.{int(time.time())}"
                try:
                    os.rename(SESSION_RECORD_FILE, backup_path)
                except:
                    pass
                all_sessions = []

        # 添加新会话或更新现有会话
        session_found = False
        for i, session in enumerate(all_sessions):
            if session.get("session_id") == session_id:
                all_sessions[i] = session_data
                session_found = True
                break

        if not session_found:
            all_sessions.append(session_data)

        # 保存会话记录
        with open(SESSION_RECORD_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_sessions, f, indent=2, ensure_ascii=False)

        return True
    except Exception as e:
        tqdm.write(f"保存会话记录失败: {str(e)}")
        # 尝试备份当前内容并写入新文件
        try:
            if os.path.exists(SESSION_RECORD_FILE):
                backup_path = f"{SESSION_RECORD_FILE}.error.{int(time.time())}"
                os.rename(SESSION_RECORD_FILE, backup_path)
            with open(SESSION_RECORD_FILE, 'w', encoding='utf-8') as f:
                json.dump([session_data], f, indent=2, ensure_ascii=False)
            tqdm.write(f"已创建新的会话记录文件")
            return True
        except Exception as e2:
            tqdm.write(f"创建新会话记录文件失败: {str(e2)}")
            return False


def update_session(session_id, completed_task):
    """
    更新会话中已完成的任务
    session_id: 会话ID
    completed_task: 已完成的任务ID
    """
    completed_task = str(completed_task)  # 确保为字符串

    try:
        # 读取现有会话记录
        if not os.path.exists(SESSION_RECORD_FILE):
            tqdm.write(f"会话记录文件不存在，将创建新文件")
            save_session(session_id, [], [completed_task])
            return True

        try:
            with open(SESSION_RECORD_FILE, 'r', encoding='utf-8') as f:
                all_sessions = json.load(f)

            if not isinstance(all_sessions, list):
                tqdm.write(f"会话记录格式错误，将重置")
                save_session(session_id, [], [completed_task])
                return True
        except json.JSONDecodeError as e:
            tqdm.write(f"会话记录解析错误: {str(e)}，将重置")
            save_session(session_id, [], [completed_task])
            return True

        # 更新会话
        session_found = False
        for i, session in enumerate(all_sessions):
            if session.get("session_id") == session_id:
                session_found = True
                task_list = session.get("task_list", [])
                completed_tasks = session.get("completed_tasks", [])

                # 检查此任务是否在任务列表中
                if completed_task not in task_list:
                    tqdm.write(f"警告：任务 {completed_task} 不在当前会话的任务列表中")

                # 添加到已完成列表
                if completed_task not in completed_tasks:
                    completed_tasks.append(completed_task)
                    session["completed_tasks"] = completed_tasks
                    session["last_update"] = datetime.datetime.now().isoformat()

                    # 计算完成进度
                    total = len(task_list)
                    done = len(completed_tasks)
                    if total > 0:
                        progress = done / total * 100
                        tqdm.write(f"会话 {session_id} 进度: {done}/{total} ({progress:.1f}%)")

                all_sessions[i] = session
                break

        # 如果会话不存在，创建新会话
        if not session_found:
            tqdm.write(f"会话 {session_id} 不存在，创建新会话")
            save_session(session_id, [], [completed_task])
            return True

        # 保存会话记录
        with open(SESSION_RECORD_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_sessions, f, indent=2, ensure_ascii=False)

        return True
    except Exception as e:
        tqdm.write(f"更新会话记录失败: {str(e)}")
        return False


def get_latest_session():
    """
    获取最新的会话记录
    返回: (session_id, pending_tasks) 或 (None, [])
    """
    try:
        if not os.path.exists(SESSION_RECORD_FILE):
            return None, []

        with open(SESSION_RECORD_FILE, 'r', encoding='utf-8') as f:
            try:
                all_sessions = json.load(f)
            except json.JSONDecodeError:
                tqdm.write(f"会话记录文件格式错误，将重置会话记录")
                os.rename(SESSION_RECORD_FILE, f"{SESSION_RECORD_FILE}.bak.{int(time.time())}")
                return None, []

        if not all_sessions or not isinstance(all_sessions, list):
            tqdm.write("会话记录为空或格式不正确")
            return None, []

        # 验证会话记录格式
        valid_sessions = []
        for session in all_sessions:
            if isinstance(session, dict) and "session_id" in session and "task_list" in session:
                # 检查会话是否已完成
                all_tasks = session.get("task_list", [])
                completed_tasks = set(session.get("completed_tasks", []))
                pending_tasks = [task for task in all_tasks if task not in completed_tasks]
                if pending_tasks:  # 仅保留有未完成任务的会话
                    valid_sessions.append(session)
            else:
                tqdm.write(f"发现格式不正确的会话记录，将忽略")

        if not valid_sessions:
            tqdm.write("未找到有效的未完成会话")
            return None, []

        # 按最后更新时间排序，获取最新会话
        valid_sessions.sort(key=lambda s: s.get("last_update", ""), reverse=True)
        latest_session = valid_sessions[0]

        # 计算并验证未完成的任务
        all_tasks = latest_session.get("task_list", [])
        completed_tasks = set(latest_session.get("completed_tasks", []))
        pending_tasks = [task for task in all_tasks if task not in completed_tasks]

        # 检查时间戳
        start_time = latest_session.get("start_time", "")
        last_update = latest_session.get("last_update", "")
        try:
            if start_time:
                start_dt = datetime.datetime.fromisoformat(start_time)
                current_dt = datetime.datetime.now()
                if (current_dt - start_dt).days > 30:  # 会话超过30天
                    tqdm.write(f"警告：该会话开始于 {start_dt.strftime('%Y-%m-%d %H:%M:%S')}，已超过30天")
        except Exception:
            pass

        tqdm.write(
            f"获取最新会话：{latest_session['session_id']}，开始于 {start_time[:16].replace('T', ' ')}，上次更新 {last_update[:16].replace('T', ' ')}")

        return latest_session["session_id"], pending_tasks
    except Exception as e:
        tqdm.write(f"获取最新会话记录失败: {str(e)}")
        return None, []


def list_all_sessions():
    """
    列出所有会话记录
    """
    try:
        if not os.path.exists(SESSION_RECORD_FILE):
            tqdm.write("未找到会话记录文件")
            return []

        with open(SESSION_RECORD_FILE, 'r', encoding='utf-8') as f:
            all_sessions = json.load(f)

        if not all_sessions:
            tqdm.write("会话记录为空")
            return []

        # 按开始时间排序，从新到旧
        all_sessions.sort(key=lambda s: s.get("start_time", ""), reverse=True)

        tqdm.write("\n所有会话记录:")
        tqdm.write("=" * 80)
        tqdm.write(f"{'会话ID':<20} {'开始时间':<20} {'已完成/总任务':<15} {'最后更新时间':<20}")
        tqdm.write("-" * 80)

        for session in all_sessions:
            session_id = session.get("session_id", "未知")
            start_time = session.get("start_time", "")[:16].replace('T', ' ')
            total_tasks = len(session.get("task_list", []))
            completed_tasks = len(session.get("completed_tasks", []))
            completed_ratio = f"{completed_tasks}/{total_tasks}"
            last_update = session.get("last_update", "")[:16].replace('T', ' ')

            tqdm.write(f"{session_id:<20} {start_time:<20} {completed_ratio:<15} {last_update:<20}")

        tqdm.write("=" * 80)
        return all_sessions
    except Exception as e:
        tqdm.write(f"列出会话记录失败: {str(e)}")
        return []


def clean_completed_sessions():
    """
    清理所有已完成的会话记录
    返回: (成功清理的会话数, 剩余会话数)
    """
    try:
        if not os.path.exists(SESSION_RECORD_FILE):
            return 0, 0

        with open(SESSION_RECORD_FILE, 'r', encoding='utf-8') as f:
            all_sessions = json.load(f)

        if not all_sessions:
            return 0, 0

        original_count = len(all_sessions)
        # 保留有未完成任务的会话
        remaining_sessions = []
        for session in all_sessions:
            all_tasks = session.get("task_list", [])
            completed_tasks = set(session.get("completed_tasks", []))
            if len(completed_tasks) < len(all_tasks):  # 有未完成任务
                remaining_sessions.append(session)

        # 只有在有实际变化时才写入文件
        if len(remaining_sessions) < original_count:
            with open(SESSION_RECORD_FILE, 'w', encoding='utf-8') as f:
                json.dump(remaining_sessions, f, indent=2, ensure_ascii=False)

        cleaned_count = original_count - len(remaining_sessions)
        return cleaned_count, len(remaining_sessions)
    except Exception as e:
        tqdm.write(f"清理已完成会话记录失败: {str(e)}")
        return 0, 0


# 定时自动调整线程数

def auto_adjust_thread():
    """自适应线程调整定时器，更频繁地调整并输出诊断信息"""
    global current_workers, current_total_workers
    adjust_interval = THREAD_ADJUST_INTERVAL
    status_interval = THREAD_STATUS_INTERVAL
    last_status_time = time.time()

    tqdm.write(
        f"线程调整模式: {'自动' if THREAD_AUTO_MODE else '手动'} | 调整间隔: {adjust_interval if THREAD_AUTO_MODE else '不调整'}秒, 状态报告间隔: {status_interval}秒")

    while True:
        try:
            # 只在自动模式下执行线程调整
            if THREAD_AUTO_MODE:
                # 执行线程调整
                adjust_workers()
                adjust_total_workers()
                adjust_workers_by_performance()

            # 定期输出当前线程状态（无论自动还是手动模式）
            current_time = time.time()
            if current_time - last_status_time >= status_interval:
                cpu = psutil.cpu_percent()
                mem = psutil.virtual_memory().percent
                mode_str = "自动" if THREAD_AUTO_MODE else "手动"
                tqdm.write(
                    f"线程状态报告 [{mode_str}模式] - 章节线程: {current_workers}, 漫画线程: {current_total_workers}, CPU: {cpu}%, 内存: {mem}%")
                last_status_time = current_time

            # 等待下一次调整
            time.sleep(adjust_interval)
        except Exception as e:
            tqdm.write(f"自适应线程调整异常: {str(e)}")
            time.sleep(adjust_interval)


# 确保自适应线程守护进程启动
threading.Thread(target=auto_adjust_thread, daemon=True).start()

def clean_all_temp_dirs():
    """
    全局清理：所有BOOKID任务结束后，检查并彻底删除所有临时文件夹及其空子目录。
    """
    import os, shutil
    base_dirs = [
        os.path.join('D:/ComicsDownloads/漫小肆', '漫小肆漫画NAS中间件'),
        os.path.join('D:/ComicsDownloads/漫小肆', '漫小肆章节图片下载'),
    ]
    for base_dir in base_dirs:
        if os.path.exists(base_dir):
            for sub in os.listdir(base_dir):
                sub_path = os.path.join(base_dir, sub)
                try:
                    if os.path.isdir(sub_path):
                        shutil.rmtree(sub_path, ignore_errors=True)
                        # 再次尝试删除空目录
                        if os.path.exists(sub_path) and not os.listdir(sub_path):
                            os.rmdir(sub_path)
                except Exception as e:
                    print(f"全局清理失败: {sub_path} - {e}")
            # 如果base_dir本身已空，也尝试删除
            try:
                if not os.listdir(base_dir):
                    os.rmdir(base_dir)
            except Exception as e:
                print(f"全局清理base_dir失败: {base_dir} - {e}")

if __name__ == "__main__":
    # 检查依赖项
    check_dependencies()

    # 初始化日志目录
    log_dir = os.path.join("D:/ComicsDownloads/漫小肆", '漫小肆漫画下载日志')
    os.makedirs(log_dir, exist_ok=True)
    compare_log_path = os.path.join(log_dir, '漫小肆漫画下载对比.log')

    # 创建共享的日志字典和锁
    compare_log = {}
    compare_log_lock = Lock()

    # 用于记录失败的ID
    failed_ids = []

    # 加载或创建对比日志
    try:
        if os.path.exists(compare_log_path):
            with open(compare_log_path, 'r', encoding='utf-8') as f:
                compare_log = json.load(f)
        else:
            # 创建空日志文件
            with open(compare_log_path, 'w', encoding='utf-8') as f:
                json.dump(compare_log, f)
    except Exception as e:
        tqdm.write(f"加载对比日志失败: {str(e)}，将创建新日志")
        compare_log = {}

    # 打印当前线程设置模式
    tqdm.write(f"\n当前线程设置模式: {'自动' if THREAD_AUTO_MODE else '手动'}")
    if THREAD_AUTO_MODE:
        tqdm.write(
            f"自动模式: 初始线程数(漫画线程:{current_total_workers}, 章节线程:{current_workers}), 范围(漫画:{MIN_TOTAL_WORKERS}-{MAX_TOTAL_WORKERS}, 章节:{MIN_CHAPTER_WORKERS}-{MAX_CHAPTER_WORKERS})")
    else:
        tqdm.write(f"手动模式: 漫画线程:{current_total_workers}, 章节线程:{current_workers} (固定值)")

    if NAS_CONFIG['enabled']:
        tqdm.write("正在测试NAS连接性...")
        if not ComicDownloader.test_nas_connection():
            tqdm.write("NAS连接测试失败，程序终止。")
            exit(1)
        else:
            tqdm.write("NAS连接测试成功，继续执行。")
    else:
        tqdm.write("NAS上传未启用，跳过连接测试。")

    # 用户选择模式
    tqdm.write("\n请选择下载模式:")
    tqdm.write("1. 按页码范围下载")
    tqdm.write("2. 按BOOKID下载")
    tqdm.write("3. 继续最近一次未完成的任务")
    tqdm.write("4. 查看所有会话记录")
    tqdm.write("5. 清理已完成的会话记录")
    tqdm.write("6. 退出")

    choice = input("请输入选择 (1-6): ").strip()

    all_tasks = []
    session_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S")  # 生成会话ID

    if choice == '1':
        # 让用户输入起始页和结束页
        start_page = input("请输入起始页码（默认1）: ").strip()
        end_page = input("请输入结束页码（默认1，0表示处理到没有为止）: ").strip()
        try:
            start_page = int(start_page) if start_page else 1
        except:
            start_page = 1
        try:
            end_page = int(end_page) if end_page else 1
        except:
            end_page = 1

        tqdm.write(f"将从第{start_page}页处理到第{end_page if end_page != 0 else '最后'}页...")

        page = start_page
        while True:
            target_url = f"https://www.ikanwzd.cc/update?page={page}"
            tqdm.write(f"正在抓取第 {page} 页...")
            books = scrape_book_ids(target_url)
            # 新增：每次页面抓取后sleep，降低整体频率，sleep时间为配置区可调范围
            main_loop_sleep = random.uniform(MAIN_LOOP_SLEEP_MIN, MAIN_LOOP_SLEEP_MAX)
            tqdm.write(f"主循环抓取后休眠 {main_loop_sleep:.2f} 秒以降低频率...")
            time.sleep(main_loop_sleep)
            if not books:
                tqdm.write(f"第 {page} 页未获取到有效数据")
                if end_page == 0:
                    break
                else:
                    page += 1
                    if page > end_page:
                        break
                    continue

            tqdm.write(f"第 {page} 页获取到 {len(books)} 个漫画：")
            with tqdm(total=len(books), desc=f"第 {page} 页漫画检查") as pbar:
                for book in books:
                    downloader = ComicDownloader(
                        book['id'],
                        USER_AGENTS,
                        current_workers,  # 使用当前线程设置
                        compare_log,
                        compare_log_lock
                    )
                    downloader.get_all_chapters()
                    need_update, reason, _ = downloader.check_update_needed()
                    comic_info = f"{downloader.comic_name}+(1-{downloader.chapter_count}话)-漫小肆漫画({book['id']})"
                    pbar.update(1)

                    if need_update:
                        tqdm.write(f" - [需下载] {comic_info} - {reason}")
                        all_tasks.append(book['id'])
                    else:
                        tqdm.write(f" - [已跳过] {comic_info} - {reason}")
            if end_page == 0:
                page += 1
            else:
                if page >= end_page:
                    break
                page += 1

        # 任务列表确定后立即保存会话
        if all_tasks:
            save_session(session_id, all_tasks)
            tqdm.write(f"已保存本次会话，共 {len(all_tasks)} 个下载任务")

    elif choice == '2':
        # BOOKID模式
        book_ids = input("请输入要下载的BOOKID（多个用逗号分隔）: ").strip()
        if not book_ids:
            tqdm.write("未输入BOOKID，程序终止。")
            exit(0)

        book_list = [bid.strip() for bid in book_ids.split(',') if bid.strip()]
        tqdm.write(f"\n准备处理 {len(book_list)} 个漫画:")

        # 使用tqdm添加进度条
        with tqdm(total=len(book_list), desc="检查漫画更新状态") as pbar:
            for book_id in book_list:
                downloader = ComicDownloader(
                    book_id,
                    USER_AGENTS,
                    current_workers,  # 使用当前线程设置
                    compare_log,
                    compare_log_lock
                )
                downloader.get_all_chapters()
                need_update, reason, _ = downloader.check_update_needed()
                comic_info = f"{downloader.comic_name}+(1-{downloader.chapter_count}话)-漫小肆漫画({book_id})"
                pbar.update(1)

                if need_update:
                    tqdm.write(f" - [需下载] {comic_info} - {reason}")
                    all_tasks.append(book_id)
                else:
                    tqdm.write(f" - [已跳过] {comic_info} - {reason}")
                time.sleep(0.1)

        # 任务列表确定后立即保存会话
        if all_tasks:
            save_session(session_id, all_tasks)
            tqdm.write(f"已保存本次会话，共 {len(all_tasks)} 个下载任务")

    elif choice == '3':
        # 继续最近一次未完成的任务
        last_session_id, pending_tasks = get_latest_session()
        if not last_session_id or not pending_tasks:
            tqdm.write("未找到未完成的任务或最近的下载会话。")
            exit(0)

        tqdm.write(f"\n找到最近的未完成会话 ({last_session_id})，共有 {len(pending_tasks)} 个未完成任务")

        # 使用上一个会话ID
        session_id = last_session_id

        # 检查每个未完成任务的状态
        tqdm.write("\n正在检查未完成任务的状态...")
        with tqdm(total=len(pending_tasks), desc="检查未完成任务") as pbar:
            for book_id in pending_tasks:
                downloader = ComicDownloader(
                    book_id,
                    USER_AGENTS,
                    current_workers,  # 使用当前线程设置
                    compare_log,
                    compare_log_lock
                )
                downloader.get_all_chapters()
                need_update, reason, _ = downloader.check_update_needed()
                comic_info = f"{downloader.comic_name}+(1-{downloader.chapter_count}话)-漫小肆漫画({book_id})"
                pbar.update(1)

                if need_update:
                    tqdm.write(f" - [需下载] {comic_info} - {reason}")
                    all_tasks.append(book_id)
                else:
                    tqdm.write(f" - [已完成] {comic_info} - {reason}")
                    # 更新会话记录为已完成
                    update_session(session_id, book_id)
                time.sleep(0.1)
    elif choice == '4':
        # 查看所有会话记录
        all_sessions = list_all_sessions()
        input("按Enter键继续...")
        exit(0)
    elif choice == '5':
        # 清理已完成的会话记录
        cleaned, remaining = clean_completed_sessions()
        if cleaned > 0:
            tqdm.write(f"已清理 {cleaned} 个已完成的会话记录，剩余 {remaining} 个未完成会话")
        else:
            tqdm.write(f"没有需要清理的已完成会话记录，当前有 {remaining} 个未完成会话")
        input("按Enter键继续...")
        exit(0)
    elif choice == '6':
        tqdm.write("程序终止。")
        exit(0)
    else:
        tqdm.write("无效选择，程序终止。")
        exit(1)

    if not all_tasks:
        tqdm.write("\n没有需要下载的漫画，程序终止。")
        exit(0)

    # 在开始下载前再次确认会话状态
    try:
        last_id, last_tasks = get_latest_session()
        if last_id != session_id:
            tqdm.write(f"警告：当前会话ID ({session_id}) 与最新会话ID ({last_id}) 不一致，将重新保存会话")
            save_session(session_id, all_tasks)
    except Exception as e:
        tqdm.write(f"会话状态确认失败，将重新保存会话: {str(e)}")
        save_session(session_id, all_tasks)

    tqdm.write(f"\n共 {len(all_tasks)} 个漫画需要下载，开始批量下载...")

    # 修改为遍历任务列表，一个一个处理，失败不会影响后续任务
    completed_tasks = 0
    remaining_tasks = all_tasks.copy()  # 创建任务副本用于追踪

    # 创建单独的线程池进行处理，失败的任务不会中断后续任务
    with ThreadPoolExecutor(max_workers=current_total_workers) as executor:
        # 用于存储任务ID和future的映射
        future_to_book_id = {}

        # 提交所有任务，而不是一次提交一个
        for bid in all_tasks:
            # 创建新下载器（传递共享日志字典和锁）
            downloader = ComicDownloader(
                bid,
                USER_AGENTS,
                current_workers,  # 使用当前线程设置
                compare_log,
                compare_log_lock
            )
            # 提交任务
            future = executor.submit(downloader.download_comic)
            future_to_book_id[future] = bid  # 存储映射关系

        # 使用tqdm显示整体下载进度
        with tqdm(total=len(future_to_book_id), desc="总体下载进度") as overall_pbar:
            for future in as_completed(future_to_book_id):
                bid = future_to_book_id[future]  # 获取对应的book_id
                try:
                    result = future.result()
                    if result:
                        tqdm.write(f"✅ {bid} 下载完成")
                        # 更新会话记录
                        update_session(session_id, bid)
                        completed_tasks += 1
                    else:
                        tqdm.write(f"⚠️ {bid} 下载未完成")
                        failed_ids.append(bid)  # 记录失败的ID
                except Exception as e:
                    tqdm.write(f"❌ {bid} 下载失败: {str(e)}")
                    failed_ids.append(bid)  # 记录失败的ID

                # 从剩余任务列表中移除当前处理的任务
                if bid in remaining_tasks:
                    remaining_tasks.remove(bid)

                overall_pbar.update(1)

    # 显示完成情况
    tqdm.write(f"\n任务完成情况: {completed_tasks}/{len(all_tasks)} 完成，{len(failed_ids)}/{len(all_tasks)} 失败")

    # 保存失败的ID到TXT文件
    if failed_ids:
        failed_ids_path = os.path.join("D:/ComicsDownloads/漫小肆/漫小肆漫画下载日志", "失败ID明细.txt")
        try:
            with open(failed_ids_path, 'w', encoding='utf-8') as f:
                f.write(','.join(failed_ids))
            tqdm.write(f"已将{len(failed_ids)}个失败ID保存至: {failed_ids_path}")
        except Exception as e:
            tqdm.write(f"保存失败ID文件时出错: {str(e)}")
    else:
        tqdm.write("所有ID均下载成功，无失败ID")

    tqdm.write("全部任务完成。")

    # 清理所有临时文件夹
    clean_all_temp_dirs()