import os
import re
import pandas as pd
import datetime
import logging
import time
import json
import shutil
from pathlib import Path
import sys


# ====== 颜色定义 ======
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033\1m'
    UNDERLINE = '\033[4m'


# ====== 更新配置区域 ======
# 需要删除的字符串列表
STRINGS_TO_REMOVE = [
    "hhd800.com@", "[22sht.me]", "[7sht.me]", "[98t.tv]", "第一會所新片@SIS001@",
    "olo@SIS001@", "freedl.org@", "kcf9.com@", "kfa11.com@", "kfa33.com@",
    "4k2.com@", "gg5.co@", "kfa55.com@", "4k2.com2@", "WoXav.Com@", "[456k.me]",
    "[Thz.la]", "avav66.xyz@", "chd1080.com@", "[XavLt.Com]", "aavv38.xyz@",
    "rh2048.com@", "[bbs.yzkof.com]", "dccdom.com@", "[fbfb.me]", "@ayx086.com",
    "bbsxv.xyz-", "xhd1080.tw@", "hhb", "h_", "[ThZu.Cc]","xhd1080.com@"
]

# 需要删除的后缀列表
SUFFIXES_TO_REMOVE = [
    "FHD", "Uncen", "uncensored", "4K", "8k", "nyap2p.com", "WM",
    "_000^", "^WM", ".com", ".xyz", ".me", ".tv", ".co", ".org", "GG5", "HD", "H", ".HD"
]

# 修复的特殊替换映射表
REPLACEMENT_MAP = {
    r'(\d)ch$': r'\1-C',  # 数字+ch结尾 -> -C (优先处理)
    r'[_-]c$': '-C',  # -c或_c结尾 -> -C
    r'[_-]1$': '-CD1',  # -1结尾 -> -CD1
    r'[_-]2$': '-CD2',  # -2结尾 -> -CD2
    r'[_-]3$': '-CD3',  # -3结尾 -> -CD3
    r'[_-]4$': '-CD4',  # -4结尾 -> -CD4
    r'a$': '-CD1',  # a结尾 -> CD1
    r'b$': '-CD2',  # b结尾 -> CD2
    r'd$': '-CD4',  # d结尾 -> CD4
    r'[-_]uncen$': '',  # 删除结尾的_uncen
    r'[-_]uncensored$': '',  # 删除结尾的_uncensored
    r'^h\d+': '',  # 删除开头的h+数字
    r'([A-Za-z]+)ch$': r'\1',  # 字母+ch结尾 -> 保留字母部分
    r'([A-Za-z])(\d{3,})$': r'\1-\2',  # 字母+数字结尾 -> 字母-数字 (带边界)
    r'[_-]VR_': '-',  # 处理VR后缀
    r'cd(\d)$': r'-CD\1',  # cd1 -> -CD1
    r'[_-]?CD(\d)$': r'-CD\1',  # 标准化CD格式
    r'[_-]?Part(\d)$': r'-CD\1',  # Part1 -> -CD1
    r'[_-]?Disc(\d)$': r'-CD\1',  # Disc1 -> -CD1
    r'[_-]?D(\d)$': r'-CD\1',  # D1 -> -CD1
    r'[_-]?PT(\d)$': r'-CD\1',  # PT1 -> -CD1
    r'([A-Za-z]+)(\d{2,})$': r'\1-\2',  # 字母+数字结尾 -> 字母-数字 (更宽松的匹配，带边界)
    r'^(\d+)([a-zA-Z]+)(\d+)$': r'\2-\3',  # 处理数字+字母+数字格式
    r'\.HD$': '',  # 移除结尾的.HD
    r'^ISCR-(\d+)-C---C-CD(\d)$': r'ISCR-\1-CD\2',  # 修复ISCR格式
    r'^JUR-(\d+)(.*?)uncensored.*?$': r'JUR-\1',  # 修复JUR格式
    r'-uncensored$': '',  # 直接删除-uncensored后缀
    r'[_-]uncen.*?$': '',  # 删除包含uncen的部分
    r'sored.*?$': '',  # 删除sored残留部分
    r'--+': '-',  # 将多个连字符减少为一个
    r'-\.': '.',  # 处理连字符后跟点的情况
    r'^([A-Z]{2,5})(\d{4,6})$': r'\1-\2',  # 处理HODV21972这种格式
}

# ====== 常量定义 ======
SOURCE_DIR = r"E:\新建文件夹"
BASE_DIR = r"E:\番号名称规范化"  # 基础目录
LOG_DIR = os.path.join(BASE_DIR, "番号名称规范化处理日志文件夹")  # 日志目录
EXCEL_PATH = os.path.join(BASE_DIR,
                          "番号名称规范处理记录-{}.xlsx".format(datetime.datetime.now().strftime("%Y%m%d")))  # Excel路径
BACKUP_FILE = os.path.join(LOG_DIR, "文件名备份.json")


# ====== 辅助函数 ======
def is_case_only_change(original, new):
    """检查是否只是大小写变化"""
    return original.lower() == new.lower() and original != new


def clean_numbers(numbers):
    """数字部分不足3位补零，3位及以上原样保留"""
    if len(numbers) < 3:
        return numbers.zfill(3)
    return numbers


# ====== 初始化日志 ======
def setup_logging():
    """设置日志系统并确保目录存在"""
    # 确保基础目录和日志目录存在
    os.makedirs(BASE_DIR, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)

    # 创建日志文件路径
    log_file = os.path.join(LOG_DIR, f"处理日志-{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}.log")

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
        ]
    )
    return log_file


# ====== 改进的文件名清理函数 ======
def clean_filename(filename):
    """执行文件名清理操作，完全按用户最新规则"""
    name, ext = os.path.splitext(filename)
    original_name = filename  # 保留原始输入

    # 0. 去除常见前缀（如[NoDRM]-、h_、h+数字、[]、网站名@等）
    name = re.sub(r'^\[.*?\][-_]*', '', name)
    name = re.sub(r'^[a-zA-Z0-9]+\.com@', '', name)  # 去除网站前缀
    name = re.sub(r'^[a-zA-Z0-9]+\.me@', '', name)
    name = re.sub(r'^[a-zA-Z0-9]+\.tv@', '', name)
    name = re.sub(r'^[a-zA-Z0-9]+\.co@', '', name)
    name = re.sub(r'^[a-zA-Z0-9]+\.org@', '', name)
    name = re.sub(r'^h_+', '', name, flags=re.IGNORECASE)
    name = re.sub(r'^h\d+', '', name, flags=re.IGNORECASE)
    name = re.sub(r'^\d+', '', name)  # 纯数字前缀

    # 1. ISCR特殊处理（全局提取，优先，前缀全部丢弃）
    m = re.search(r'iscr0*([1-9]\d*)hhb?([1-4abd])?$', name, re.IGNORECASE)
    if m:
        num = clean_numbers(m.group(1))
        cd = m.group(2)
        if cd:
            cd_map = {'1': 'CD1', '2': 'CD2', '3': 'CD3', '4': 'CD4', 'a': 'CD1', 'b': 'CD2', 'd': 'CD4'}
            cd_str = '-' + cd_map.get(cd.lower(), '')
        else:
            cd_str = ''
        name = f'ISCR-{num}{cd_str}'
        return f"{name.upper()}{ext}"

    # 2. 去除配置的字符串
    for s in STRINGS_TO_REMOVE:
        name = name.replace(s, '')

    # 3. 去除所有非字母数字和-/_
    name = re.sub(r'[^A-Za-z0-9\-_]', '', name)

    # 4. 统一分隔符为-，多个-合并
    name = name.replace('_', '-')
    name = re.sub(r'-+', '-', name).strip('-')

    # 5. 去除后缀（如FHD、Uncen、uncensored、4K、8K等）
    pattern = r'[-_]?(' + '|'.join(re.escape(suf) for suf in SUFFIXES_TO_REMOVE) + r')$'
    name = re.sub(pattern, '', name, flags=re.IGNORECASE)

    # 6. 去除uncen/uncensored/nyap2p.com等残留
    name = re.sub(r'[-_]*(uncen|uncensored|nyap2p\.com|wm|gg5|hd|fhd|4k|8k)$', '', name, flags=re.IGNORECASE)

    # 7. 处理229SCUTE-1503等特殊格式，直接大写
    if re.match(r'^[0-9]{3,}SCUTE-\d+$', name, re.IGNORECASE):
        name = name.upper()
        return f"{name}{ext}"

    # 8. 处理HODV21972等格式
    m = re.match(r'^([A-Z]{2,5})(\d{4,6})$', name, re.IGNORECASE)
    if m:
        name = f"{m.group(1).upper()}-{m.group(2)}"
        return f"{name}{ext}"

    # 9. 处理JUR-xxx_uncensored等格式
    m = re.match(r'^(JUR-\d+)[._-]?uncensored.*', name, re.IGNORECASE)
    if m:
        name = m.group(1).upper()
        return f"{name}{ext}"

    # 10. 处理EBOD-xxx_...等格式
    m = re.match(r'^(EBOD-\d+)[._-].*', name, re.IGNORECASE)
    if m:
        name = m.group(1).upper()
        return f"{name}{ext}"

    # 11. 处理字母+数字+ch/c/_ch/C结尾 → -C，数字补3位（强制只保留最后3位）
    m = re.match(r'^([A-Za-z]+)[-_]?(\d+)[-_]?(ch|c|C)$', name, re.IGNORECASE)
    if m:
        prefix = m.group(1).upper()
        num_raw = m.group(2)
        if num_raw.startswith('0') and len(num_raw) > 3:
            num = num_raw[-3:]
        elif len(num_raw) < 3:
            num = num_raw.zfill(3)
        else:
            num = num_raw
        name = f"{prefix}-{num}-C"
        result = f"{name}{ext}"
        print(f"DEBUG: 输入={original_name}，输出={result}")
        return result

    # 12. 处理字母+数字+分隔符+cd1/cd2/cd3/cd4/1/2/3/4/a/b/d/A/B/D结尾 → -CDx，数字补3位（强制只保留最后3位）
    m = re.match(r'^([A-Za-z]+)[-_]?(\d+)[-_](cd[1-4]|[1-4aAbBdD])$', name, re.IGNORECASE)
    if m:
        prefix = m.group(1).upper()
        num_raw = m.group(2)
        if num_raw.startswith('0') and len(num_raw) > 3:
            num = num_raw[-3:]
        elif len(num_raw) < 3:
            num = num_raw.zfill(3)
        else:
            num = num_raw
        tail = m.group(3).lower()
        cd_map = {'1': 'CD1', '2': 'CD2', '3': 'CD3', '4': 'CD4', 'a': 'CD1', 'b': 'CD2', 'd': 'CD4',
                  'cd1': 'CD1', 'cd2': 'CD2', 'cd3': 'CD3', 'cd4': 'CD4'}
        name = f"{prefix}-{num}-{cd_map[tail]}"
        result = f"{name}{ext}"
        print(f"DEBUG: 输入={original_name}，输出={result}")
        return result

    # 13. 处理字母+数字，数字补3位（强制只保留最后3位）
    m = re.match(r'^([A-Za-z]+)[-_]?(\d+)$', name, re.IGNORECASE)
    if m:
        prefix = m.group(1).upper()
        num_raw = m.group(2)
        if num_raw.startswith('0') and len(num_raw) > 3:
            num = num_raw[-3:]
        elif len(num_raw) < 3:
            num = num_raw.zfill(3)
        else:
            num = num_raw
        name = f"{prefix}-{num}"
        result = f"{name}{ext}"
        print(f"DEBUG: 输入={original_name}，输出={result}")
        return result

    # 14. 处理 mxsps605A 这种（字母+数字+A/B），数字补3位
    m = re.match(r'^([A-Za-z]+)0*(\d+)([aAbB])$', name, re.IGNORECASE)
    if m:
        prefix = m.group(1).upper()
        num = clean_numbers(m.group(2))
        tail = m.group(3).lower()
        cd_map = {'a': 'CD1', 'b': 'CD2'}
        name = f"{prefix}-{num}-{cd_map[tail]}"
        result = f"{name}{ext}"
        print(f"DEBUG: 输入={original_name}，输出={result}")
        return result

    # 15. 处理 mdvr00306-CD4 这种（字母+前导0数字-CDx），数字补3位
    m = re.match(r'^([A-Za-z]+)0*(\d+)-CD(\d)$', name, re.IGNORECASE)
    if m:
        prefix = m.group(1).upper()
        num = clean_numbers(m.group(2))
        cd = m.group(3)
        name = f"{prefix}-{num}-CD{cd}"
        result = f"{name}{ext}"
        print(f"DEBUG: 输入={original_name}，输出={result}")
        return result

    # 16. 处理数字+字母+数字（如200GANA-3173-uncensored-nyap2p.com → GANA-3173），数字补3位
    m = re.match(r'^(\d+)([A-Za-z]+)[-_]?(\d+)', name, re.IGNORECASE)
    if m:
        prefix = m.group(2).upper()
        num = clean_numbers(m.group(3))
        name = f"{prefix}-{num}"
        result = f"{name}{ext}"
        print(f"DEBUG: 输入={original_name}，输出={result}")
        return result

    # 17. 统一大写
    name = name.upper()
    result = f"{name}{ext}"
    print(f"DEBUG: 输入={original_name}，输出={result}")
    return result


# ====== 备份原始文件名 ======
def backup_filenames(file_map):
    """备份原始文件名到JSON文件"""
    # 确保日志目录存在
    os.makedirs(LOG_DIR, exist_ok=True)

    with open(BACKUP_FILE, 'w', encoding='utf-8') as f:
        json.dump(file_map, f, ensure_ascii=False, indent=2)
    logging.info(f"文件名备份已保存至: {BACKUP_FILE}")


# ====== 恢复原始文件名 ======
def restore_filenames():
    """从备份恢复原始文件名"""
    if not os.path.exists(BACKUP_FILE):
        logging.error("找不到备份文件，无法恢复")
        print(f"{Colors.RED}错误: 找不到备份文件，无法恢复{Colors.ENDC}")
        return False

    with open(BACKUP_FILE, 'r', encoding='utf-8') as f:
        file_map = json.load(f)

    restore_count = 0
    for new_name, original_name in file_map.items():
        new_path = os.path.join(SOURCE_DIR, new_name)
        original_path = os.path.join(SOURCE_DIR, original_name)

        if not os.path.exists(new_path):
            logging.warning(f"文件不存在: {new_name}")
            print(f"{Colors.YELLOW}警告: 文件不存在: {new_name}{Colors.ENDC}")
            continue

        # 处理目标文件已存在的情况
        if os.path.exists(original_path):
            # 比较文件大小
            new_size = os.path.getsize(new_path)
            original_size = os.path.getsize(original_path)

            # 检查是否只是大小写变化
            if is_case_only_change(original_name, new_name):
                # 如果是大小写变化，直接恢复
                os.rename(new_path, original_path)
                restore_count += 1
                logging.info(f"已恢复大小写: {original_name}")
                print(f"{Colors.GREEN}已恢复大小写: {original_name}{Colors.ENDC}")
            elif new_size > original_size:
                os.remove(original_path)
                os.rename(new_path, original_path)
                restore_count += 1
                logging.info(f"已覆盖: {original_name} (大小: {new_size} > {original_size})")
                print(f"{Colors.GREEN}已覆盖: {original_name} (大小: {new_size} > {original_size}){Colors.ENDC}")
            else:
                # 创建唯一文件名
                base, ext = os.path.splitext(original_name)
                counter = 1
                while True:
                    unique_name = f"{base}_{counter}{ext}"
                    unique_path = os.path.join(SOURCE_DIR, unique_name)
                    if not os.path.exists(unique_path):
                        break
                    counter += 1

                os.rename(new_path, unique_path)
                logging.info(f"已恢复为唯一文件名: {unique_name} (原始文件已存在)")
                print(f"{Colors.YELLOW}已恢复为唯一文件名: {unique_name} (原始文件已存在){Colors.ENDC}")
                restore_count += 1
        else:
            os.rename(new_path, original_path)
            restore_count += 1
            logging.info(f"已恢复: {original_name}")
            print(f"{Colors.GREEN}已恢复: {original_name}{Colors.ENDC}")

    # 删除备份文件
    os.remove(BACKUP_FILE)
    logging.info(f"恢复完成: {restore_count}个文件已恢复，备份文件已删除")
    print(f"{Colors.GREEN}恢复完成: {restore_count}个文件已恢复，备份文件已删除{Colors.ENDC}")
    return True


# ====== 主处理函数 ======
def process_files(auto_mode=False):
    """主处理函数：扫描、重命名文件并生成报告"""
    # 设置日志
    log_file = setup_logging()
    logging.info(f"====== 开始处理: {SOURCE_DIR} ======")
    print(f"{Colors.CYAN}====== 开始处理: {SOURCE_DIR} ======{Colors.ENDC}")

    # 检查源目录是否存在
    if not os.path.exists(SOURCE_DIR):
        logging.error(f"源目录不存在: {SOURCE_DIR}")
        print(f"{Colors.RED}错误: 源目录不存在: {SOURCE_DIR}{Colors.ENDC}")
        return

    # 获取视频文件
    video_files = [f for f in os.listdir(SOURCE_DIR)
                   if os.path.isfile(os.path.join(SOURCE_DIR, f)) and
                   f.lower().endswith(('.mp4', '.avi', '.mkv', '.wmv', '.mov', '.rmvb', '.flv'))]

    if not video_files:
        logging.info("未找到视频文件")
        print(f"{Colors.YELLOW}未找到视频文件{Colors.ENDC}")
        return

    # 创建Excel记录
    excel_data = []
    file_map = {}
    stats = {
        "total": len(video_files),
        "renamed": 0,
        "case_updated": 0,
        "skipped": 0,
        "no_change": 0,
        "failed": 0,
        "overwritten": 0
    }

    # 处理每个文件
    for idx, filename in enumerate(video_files, 1):
        original_path = os.path.join(SOURCE_DIR, filename)

        try:
            # 清理文件名
            new_name = clean_filename(filename)
            new_path = os.path.join(SOURCE_DIR, new_name)

            # 如果文件名没有变化
            if filename == new_name:
                logging.info(f"无需修改: {filename}")
                print(f"{Colors.YELLOW}无需修改: {filename}{Colors.ENDC}")
                excel_data.append({
                    "序号": idx,
                    "原文件名": filename,
                    "规范文件名": new_name,
                    "操作类型": "无需修改"
                })
                stats["no_change"] += 1
                continue

            # 检查是否只是大小写变化
            if is_case_only_change(filename, new_name):
                # 创建临时文件名
                temp_name = f"__temp_{int(time.time() * 1000)}_{filename}"
                temp_path = os.path.join(SOURCE_DIR, temp_name)

                # 先重命名为临时文件
                os.rename(original_path, temp_path)
                # 再从临时文件重命名为目标文件名
                os.rename(temp_path, new_path)

                action = "重命名（更新大小写）"
                file_map[new_name] = filename
                logging.info(f"{action}成功: {filename} -> {new_name}")
                print(f"{Colors.GREEN}{action}成功: {filename} -> {new_name}{Colors.ENDC}")
                excel_data.append({
                    "序号": idx,
                    "原文件名": filename,
                    "规范文件名": new_name,
                    "操作类型": "更新大小写"
                })
                stats["case_updated"] += 1
                continue

            # 处理目标文件已存在的情况
            if os.path.exists(new_path):
                # 检查是否是同一个文件（通过文件大小）
                original_size = os.path.getsize(original_path)
                existing_size = os.path.getsize(new_path)

                # 如果是同一个文件，直接跳过
                if original_size == existing_size:
                    logging.info(f"跳过: {filename} -> {new_name} (文件已存在且大小相同)")
                    print(f"{Colors.BLUE}跳过: {filename} -> {new_name} (文件已存在且大小相同){Colors.ENDC}")
                    excel_data.append({
                        "序号": idx,
                        "原文件名": filename,
                        "规范文件名": new_name,
                        "操作类型": "跳过（文件已存在）"
                    })
                    stats["skipped"] += 1
                    continue

                # 如果不是同一个文件，处理冲突
                if original_size > existing_size:
                    os.remove(new_path)
                    os.rename(original_path, new_path)
                    action = "覆盖"
                    file_map[new_name] = filename
                    logging.info(f"{action}成功: {filename} -> {new_name}")
                    print(f"{Colors.GREEN}{action}成功: {filename} -> {new_name}{Colors.ENDC}")
                    excel_data.append({
                        "序号": idx,
                        "原文件名": filename,
                        "规范文件名": new_name,
                        "操作类型": "覆盖"
                    })
                    stats["overwritten"] += 1
                else:
                    # 创建唯一文件名
                    base, ext = os.path.splitext(new_name)
                    counter = 1
                    while True:
                        unique_name = f"{base}_{counter}{ext}"
                        unique_path = os.path.join(SOURCE_DIR, unique_name)
                        if not os.path.exists(unique_path):
                            break
                        counter += 1

                    os.rename(original_path, unique_path)
                    new_name = unique_name
                    action = "重命名"
                    file_map[unique_name] = filename
                    logging.info(f"{action}成功: {filename} -> {unique_name}")
                    print(f"{Colors.GREEN}{action}成功: {filename} -> {unique_name}{Colors.ENDC}")
                    excel_data.append({
                        "序号": idx,
                        "原文件名": filename,
                        "规范文件名": unique_name,
                        "操作类型": "重命名（冲突解决）"
                    })
                    stats["renamed"] += 1
            else:
                os.rename(original_path, new_path)
                action = "重命名"
                file_map[new_name] = filename
                logging.info(f"{action}成功: {filename} -> {new_name}")
                print(f"{Colors.GREEN}{action}成功: {filename} -> {new_name}{Colors.ENDC}")
                excel_data.append({
                    "序号": idx,
                    "原文件名": filename,
                    "规范文件名": new_name,
                    "操作类型": "重命名"
                })
                stats["renamed"] += 1

        except Exception as e:
            error_msg = f"处理失败: {filename} - {str(e)}"
            logging.error(error_msg)
            print(f"{Colors.RED}{error_msg}{Colors.ENDC}")
            excel_data.append({
                "序号": idx,
                "原文件名": filename,
                "规范文件名": f"处理失败: {str(e)}",
                "操作类型": "失败"
            })
            stats["failed"] += 1

    # 保存Excel文件
    if excel_data:
        df = pd.DataFrame(excel_data)
        # 确保基础目录存在
        os.makedirs(BASE_DIR, exist_ok=True)
        df.to_excel(EXCEL_PATH, index=False)
        logging.info(f"Excel记录已保存至: {EXCEL_PATH}")
        print(f"{Colors.GREEN}Excel记录已保存至: {EXCEL_PATH}{Colors.ENDC}")

    # 备份原始文件名
    if file_map:
        backup_filenames(file_map)
        logging.info(f"共处理 {stats['total']} 个文件")
        print(f"\n{Colors.CYAN}====== 处理统计 ======{Colors.ENDC}")
        print(f"{Colors.GREEN}成功重命名: {stats['renamed']}{Colors.ENDC}")
        print(f"{Colors.GREEN}大小写更新: {stats['case_updated']}{Colors.ENDC}")
        print(f"{Colors.GREEN}覆盖文件: {stats['overwritten']}{Colors.ENDC}")
        print(f"{Colors.BLUE}跳过文件: {stats['skipped']}{Colors.ENDC}")
        print(f"{Colors.YELLOW}无需修改: {stats['no_change']}{Colors.ENDC}")
        print(f"{Colors.RED}处理失败: {stats['failed']}{Colors.ENDC}")
        print(f"{Colors.CYAN}总计: {stats['total']}{Colors.ENDC}")

        # 打印所有修改记录
        print(f"\n{Colors.CYAN}====== 所有修改记录 ======{Colors.ENDC}")
        modified_count = 0
        for i, row in enumerate(excel_data, 1):
            if row["操作类型"] == "失败":
                color = Colors.RED
                symbol = "✗"
            elif row["操作类型"] == "无需修改":
                color = Colors.YELLOW
                symbol = "○"
            elif row["操作类型"] == "跳过（文件已存在）":
                color = Colors.BLUE
                symbol = "↷"
            else:
                color = Colors.GREEN
                symbol = "✓"
                modified_count += 1

            if row["原文件名"] != row["规范文件名"] or row["操作类型"] == "失败":
                print(
                    f"{color}{i:3d}. {symbol} [{row['操作类型']}] {row['原文件名']} -> {row['规范文件名']}{Colors.ENDC}")

        if modified_count == 0:
            print(f"{Colors.YELLOW}没有文件被修改{Colors.ENDC}")

        # 在自动模式下跳过用户交互
        if auto_mode:
            logging.info("自动模式 - 跳过恢复询问")
            print(f"{Colors.GREEN}已启用自动模式，跳过恢复询问{Colors.ENDC}")
            print(f"{Colors.YELLOW}修改已保留{Colors.ENDC}")
            return

        # 询问是否恢复
        print(f"\n{Colors.CYAN}处理完成! 是否恢复原始文件名? (Y/N){Colors.ENDC}")
        choice = input().strip().upper()

        if choice == 'Y':
            logging.info("用户选择恢复文件名")
            print(f"{Colors.CYAN}正在恢复文件名...{Colors.ENDC}")
            restore_filenames()
            print(f"{Colors.GREEN}文件名恢复完成!{Colors.ENDC}")
        else:
            logging.info("用户选择保留修改")
            print(f"{Colors.GREEN}修改已保留{Colors.ENDC}")
    else:
        logging.info("没有文件被修改")
        print(f"{Colors.YELLOW}没有文件被修改{Colors.ENDC}")


# ====== 主程序 ======
if __name__ == "__main__":
    # 添加编码处理
    if sys.stdout.encoding is None or sys.stdout.encoding.lower() != 'utf-8':
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except:
            # 如果重配置失败，设置环境变量
            os.environ["PYTHONIOENCODING"] = "utf-8"

    print(f"{Colors.CYAN}{'=' * 50}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}视频文件名称规范化处理{Colors.ENDC}")
    print(f"{Colors.CYAN}{'=' * 50}{Colors.ENDC}")
    print(f"{Colors.BLUE}源目录: {SOURCE_DIR}{Colors.ENDC}")
    print(f"{Colors.BLUE}日志目录: {LOG_DIR}{Colors.ENDC}")
    print(f"{Colors.BLUE}报告目录: {BASE_DIR}{Colors.ENDC}")
    print(f"{Colors.CYAN}{'=' * 50}{Colors.ENDC}\n")

    # 检查是否要恢复
    auto_mode = False

    # 在子进程环境中跳过交互
    if "--auto" in sys.argv:
        auto_mode = True
        print(f"{Colors.YELLOW}已启用自动模式{Colors.ENDC}")

    if os.path.exists(BACKUP_FILE) and not auto_mode:
        print(f"{Colors.YELLOW}检测到未完成的备份文件，是否要恢复? (Y/N){Colors.ENDC}")
        choice = input().strip().upper()
        if choice == 'Y':
            print(f"{Colors.CYAN}正在恢复文件名...{Colors.ENDC}")
            restore_filenames()
            print(f"{Colors.GREEN}文件名恢复完成!{Colors.ENDC}")
            exit()

    # 执行主处理
    start_time = time.time()
    process_files(auto_mode)
    end_time = time.time()

    print(f"\n{Colors.CYAN}{'=' * 50}{Colors.ENDC}")
    print(f"{Colors.GREEN}处理完成! 耗时: {end_time - start_time:.2f}秒{Colors.ENDC}")
    print(f"{Colors.BLUE}详细日志请查看: {LOG_DIR}{Colors.ENDC}")
    print(f"{Colors.BLUE}Excel报告: {EXCEL_PATH}{Colors.ENDC}")
    print(f"{Colors.CYAN}{'=' * 50}{Colors.ENDC}")