import socket
import threading
import re
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
import ipaddress
import sys
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import os
import uuid
import base64
from cryptography.fernet import Fernet
try:
    from smbprotocol.connection import Connection
    from smbprotocol.session import Session
    from smbprotocol.tree import TreeConnect
    from smbprotocol.open import Open, CreateDisposition, FileAttributes, FilePipePrinterAccessMask, ImpersonationLevel, ShareAccess, CreateOptions
except ImportError:
    pass  # 允许无smbprotocol时不报错
import tkinter.filedialog as filedialog
import ctypes
import ctypes.wintypes
import tkinter.simpledialog as simpledialog
import traceback
from PIL import Image, ImageTk


class FeiNiuScanner:
    def __init__(self):
        self.devices = []
        self.scanning = False
        self.callback = None
        self.total_ips = 0
        self.scanned_ips = 0
        self.progress_callback = None
        self.thread_count = 100
        self.ports_to_scan = [8000]
        self.devices_lock = threading.Lock()
        self.log_path = r'D:\飞牛NAS设备记录日志\飞牛统一管理工具专用日志'
        self.log_file = os.path.join(self.log_path, '飞牛设备信息.log')
        if not os.path.exists(self.log_path):
            os.makedirs(self.log_path)
        self.device_log = self.load_device_log()

    def get_local_ips(self):
        """获取本地所有IP地址（使用socket替代netifaces）"""
        ips = []
        try:
            # 获取本地主机名
            host_name = socket.gethostname()

            # 获取本机所有IP地址
            ip_list = socket.gethostbyname_ex(host_name)[2]

            # 获取子网掩码（使用系统命令）
            for ip in ip_list:
                if ip.startswith("127.") or ip.startswith("169.254."):
                    continue  # 跳过回环地址和链路本地地址

                # 尝试获取子网掩码（Windows和Linux/macOS方式不同）
                subnet_mask = self.get_subnet_mask(ip)
                if subnet_mask:
                    # 计算网络地址
                    network = self.calculate_network(ip, subnet_mask)
                    if network:
                        ips.append(network)
                else:
                    # 如果无法获取子网掩码，则假设是标准的/24子网
                    parts = ip.split('.')
                    ips.append(f"{parts[0]}.{parts[1]}.{parts[2]}.0/24")

        except Exception as e:
            print(f"获取本地IP地址时出错: {e}")

        # 如果以上方法失败，使用回退方法
        if not ips:
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                s.close()
                if local_ip:
                    parts = local_ip.split('.')
                    ips.append(f"{parts[0]}.{parts[1]}.{parts[2]}.0/24")
            except:
                pass

        return list(set(ips))  # 去重

    def get_subnet_mask(self, ip):
        """获取指定IP的子网掩码（使用系统命令）"""
        try:
            # Windows系统
            if sys.platform.startswith('win'):
                import subprocess
                result = subprocess.check_output(['ipconfig'], text=True)

                # 查找当前IP对应的子网掩码
                pattern = re.compile(
                    r"IPv4 Address.*?{}.*?Subnet Mask.*?(\d+\.\d+\.\d+\.\d+)".format(ip.replace('.', r'\.')),
                    re.DOTALL | re.IGNORECASE
                )
                match = pattern.search(result)
                if match:
                    return match.group(1)

            # Linux/macOS系统
            else:
                import netifaces
                for interface in netifaces.interfaces():
                    addrs = netifaces.ifaddresses(interface)
                    if netifaces.AF_INET in addrs:
                        for addr_info in addrs[netifaces.AF_INET]:
                            if addr_info['addr'] == ip:
                                return addr_info.get('netmask')

        except Exception:
            return None

        return None

    def calculate_network(self, ip, subnet_mask):
        """计算网络地址"""
        try:
            # 将IP和子网掩码转换为整数
            ip_int = int(ipaddress.IPv4Address(ip))
            mask_int = int(ipaddress.IPv4Address(subnet_mask))

            # 计算网络地址
            network_int = ip_int & mask_int
            network_ip = str(ipaddress.IPv4Address(network_int))

            # 计算CIDR前缀长度
            prefix = bin(mask_int).count("1")

            return f"{network_ip}/{prefix}"
        except Exception:
            return None

    def scan_network(self, callback, progress_callback=None, finished_callback=None):
        try:
            print(f"【DEBUG】scan_network 被调用，self.scanning={self.scanning}", flush=True)
            if self.scanning:
                self.devices = []
                self.callback = callback
                self.progress_callback = progress_callback
                self.finished_callback = finished_callback

                # 获取所有本地网络
                networks = self.get_local_ips()
                self.total_ips = 0
                self.scanned_ips = 0
                for network in networks:
                    net = ipaddress.ip_network(network, strict=False)
                    self.total_ips += (net.num_addresses - 2) * len(self.ports_to_scan)  # IP数*端口数

                # 创建线程扫描每个子网
                threads = []
                for network in networks:
                    thread = threading.Thread(target=self.scan_subnet, args=(network,))
                    thread.daemon = True
                    thread.start()
                    threads.append(thread)

                # 等待所有扫描完成
                for thread in threads:
                    thread.join()
                if self.finished_callback:
                    self.finished_callback()
            else:
                print(f"【DEBUG】scan_network 直接return, self.scanning={self.scanning}", flush=True)
        except Exception as e:
            print(f"【ERROR】scan_network异常: {e}", flush=True)

    def scan_subnet(self, network):
        """多线程并发扫描指定子网的端口"""
        try:
            net = ipaddress.ip_network(network, strict=False)
            tasks = []
            with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
                for ip in net.hosts():
                    ip_str = str(ip)
                    for port in self.ports_to_scan:
                        tasks.append(executor.submit(self.scan_ip_port, ip_str, port))
                for future in as_completed(tasks):
                    pass  # 不再在这里递增进度
        except Exception as e:
            print(f"扫描网络 {network} 时出错: {e}")

    def scan_ip_port(self, ip, port):
        print(f"[{threading.get_ident()}] 正在扫描 {ip}:{port}", flush=True)
        if self.check_port(ip, port):
            print(f"[{threading.get_ident()}] 端口开放: {ip}:{port}", flush=True)
            device_info = self.get_device_info(ip, port)
            if device_info:
                with self.devices_lock:
                    # 检查是否已存在相同IP和端口的设备，避免重复
                    exists = any(dev['ip'] == device_info['ip'] and str(dev['port']) == str(device_info['port']) for dev in self.devices)
                    if not exists:
                        self.devices.append(device_info)
                        if self.callback:
                            self.callback(self.devices)
        else:
            print(f"[{threading.get_ident()}] 端口关闭: {ip}:{port}", flush=True)
        # 进度递增和回调
        with self.devices_lock:
            self.scanned_ips += 1
        if self.progress_callback:
            self.progress_callback(self.scanned_ips, self.total_ips)

    def check_port(self, ip, port, timeout=0.5):
        """用HTTP请求判断端口是否开放"""
        try:
            url = f"http://{ip}:{port}"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
            response = requests.get(url, timeout=timeout, headers=headers)
            return response.status_code == 200
        except Exception as e:
            print(f"[{threading.get_ident()}] 检查端口 {ip}:{port} 失败: {e}", flush=True)
            return False

    def get_device_info(self, ip, port):
        """只要HTTP能访问就认为是飞牛设备"""
        try:
            url = f"http://{ip}:{port}"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
            response = requests.get(url, timeout=2, headers=headers)
            print(f"设备 {ip}:{port} 状态码: {response.status_code}，返回内容: {response.text[:100]}")
            if response.status_code == 200:
                # 先用默认信息
                info = {
                    'ip': ip,
                    'name': '飞牛NAS',
                    'cpu': '未知',
                    'memory': '未知',
                    'firmware': '未知',
                    'status': '在线',
                    'port': port,
                    'last_seen': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                # 如果日志有记录，优先用日志内容
                log = self.device_log.get(ip)
                if log:
                    info['name'] = log.get('name', info['name'])
                    info['cpu'] = log.get('cpu', info['cpu'])
                    info['memory'] = log.get('memory', info['memory'])
                    info['firmware'] = log.get('firmware', info['firmware'])
                return info
        except Exception as e:
            print(f"访问 {ip}:{port} 出错: {e}")
        return None

    def stop_scan(self):
        """停止扫描"""
        self.scanning = False

    def load_device_log(self):
        if os.path.exists(self.log_file):
            try:
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}

    def save_device_log(self):
        try:
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(self.device_log, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f'写入日志失败: {e}')

    def get_unc_path(self, path):
        # 仅在Windows下有效
        if not path or not os.path.isabs(path):
            return path
        import ctypes
        import ctypes.wintypes
        buf = ctypes.create_unicode_buffer(1024)
        size = ctypes.wintypes.DWORD(1024)
        if ctypes.windll.mpr.WNetGetUniversalNameW(
            path,
            0x00000001,  # UNIVERSAL_NAME_INFO_LEVEL
            buf,
            ctypes.byref(size)
        ) == 0:
            unc = buf.value
            parts = unc.strip("\\").split("\\")
            if len(parts) >= 2:
                share = parts[1]
                subpath = "/".join(parts[2:]) if len(parts) > 2 else ""
                return share + ("/" + subpath if subpath else "")
        return path


class CredentialManager:
    def __init__(self, key_file, cred_file):
        self.key_file = key_file
        self.cred_file = cred_file
        self.key = self.load_key()
        self.fernet = Fernet(self.key)
        self.creds = self.load_creds()

    def load_key(self):
        if not os.path.exists(self.key_file):
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            return key
        with open(self.key_file, 'rb') as f:
            return f.read()

    def load_creds(self):
        if not os.path.exists(self.cred_file):
            return {}
        with open(self.cred_file, 'rb') as f:
            data = f.read()
            if not data:
                return {}
            try:
                return json.loads(self.fernet.decrypt(data).decode())
            except Exception:
                return {}

    def save_creds(self):
        with open(self.cred_file, 'wb') as f:
            f.write(self.fernet.encrypt(json.dumps(self.creds).encode()))

    def get_cred(self, ip):
        return self.creds.get(ip, {"user": "", "pass": ""})

    def set_cred(self, ip, user, passwd):
        self.creds[ip] = {"user": user, "pass": passwd}
        self.save_creds()


class SyncManager:
    def __init__(self, nas_ip, nas_share, nas_folder, nas_user, nas_pass, local_folder, log_dir, progress_callback=None):
        print(f"【SYNC】初始化: nas_ip={nas_ip}, nas_share=[{nas_share}], nas_folder=[{nas_folder}], user={nas_user}")
        self.nas_ip = nas_ip
        self.nas_share = nas_share
        self.nas_folder = nas_folder
        self.nas_user = nas_user
        self.nas_pass = nas_pass
        self.local_folder = local_folder
        self.log_dir = log_dir
        self.progress_callback = progress_callback
        self.nas_log_file = os.path.join(log_dir, f"NAS_{nas_ip.replace('.', '_')}.log")
        self.local_log_file = os.path.join(log_dir, f"本地_{os.path.basename(local_folder)}.log")
        self.nas_files = []
        self.local_files = []
        self.total_steps = 1
        self.current_step = 0

    def scan_local(self):
        file_list = []
        for root, dirs, files in os.walk(self.local_folder):
            for f in files:
                full_path = os.path.join(root, f)
                rel_path = os.path.relpath(full_path, self.local_folder)
                file_list.append({
                    "name": f,
                    "path": rel_path.replace("\\", "/"),
                    "size": os.path.getsize(full_path),
                    "type": "file"
                })
        self.local_files = file_list
        self.save_log(self.local_log_file, file_list)
        return file_list

    def smb_list_files(self, conn, share, path=""):
        file_list = []
        for f in conn.listPath(share, path):
            if f.filename in ('.', '..'):
                continue
            full_path = path + '/' + f.filename if path else f.filename
            if f.isDirectory:
                file_list += self.smb_list_files(conn, share, full_path)
            else:
                file_list.append({
                    "name": f.filename,
                    "path": full_path.lstrip('/'),
                    "size": f.file_size,
                    "type": "file"
                })
        return file_list

    def scan_smb(self):
        # 使用pysmb递归遍历NAS目录
        print(f"【SYNC】pysmb连接: //{self.nas_ip}/[{self.nas_share}]，子目录: [{self.nas_folder}]")
        file_list = []
        try:
            from smb.SMBConnection import SMBConnection
            conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
            conn.connect(self.nas_ip, 445)
            base_path = self.nas_folder.strip('/') if self.nas_folder else ''
            file_list = self.smb_list_files(conn, self.nas_share, base_path)
            conn.close()
        except Exception as e:
            import traceback
            print(f"【SYNC】pysmb扫描失败: {e}")
            traceback.print_exc()
        self.nas_files = file_list
        self.save_log(self.nas_log_file, file_list)
        return file_list

    def save_log(self, log_path, data):
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def load_log(self, log_path):
        if not os.path.exists(log_path):
            return []
        with open(log_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def compare_logs(self):
        # 返回本地独有、NAS独有、都存在但大小不同
        nas_map = {f['path']: f for f in self.nas_files}
        local_map = {f['path']: f for f in self.local_files}
        only_nas = [f for p, f in nas_map.items() if p not in local_map]
        only_local = [f for p, f in local_map.items() if p not in nas_map]
        diff_size = [f for p, f in nas_map.items() if p in local_map and f['size'] != local_map[p]['size']]
        return only_nas, only_local, diff_size

    def sync(self, mode):
        # mode: 'NAS-本地', '本地-NAS', '双向同步(全保留)', '双向同步(NAS为准)', '双向同步(本地为准)'
        # 这里只做伪代码，实际操作需用shutil/smb写文件
        only_nas, only_local, diff_size = self.compare_logs()
        # 统计总步数
        self.total_steps = len(only_nas) + len(only_local) + len(diff_size)
        self.current_step = 0
        # 伪代码：实际应分别用shutil.copy和smb写文件
        if mode == 'NAS-本地(清空本地)':
            # 清空本地后从NAS下载全部文件到本地
            for f in self.local_files:
                try:
                    local_path = os.path.join(self.local_folder, f['path'])
                    if os.path.exists(local_path):
                        os.remove(local_path)
                        print(f"[DEBUG] 已删除本地文件: {local_path}")
                except Exception as e:
                    print(f"[DEBUG] 删除本地文件失败: {f['path']} {e}")
                self.update_progress()
            # 下载
            try:
                from smb.SMBConnection import SMBConnection
                print("[DEBUG] 尝试连接NAS进行NAS-本地(清空本地)同步")
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                for f in self.nas_files:
                    local_path = os.path.join(self.local_folder, f['path'])
                    os.makedirs(os.path.dirname(local_path), exist_ok=True)
                    with open(local_path, 'wb') as fp:
                        print(f"[DEBUG] 正在下载: {f['path']} -> {local_path}")
                        try:
                            conn.retrieveFile(self.nas_share, '/' + f['path'], fp)
                            print(f"[DEBUG] 下载完成: {f['path']}")
                        except Exception as e:
                            print(f"[DEBUG] 下载失败: {f['path']} {e}")
                    self.update_progress()
                conn.close()
                print("[DEBUG] NAS-本地(清空本地)同步完成")
            except Exception as e:
                import traceback
                print(f"[DEBUG] NAS文件下载失败: {e}")
                traceback.print_exc()
        elif mode == 'NAS-本地(保留本地)':
            # 保留本地后从NAS下载全部文件到本地（直接覆盖）
            try:
                from smb.SMBConnection import SMBConnection
                print("[DEBUG] 尝试连接NAS进行NAS-本地(保留本地)同步")
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                for f in self.nas_files:
                    local_path = os.path.join(self.local_folder, f['path'])
                    os.makedirs(os.path.dirname(local_path), exist_ok=True)
                    with open(local_path, 'wb') as fp:
                        print(f"[DEBUG] 正在下载: {f['path']} -> {local_path}")
                        try:
                            conn.retrieveFile(self.nas_share, '/' + f['path'], fp)
                            print(f"[DEBUG] 下载完成: {f['path']}")
                        except Exception as e:
                            print(f"[DEBUG] 下载失败: {f['path']} {e}")
                    self.update_progress()
                conn.close()
                print("[DEBUG] NAS-本地(保留本地)同步完成")
            except Exception as e:
                import traceback
                print(f"[DEBUG] NAS文件下载失败: {e}")
                traceback.print_exc()
        elif mode == '本地-NAS(清空NAS)':
            # 清空NAS后将本地所有文件上传到NAS
            try:
                from smb.SMBConnection import SMBConnection
                print("[DEBUG] 尝试连接NAS进行本地-NAS(清空NAS)同步")
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                # 先清空NAS
                for f in self.nas_files:
                    try:
                        print(f"[DEBUG] 删除NAS文件: {f['path']}")
                        conn.deleteFiles(self.nas_share, '/' + f['path'])
                    except Exception as e:
                        print(f"[DEBUG] 删除NAS文件失败: {f['path']} {e}")
                    self.update_progress()
                # 上传
                for f in self.local_files:
                    try:
                        remote_path = '/' + f['path']
                        dirs = remote_path.strip('/').split('/')[:-1]
                        cur_dir = ''
                        for d in dirs:
                            cur_dir += '/' + d
                            try:
                                print(f"[DEBUG] 创建NAS目录: {cur_dir}")
                                conn.createDirectory(self.nas_share, cur_dir)
                            except Exception as e:
                                print(f"[DEBUG] 创建NAS目录失败(可能已存在): {cur_dir} {e}")
                        local_file_path = os.path.join(self.local_folder, f['path'])
                        print(f"[DEBUG] 上传本地文件: {local_file_path} -> {remote_path}")
                        with open(local_file_path, 'rb') as fp:
                            conn.storeFile(self.nas_share, remote_path, fp)
                        print(f"[DEBUG] 上传完成: {f['path']}")
                    except Exception as e:
                        print(f"[DEBUG] 上传失败: {f['path']} {e}")
                    self.update_progress()
                conn.close()
                print("[DEBUG] 本地-NAS(清空NAS)同步完成")
            except Exception as e:
                import traceback
                print(f"[DEBUG] 本地文件上传到NAS失败: {e}")
                traceback.print_exc()
        elif mode == '本地-NAS(保留NAS)':
            # 保留NAS后将本地所有文件上传到NAS（直接覆盖）
            try:
                from smb.SMBConnection import SMBConnection
                print("[DEBUG] 尝试连接NAS进行本地-NAS(保留NAS)同步")
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                for f in self.local_files:
                    try:
                        remote_path = '/' + f['path']
                        dirs = remote_path.strip('/').split('/')[:-1]
                        cur_dir = ''
                        for d in dirs:
                            cur_dir += '/' + d
                            try:
                                print(f"[DEBUG] 创建NAS目录: {cur_dir}")
                                conn.createDirectory(self.nas_share, cur_dir)
                            except Exception as e:
                                print(f"[DEBUG] 创建NAS目录失败(可能已存在): {cur_dir} {e}")
                        local_file_path = os.path.join(self.local_folder, f['path'])
                        print(f"[DEBUG] 上传本地文件: {local_file_path} -> {remote_path}")
                        with open(local_file_path, 'rb') as fp:
                            conn.storeFile(self.nas_share, remote_path, fp)
                        print(f"[DEBUG] 上传完成: {f['path']}")
                    except Exception as e:
                        print(f"[DEBUG] 上传失败: {f['path']} {e}")
                    self.update_progress()
                conn.close()
                print("[DEBUG] 本地-NAS(保留NAS)同步完成")
            except Exception as e:
                import traceback
                print(f"[DEBUG] 本地文件上传到NAS失败: {e}")
                traceback.print_exc()
        elif mode == '双向同步(全保留)':
            # 名称相同，大小不同生成副本，都相同只保留一个
            try:
                from smb.SMBConnection import SMBConnection
                conn = SMBConnection(self.nas_user, self.nas_pass, "client", self.nas_ip, use_ntlm_v2=True)
                conn.connect(self.nas_ip, 445)
                # 上传本地独有
                for f in only_local:
                    try:
                        remote_path = '/' + f['path']
                        dirs = remote_path.strip('/').split('/')[:-1]
                        cur_dir = ''
                        for d in dirs:
                            cur_dir += '/' + d
                            try:
                                conn.createDirectory(self.nas_share, cur_dir)
                            except Exception:
                                pass
                        with open(os.path.join(self.local_folder, f['path']), 'rb') as fp:
                            print(f"上传本地独有文件: {f['path']}")
                            conn.storeFile(self.nas_share, remote_path, fp)
                    except Exception as e:
                        print(f"上传失败: {f['path']} {e}")
                    self.update_progress()
                # 下载NAS独有
                for f in only_nas:
                    try:
                        local_path = os.path.join(self.local_folder, f['path'])
                        os.makedirs(os.path.dirname(local_path), exist_ok=True)
                        with open(local_path, 'wb') as fp:
                            print(f"下载NAS独有文件: {f['path']}")
                            conn.retrieveFile(self.nas_share, '/' + f['path'], fp)
                    except Exception as e:
                        print(f"下载失败: {f['path']} {e}")
                    self.update_progress()
                # 名称相同但大小不同，生成副本
                for f in diff_size:
                    try:
                        # 下载NAS副本
                        local_path = os.path.join(self.local_folder, f['path'] + '.nas')
                        os.makedirs(os.path.dirname(local_path), exist_ok=True)
                        with open(local_path, 'wb') as fp:
                            print(f"下载NAS副本: {f['path']} -> {f['path']}.nas")
                            conn.retrieveFile(self.nas_share, '/' + f['path'], fp)
                        # 上传本地副本
                        remote_path = '/' + f['path'] + '.local'
                        with open(os.path.join(self.local_folder, f['path']), 'rb') as fp:
                            print(f"上传本地副本: {f['path']} -> {f['path']}.local")
                            conn.storeFile(self.nas_share, remote_path, fp)
                    except Exception as e:
                        print(f"同步副本失败: {f['path']} {e}")
                    self.update_progress()
                conn.close()
            except Exception as e:
                import traceback
                print(f"双向同步(全保留)失败: {e}")
                traceback.print_exc()
        elif mode == '双向同步(NAS为准)':
            # 以NAS内容为准，全部下载到本地，本地原有信息不保留
            print("[双向同步(NAS为准)] 以NAS为准，执行NAS-本地同步逻辑")
            # self.sync('NAS-本地')
            pass
        elif mode == '双向同步(本地为准)':
            # 以本地内容为准，全部上传到NAS，NAS原有信息不保留
            print("[双向同步(本地为准)] 以本地为准，执行本地-NAS同步逻辑")
            # self.sync('本地-NAS')
            pass
        # ...
    def update_progress(self):
        self.current_step += 1
        if self.progress_callback:
            percent = int((self.current_step / self.total_steps) * 100) if self.total_steps else 100
            self.progress_callback(percent, self.current_step, self.total_steps)


class ScannerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("飞牛统一管理里工具V1.6")
        self.root.geometry("1200x700")  # 更大窗口
        self.root.resizable(True, True)

        # 设置应用图标（可选）
        try:
            self.root.iconbitmap(r"D:\飞牛NAS设备记录日志\各品牌LOGO\favicon-ICO\飞牛-favicon.ico")  # 需准备图标文件
        except:
            pass

        # 创建标题栏
        header_frame = tk.Frame(root, bg="#90caf9", height=60)
        header_frame.pack(fill=tk.X)

        # 创建图标和标题
        try:
            icon_img = Image.open(r"D:\飞牛NAS设备记录日志\各品牌LOGO\favicon-ICO\飞牛-favicon.ico")
            icon_img = icon_img.resize((32, 32), Image.LANCZOS)
            self.icon_photo = ImageTk.PhotoImage(icon_img)
            icon_label = tk.Label(header_frame, image=self.icon_photo, bg="#90caf9")
            icon_label.pack(side=tk.LEFT, padx=10)
        except Exception as e:
            icon_label = tk.Label(header_frame, text="🐮", font=("Arial", 24), bg="#90caf9", fg="white")
            icon_label.pack(side=tk.LEFT, padx=10)

        title_label = tk.Label(header_frame, text="飞牛统一管理里工具V1.6",
                               font=("微软雅黑", 14, "bold"), bg="#90caf9", fg="white")
        title_label.pack(side=tk.LEFT, pady=15)

        # 创建控制面板
        control_frame = tk.Frame(root, padx=10, pady=10)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        # 新增：端口号输入框
        self.port_label = tk.Label(control_frame, text="端口号(逗号分隔):", font=("微软雅黑", 9))
        self.port_label.pack(side=tk.LEFT, padx=(10, 2))
        self.port_entry = tk.Entry(control_frame, width=20)  # 加宽
        self.port_entry.insert(0, "8000")
        self.port_entry.pack(side=tk.LEFT, padx=(0, 10))

        # 新增：全部打开管理页按钮
        self.open_all_button = tk.Button(
            control_frame,
            text="全部打开管理页",
            command=self.open_all_devices_web,
            bg="#27ae60",
            fg="white",
            font=("微软雅黑", 10),
            padx=20,
            pady=5,
            width=12
        )
        self.open_all_button.pack(side=tk.RIGHT, padx=5)

        # 添加刷新按钮
        refresh_button = tk.Button(
            control_frame,
            text="刷新设备列表",
            command=self.start_scan,  # 直接调用start_scan实现重新扫描
            bg="#3498db",
            fg="white",
            font=("微软雅黑", 10),
            padx=20,
            pady=5,
            width=12
        )
        refresh_button.pack(side=tk.RIGHT, padx=5)

        # 添加说明标签
        info_label = tk.Label(
            control_frame,
            text="双击设备可打开其管理页面",
            font=("微软雅黑", 9),
            fg="#7f8c8d"
        )
        info_label.pack(side=tk.RIGHT, padx=10)

        # 创建设备表格
        table_frame = tk.Frame(root)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # 添加表头
        columns = ("ip", "name", "cpu", "memory", "firmware", "status", "port", "last_seen")
        column_names = {
            "ip": "IP地址",
            "name": "设备名称",
            "cpu": "CPU型号",
            "memory": "内存",
            "firmware": "固件版本",
            "status": "状态",
            "port": "端口",
            "last_seen": "最后检测时间"
        }

        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", selectmode="browse")

        # 配置列标题和宽度（适当加宽）
        for col in columns:
            self.tree.heading(col, text=column_names[col])
            self.tree.column(col, width=140, minwidth=80, anchor=tk.CENTER, stretch=True)
        # 让所有列都自适应拉伸
        for col in columns:
            self.tree.column(col, stretch=True)
        self.tree.column("ip", width=180, minwidth=120)
        self.tree.column("name", width=220, minwidth=120)
        self.tree.column("last_seen", width=200, minwidth=120)
        self.tree.column("port", width=100, minwidth=60)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.pack(fill=tk.BOTH, expand=True)

        # 配置表格样式
        style = ttk.Style()
        style.configure("Treeview",
                        font=("微软雅黑", 10),
                        rowheight=25,
                        borderwidth=0)
        style.configure("Treeview.Heading",
                        font=("微软雅黑", 10, "bold"),
                        background="#ecf0f1")

        # 绑定双击事件
        self.tree.bind("<Double-1>", self.on_double_click)
        # 新增：绑定右键菜单
        self.tree.bind("<Button-3>", self.on_right_click)

        # 创建状态栏
        self.status_frame = tk.Frame(root, height=25, bg="#ecf0f1")
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # 新增：进度条控件
        self.progressbar = ttk.Progressbar(self.status_frame, orient="horizontal", length=120, mode="determinate", maximum=100)
        self.progressbar.pack(side=tk.LEFT, padx=(10, 5), pady=2)

        self.status_var = tk.StringVar()
        self.status_var.set("就绪 | 点击'开始扫描'搜索网络中的飞牛NAS设备")
        status_label = tk.Label(self.status_frame, textvariable=self.status_var,
                                bd=0, relief=tk.SUNKEN, anchor=tk.W, padx=10,
                                bg="#ecf0f1", fg="#2c3e50", font=("微软雅黑", 9))
        status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 创建扫描器
        self.scanner = FeiNiuScanner()
        self.device_count = 0
        self.scan_progress = 0
        self.total_ips = 0
        self.scanned_ips = 0

        # 在设备表格下方添加同步功能栏
        self.sync_frame = tk.Frame(root, padx=10, pady=10, bg="#f5f5f5")
        self.sync_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        # --- 同步区三行布局 ---
        self.sync_row1 = tk.Frame(self.sync_frame, bg="#f5f5f5")
        self.sync_row1.pack(fill=tk.X, pady=2)
        tk.Label(self.sync_row1, text="设备IP:", font=("微软雅黑", 9), bg="#f5f5f5").grid(row=0, column=0, padx=8, sticky='w')
        self.device_combo = ttk.Combobox(self.sync_row1, values=[], state="readonly", width=16)
        self.device_combo.grid(row=0, column=1, padx=8, sticky='w')
        self.device_combo.bind("<<ComboboxSelected>>", self.on_device_selected)
        tk.Label(self.sync_row1, text="用户名:", font=("微软雅黑", 9), bg="#f5f5f5").grid(row=0, column=2, padx=8, sticky='w')
        self.nas_user_entry = tk.Entry(self.sync_row1, width=12)
        self.nas_user_entry.grid(row=0, column=3, padx=8, sticky='w')
        tk.Label(self.sync_row1, text="密码:", font=("微软雅黑", 9), bg="#f5f5f5").grid(row=0, column=4, padx=8, sticky='w')
        self.nas_pass_entry = tk.Entry(self.sync_row1, width=12, show="*")
        self.nas_pass_entry.grid(row=0, column=5, padx=8, sticky='w')
        self.show_pass_var = tk.BooleanVar()
        tk.Checkbutton(self.sync_row1, text="显示密码", variable=self.show_pass_var, command=self.toggle_password, bg="#f5f5f5").grid(row=0, column=6, padx=8, sticky='w')
        # 新增：保存账号按钮和显示
        self.save_cred_btn = tk.Button(self.sync_row1, text="保存账号", command=self.save_current_cred, bg="#90caf9", font=("微软雅黑", 9))
        self.save_cred_btn.grid(row=0, column=7, padx=8, sticky='w')
        self.saved_cred_label = tk.Label(self.sync_row1, text="", font=("微软雅黑", 9), fg="#388e3c", bg="#f5f5f5")
        self.saved_cred_label.grid(row=1, column=1, columnspan=7, sticky='w', padx=8, pady=(2,0))
        # 第二行：NAS路径、本地路径及其选择按钮
        self.sync_row2 = tk.Frame(self.sync_frame, bg="#f5f5f5")
        self.sync_row2.pack(fill=tk.X, pady=2)
        tk.Label(self.sync_row2, text="NAS路径:", font=("微软雅黑", 9), bg="#f5f5f5").grid(row=0, column=0, padx=8, sticky='w')
        self.nas_path_var = tk.StringVar()
        self.nas_path_entry = tk.Entry(self.sync_row2, textvariable=self.nas_path_var, state='readonly')
        self.nas_path_entry.grid(row=0, column=1, padx=8, sticky='ew')
        self.nas_path_scroll = tk.Scrollbar(self.sync_row2, orient='horizontal', command=self.nas_path_entry.xview)
        self.nas_path_entry.config(xscrollcommand=self.nas_path_scroll.set)
        self.nas_path_scroll.grid(row=1, column=1, sticky='ew', padx=8)
        tk.Button(self.sync_row2, text="选择NAS目录", command=self.choose_nas_folder).grid(row=0, column=2, padx=8, sticky='w')
        tk.Label(self.sync_row2, text="本地路径:", font=("微软雅黑", 9), bg="#f5f5f5").grid(row=0, column=3, padx=8, sticky='w')
        self.local_path_var = tk.StringVar()
        self.local_path_entry = tk.Entry(self.sync_row2, textvariable=self.local_path_var, state='readonly')
        self.local_path_entry.grid(row=0, column=4, padx=8, sticky='ew')
        self.local_path_scroll = tk.Scrollbar(self.sync_row2, orient='horizontal', command=self.local_path_entry.xview)
        self.local_path_entry.config(xscrollcommand=self.local_path_scroll.set)
        self.local_path_scroll.grid(row=1, column=4, sticky='ew', padx=8)
        tk.Button(self.sync_row2, text="选择本地目录", command=self.choose_local_folder).grid(row=0, column=5, padx=8, sticky='w')
        self.sync_row2.columnconfigure(1, weight=1)
        self.sync_row2.columnconfigure(4, weight=1)
        # 第三行：同步方式、同步按钮、进度条
        self.sync_row3 = tk.Frame(self.sync_frame, bg="#f5f5f5")
        self.sync_row3.pack(fill=tk.X, pady=2)
        self.sync_progress = ttk.Progressbar(self.sync_row3, orient="horizontal", mode="determinate", maximum=100)
        self.sync_progress.grid(row=0, column=0, padx=12, sticky='ew')
        self.sync_percent_label = tk.Label(self.sync_row3, text="0%")
        self.sync_percent_label.grid(row=0, column=1, padx=8, sticky='w')
        # 新增：同步统计信息，放在同步方式按钮前
        self.sync_stat_label = tk.Label(self.sync_row3, text="剩余: 0/0 | 速度: 0/秒 | 剩余时间: --", font=("微软雅黑", 9), fg="#1976d2", bg="#f5f5f5")
        self.sync_stat_label.grid(row=0, column=2, padx=8, sticky='w')
        self.sync_mode_var = tk.StringVar()
        self.sync_mode_btn = tk.Button(self.sync_row3, text="选择同步方式", command=self.show_sync_mode_menu)
        self.sync_mode_btn.grid(row=0, column=3, padx=12, sticky='w')
        self.sync_btn = tk.Button(self.sync_row3, text="同步", command=self.start_sync)
        self.sync_btn.grid(row=0, column=4, padx=12, sticky='w')
        self.sync_row3.columnconfigure(0, weight=1)
        # 初始化凭据管理器
        self.cred_mgr = CredentialManager(
            os.path.join(self.scanner.log_path, 'key.bin'),
            os.path.join(self.scanner.log_path, '登录日志.json')
        )
        self.nas_folder_preloader = NASFolderPreloader(self.scanner, self.cred_mgr, lambda: self.scanner.devices, self.update_nas_preload_progress)
        self._preload_once_flag = False
        self.update_device_combo()
        self.sync_mode_menu = tk.Menu(self.sync_row3, tearoff=0)
        self.sync_modes = [
            ('NAS-本地(清空本地)', '清空本地后从NAS下载全部文件到本地'),
            ('NAS-本地(保留本地)', '保留本地后从NAS下载全部文件到本地'),
            ('本地-NAS(清空NAS)', '清空NAS后将本地所有文件上传到NAS'),
            ('本地-NAS(保留NAS)', '保留NAS后将本地所有文件上传到NAS'),
            ('双向同步(全保留)', '本地独有上传，NAS独有下载，冲突文件生成副本（.nas/.local）')
        ]
        for mode, desc in self.sync_modes:
            self.sync_mode_menu.add_command(label=f"{mode}    {desc}", command=lambda m=mode: self.set_sync_mode(m))
        self.sync_mode_var.set(self.sync_modes[0][0])
        # --- 表格点击自动带入IP ---
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)

        # --- 新增：NAS文件夹预读取进度区 ---
        self.nas_preload_frame = tk.Frame(control_frame, bg="#f5f5f5", height=44)
        self.nas_preload_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=8, pady=0)
        # 第一行：设备名+进度条+百分比
        self.nas_preload_row = tk.Frame(self.nas_preload_frame, bg="#f5f5f5")
        self.nas_preload_row.pack(side=tk.TOP, fill=tk.X)
        self.nas_preload_device_label = tk.Label(self.nas_preload_row, text="", bg="#f5f5f5", font=("微软雅黑", 10, "bold"), width=10, anchor='w', fg="#2c3e50")
        self.nas_preload_device_label.pack(side=tk.LEFT, padx=(6, 0), pady=4)
        self.nas_preload_progress = tk.DoubleVar(value=0)
        self.nas_preload_bar = ttk.Progressbar(self.nas_preload_row, orient="horizontal", mode="determinate", variable=self.nas_preload_progress, maximum=100)
        self.nas_preload_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=4, pady=4)
        self.nas_preload_percent_label = tk.Label(self.nas_preload_row, text="0%", bg="#f5f5f5", font=("微软雅黑", 10, "bold"), width=5, anchor='w', fg="#2c3e50")
        self.nas_preload_percent_label.pack(side=tk.LEFT, padx=(4, 6), pady=4)
        # 第二行：进度说明（只显示设备总数）
        self.nas_preload_label = tk.Label(self.nas_preload_frame, text="NAS文件夹预读取进度：0", bg="#f5f5f5", font=("微软雅黑", 10))
        self.nas_preload_label.pack(side=tk.TOP, anchor='w', padx=10, pady=(0, 4))
        # 状态Label
        self.nas_preload_status_label = tk.Label(self.nas_preload_frame, text="预读取进行中", bg="#f5f5f5", font=("微软雅黑", 10, "bold"), fg="#e67e22")
        self.nas_preload_status_label.pack(side=tk.TOP, anchor='w', padx=10, pady=(0, 4))

        # 自动启动扫描
        self.root.after(100, self.start_scan)

    def _auto_patch_devices_with_creds(self):
        creds = self.cred_mgr.creds
        for dev in self.scanner.devices:
            ip = dev.get('ip')
            if ip in creds:
                dev['user'] = creds[ip].get('user', '')
                dev['pass'] = creds[ip].get('pass', '')
                if not dev.get('name'):
                    dev['name'] = ip

    def start_scan(self):
        try:
            print("【DEBUG】start_scan 被调用", flush=True)
            self.status_var.set("扫描中... 0% | 正在搜索网络中的飞牛NAS设备")
            self.tree.delete(*self.tree.get_children())
            self.device_count = 0
            self.scan_progress = 0
            self.total_ips = 0
            self.scanned_ips = 0

            # 读取端口号（移除线程数读取）
            port_str = self.port_entry.get()
            try:
                ports = [int(p.strip()) for p in port_str.split(",") if p.strip().isdigit()]
            except Exception:
                ports = [8000]
            # 固定线程数为200
            self.scanner.thread_count = 200
            self.scanner.ports_to_scan = ports
            self.scanner.scanning = True

            # 启动扫描线程，传递进度回调和完成回调
            threading.Thread(
                target=self.scanner.scan_network,
                args=(self.update_device_list, self.update_scan_progress_real, self.on_scan_finished),
                daemon=True
            ).start()
        except Exception as e:
            print(f"【ERROR】start_scan异常: {e}", flush=True)

    def update_scan_progress_real(self, scanned, total):
        percent = int((scanned / total) * 100) if total else 0
        self.scan_progress = percent
        self.progressbar['value'] = percent
        self.status_var.set(f"扫描中... {self.scan_progress}% | 已发现 {self.device_count} 台设备")
        # 扫描完成后自动停止扫描
        if scanned >= total and self.scanner.scanning:
            self.scanner.scanning = False
            self.status_var.set(f"扫描完成 | 共找到 {self.device_count} 台设备 | 100%")
            self.progressbar['value'] = 100

    def update_device_list(self, devices):
        """更新设备列表"""
        self.device_count = len(devices)
        # 如果有新设备才更新表格（提高性能）
        if len(self.tree.get_children()) == len(devices):
            return
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        # 添加设备到表格
        for device in devices:
            self.tree.insert("", tk.END, values=(
                device['ip'],
                device['name'],
                device['cpu'],
                device['memory'],
                device['firmware'],
                device['status'],
                device['port'],
                device['last_seen']
            ))
            # 根据不同状态设置不同的行颜色
            if device['name'] == '飞牛NAS':
                self.tree.item(self.tree.get_children()[-1], tags=('feiniu',))
            elif device['name'] == '未知设备':
                self.tree.item(self.tree.get_children()[-1], tags=('unknown',))
            else:
                self.tree.item(self.tree.get_children()[-1], tags=('other',))
        # 配置标签样式
        self.tree.tag_configure('feiniu', background='#e8f5e9')
        self.tree.tag_configure('unknown', background='#fff8e1')
        self.tree.tag_configure('other', background='#e3f2fd')
        # --- 每次刷新表格时也刷新设备下拉框内容 ---
        self.update_device_combo()
        # 新增：每次刷新后自动补全账号并只触发一次批量预读取
        if not self._preload_once_flag:
            self._auto_patch_devices_with_creds()
            self.nas_folder_preloader.preload_all()
            self._preload_once_flag = True

    def on_double_click(self, event):
        """双击设备事件"""
        selected = self.tree.selection()
        if not selected:
            return

        item = selected[0]
        values = self.tree.item(item, "values")
        if values:
            ip = values[0]
            port = values[6]
            self.open_device_web(ip, port)

    def open_device_web(self, ip, port):
        """打开设备管理页面，优先用chrome，没有则用默认浏览器"""
        import webbrowser
        import shutil
        url = f"http://{ip}:{port}"
        try:
            chrome_path = shutil.which("chrome") or shutil.which("chrome.exe")
            if chrome_path:
                webbrowser.get(f'"{chrome_path}" %s').open(url)
                self.status_var.set(f"正在用Chrome打开: {url}")
            else:
                webbrowser.open(url)
                self.status_var.set(f"正在打开: {url}")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开浏览器: {str(e)}")

    def on_scan_finished(self):
        self.status_var.set(f"扫描完成 | 共找到 {self.device_count} 台设备 | 100%")
        self.progressbar['value'] = 100

    def on_right_click(self, event):
        # 右键菜单弹出，只显示当前列的修改项
        iid = self.tree.identify_row(event.y)
        col = self.tree.identify_column(event.x)
        if iid and col:
            self.tree.selection_set(iid)
            col_index = int(col.replace('#', '')) - 1
            columns = self.tree['columns']
            field = columns[col_index]
            field_map = {
                'name': '设备名称',
                'cpu': 'CPU型号',
                'memory': '内存',
                'firmware': '固件版本'
            }
            if field in field_map:
                menu = tk.Menu(self.root, tearoff=0)
                menu.add_command(label=f"修改{field_map[field]}", command=lambda: self.edit_device_field(field))
                menu.post(event.x_root, event.y_root)

    def edit_device_field(self, field):
        selected = self.tree.selection()
        if not selected:
            return
        item = selected[0]
        values = self.tree.item(item, "values")
        field_map = {
            'name': '设备名称',
            'cpu': 'CPU型号',
            'memory': '内存',
            'firmware': '固件版本'
        }
        current_value = ''
        idx = 0
        if field == 'name':
            current_value = values[1]
            idx = 1
        elif field == 'cpu':
            current_value = values[2]
            idx = 2
        elif field == 'memory':
            current_value = values[3]
            idx = 3
        elif field == 'firmware':
            current_value = values[4]
            idx = 4
        else:
            return
        win = tk.Toplevel(self.root)
        win.title(f"修改{field_map[field]}")
        win.geometry("340x170" if field == 'memory' else "320x140")
        win.transient(self.root)
        win.grab_set()
        if field == 'memory':
            # 拆分现有内存字符串
            import re
            m = re.match(r'DDR(\d+)-(\d+)GB', current_value)
            ddr = m.group(1) if m else ''
            size = m.group(2) if m else ''
            tk.Label(win, text=f"现内存：{current_value}", font=("微软雅黑", 10)).pack(pady=8)
            mem_frame = tk.Frame(win)
            mem_frame.pack(pady=5)
            tk.Label(mem_frame, text="内存代数：DDR", font=("微软雅黑", 10)).pack(side=tk.LEFT)
            entry_ddr = tk.Entry(mem_frame, width=4, font=("微软雅黑", 10))
            entry_ddr.insert(0, ddr)
            entry_ddr.pack(side=tk.LEFT, padx=2)
            tk.Label(mem_frame, text="-", font=("微软雅黑", 10)).pack(side=tk.LEFT)
            entry_size = tk.Entry(mem_frame, width=6, font=("微软雅黑", 10))
            entry_size.insert(0, size)
            entry_size.pack(side=tk.LEFT, padx=2)
            tk.Label(mem_frame, text="GB", font=("微软雅黑", 10)).pack(side=tk.LEFT)
        elif field == 'firmware':
            # 拆分现有固件版本字符串
            import re
            m = re.match(r'fnOS-(\d+)\.(\d+)\.(\d+)', current_value)
            major = m.group(1) if m else ''
            minor = m.group(2) if m else ''
            patch = m.group(3) if m else ''
            tk.Label(win, text=f"现固件版本：{current_value}", font=("微软雅黑", 10)).pack(pady=8)
            fw_frame = tk.Frame(win)
            fw_frame.pack(pady=5)
            tk.Label(fw_frame, text="主版本号：", font=("微软雅黑", 10)).pack(side=tk.LEFT)
            entry_major = tk.Entry(fw_frame, width=3, font=("微软雅黑", 10))
            entry_major.insert(0, major)
            entry_major.pack(side=tk.LEFT, padx=2)
            tk.Label(fw_frame, text=".", font=("微软雅黑", 10)).pack(side=tk.LEFT)
            entry_minor = tk.Entry(fw_frame, width=3, font=("微软雅黑", 10))
            entry_minor.insert(0, minor)
            entry_minor.pack(side=tk.LEFT, padx=2)
            tk.Label(fw_frame, text=".", font=("微软雅黑", 10)).pack(side=tk.LEFT)
            entry_patch = tk.Entry(fw_frame, width=3, font=("微软雅黑", 10))
            entry_patch.insert(0, patch)
            entry_patch.pack(side=tk.LEFT, padx=2)
        else:
            tk.Label(win, text=f"现{field_map[field]}：{current_value}", font=("微软雅黑", 10)).pack(pady=10)
            entry = tk.Entry(win, font=("微软雅黑", 10))
            entry.insert(0, current_value)
            entry.pack(pady=5, fill=tk.X, padx=20)
            entry.focus_set()
        def close():
            win.grab_release()
            win.destroy()
        def save():
            # 只保留外部JSON查找逻辑，删除原有cpu_map大字典
            if field == 'memory':
                ddr = entry_ddr.get().strip()
                size = entry_size.get().strip()
                if not ddr or not size or not ddr.isdigit() or not size.isdigit():
                    close()
                    return
                new_value = f"DDR{ddr}-{size}GB"
            elif field == 'firmware':
                major = entry_major.get().strip() or '0'
                minor = entry_minor.get().strip() or '0'
                patch = entry_patch.get().strip() or '0'
                if not major.isdigit() or not minor.isdigit() or not patch.isdigit():
                    close()
                    return
                new_value = f"fnOS-{major}.{minor}.{patch}"
            elif field == 'cpu':
                new_value = entry.get().strip()
                if new_value:
                    import json
                    cpu_json_path = r'D:\飞牛NAS设备记录日志\飞牛统一管理工具专用日志\数据库\cpumap标准json格式.txt'
                    cpu_map = {}
                    try:
                        with open(cpu_json_path, 'r', encoding='utf-8') as f:
                            cpu_db = json.load(f)
                        # 合并所有子分类为一个大字典
                        for group in cpu_db.values():
                            cpu_map.update(group)
                    except Exception as e:
                        print(f"[CPU MAP] 读取或解析cpumap标准json格式.txt失败: {e}")
                    key = new_value.upper().replace(' ', '')
                    # 先精确查找（去空格大写）
                    found = None
                    for k, v in cpu_map.items():
                        if k.upper().replace(' ', '') == key:
                            found = v
                            break
                    # 再模糊查找（包含关系）
                    if not found:
                        for k, v in cpu_map.items():
                            if key in k.upper().replace(' ', ''):
                                found = v
                                break
                    if found:
                        new_value = found
                    else:
                        # 没找到就保留原输入
                        pass
                if not new_value:
                    close()
                    return
            else:
                new_value = entry.get().strip()
                if not new_value:
                    close()
                    return
            new_values = list(values)
            new_values[idx] = new_value
            self.tree.item(item, values=new_values)
            for dev in self.scanner.devices:
                if dev['ip'] == values[0] and str(dev['port']) == str(values[6]):
                    dev[field] = new_value
                    self.scanner.device_log[dev['ip']] = {
                        'ip': dev['ip'],
                        'name': dev['name'],
                        'cpu': dev['cpu'],
                        'memory': dev['memory'],
                        'firmware': dev['firmware']
                    }
                    self.scanner.save_device_log()
            close()
        def save_all():
            if field == 'memory':
                ddr = entry_ddr.get().strip()
                size = entry_size.get().strip()
                if not ddr or not size or not ddr.isdigit() or not size.isdigit():
                    close()
                    return
                new_value = f"DDR{ddr}-{size}GB"
            elif field == 'firmware':
                major = entry_major.get().strip() or '0'
                minor = entry_minor.get().strip() or '0'
                patch = entry_patch.get().strip() or '0'
                if not major.isdigit() or not minor.isdigit() or not patch.isdigit():
                    close()
                    return
                new_value = f"fnOS-{major}.{minor}.{patch}"
            else:
                new_value = entry.get().strip()
                if not new_value:
                    close()
                    return
            for dev in self.scanner.devices:
                dev[field] = new_value
                self.scanner.device_log[dev['ip']] = {
                    'ip': dev['ip'],
                    'name': dev['name'],
                    'cpu': dev['cpu'],
                    'memory': dev['memory'],
                    'firmware': dev['firmware']
                }
            self.scanner.save_device_log()
            self.update_device_list(self.scanner.devices)
            close()
        btn_frame = tk.Frame(win)
        btn_frame.pack(pady=10)
        if field == 'firmware':
            tk.Button(btn_frame, text="仅本机修改", command=save, width=12).pack(side=tk.LEFT, padx=5)
            tk.Button(btn_frame, text="所有设备统一修改", command=save_all, width=16).pack(side=tk.LEFT, padx=5)
        else:
            tk.Button(btn_frame, text="确定", command=save, width=10).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="取消", command=close, width=10).pack(side=tk.LEFT, padx=5)
        def on_focus_out(event):
            if not win.focus_get() or (field != 'memory' and field != 'firmware' and win.focus_get() not in (entry,)):
                close()
        win.bind("<FocusOut>", on_focus_out)
        win.protocol("WM_DELETE_WINDOW", close)

    def open_all_devices_web(self):
        """一键打开所有设备管理页面"""
        for device in self.scanner.devices:
            ip = device['ip']
            port = device['port']
            self.open_device_web(ip, port)

    def update_device_combo(self):
        ips = [dev['ip'] for dev in self.scanner.devices]
        self.device_combo['values'] = ips
        # 不自动current(0)，由表格点击带入

    def on_device_selected(self, event=None):
        ip = self.device_combo.get()
        cred = self.cred_mgr.get_cred(ip)
        self.nas_user_entry.delete(0, tk.END)
        self.nas_user_entry.insert(0, cred['user'])
        self.nas_pass_entry.delete(0, tk.END)
        self.nas_pass_entry.insert(0, cred['pass'])

    def choose_nas_folder(self):
        import os
        import json
        ip = self.device_combo.get()
        device_name = ''
        # 从设备列表查找设备名
        for dev in self.scanner.devices:
            if dev.get('ip') == ip:
                device_name = dev.get('name', '')
                break
        safe_name = ''.join(c for c in device_name if c.isalnum() or c in '-_').strip() or ip
        log_dir = r'D:\飞牛NAS设备记录日志\飞牛统一管理工具专用日志\NAS文件夹预读取'
        json_path = os.path.join(log_dir, f'{safe_name}.json')
        if not os.path.exists(json_path):
            tk.messagebox.showerror("错误", "未找到该设备的预读取日志，请先进行NAS文件夹预读取！")
            return
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            tk.messagebox.showerror("错误", f"读取日志失败: {e}")
            return
        share_list = data.get('共享列表', [])
        if not share_list:
            tk.messagebox.showerror("错误", "日志中无共享信息，请确认已成功预读取！")
            return
        # 弹窗窗口
        win = tk.Toplevel(self.root)
        win.title("选择NAS目录（预读取日志）")
        width, height = 420, 320
        win.geometry(f"{width}x{height}")
        win.transient(self.root)
        win.grab_set()
        # 右侧对齐主窗口
        def place_popup():
            if not win.winfo_exists():
                return
            self.root.update_idletasks()
            root_x = self.root.winfo_x()
            root_y = self.root.winfo_y()
            root_w = self.root.winfo_width()
            win.update_idletasks()
            win_h = win.winfo_height()
            x = root_x + root_w
            y = root_y
            win.geometry(f"{width}x{win_h}+{x}+{y}")
        place_popup()
        def on_root_configure(event):
            place_popup()
        bind_id = self.root.bind('<Configure>', on_root_configure)
        def on_close():
            try:
                self.root.unbind('<Configure>', bind_id)
            except Exception:
                pass
            win.grab_release()
            win.destroy()
        win.protocol("WM_DELETE_WINDOW", on_close)
        tk.Label(win, text="请选择NAS共享名和子文件夹（数据来源：本地日志）", font=("微软雅黑", 10)).pack(pady=10, anchor='w')
        combo_frame = tk.Frame(win)
        combo_frame.pack(side=tk.TOP, fill=tk.X, expand=True)
        btn_frame = tk.Frame(win)
        btn_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=18)
        combo_vars = []
        combo_boxes = []
        # 递归生成目录链
        def add_combo(level, options):
            frame = tk.Frame(combo_frame)
            frame.pack(fill=tk.X, pady=4)
            var = tk.StringVar()
            combo = ttk.Combobox(frame, values=options, textvariable=var, state="readonly", width=32)
            combo.pack(side=tk.LEFT, padx=10)
            combo_vars.append(var)
            combo_boxes.append(combo)
            def on_select(event=None):
                idx = combo_boxes.index(combo)
                while len(combo_boxes) > idx+1:
                    combo_boxes[-1].master.destroy()
                    combo_boxes.pop()
                    combo_vars.pop()
                selected = var.get()
                if not selected:
                    return
                # 第一层：共享名
                if idx == 0:
                    # 找到该共享的目录树
                    dirs = []
                    for s in share_list:
                        if s.get('共享名') == selected:
                            dirs = s.get('目录', [])
                            break
                    folder_names = [d['名称'] for d in dirs]
                    if folder_names:
                        add_combo(idx+1, folder_names)
                else:
                    # 递归到子目录
                    share = combo_vars[0].get()
                    dirs = share_list
                    # 先找到共享
                    for s in dirs:
                        if s.get('共享名') == share:
                            dirs = s.get('目录', [])
                            break
                    # 逐级深入
                    for i in range(1, idx+1):
                        folder = combo_vars[i].get()
                        found = None
                        for d in dirs:
                            if d['名称'] == folder:
                                found = d
                                break
                        if found:
                            dirs = found.get('子目录', [])
                        else:
                            dirs = []
                            break
                    folder_names = [d['名称'] for d in dirs]
                    if folder_names:
                        add_combo(idx+1, folder_names)
            combo.bind("<<ComboboxSelected>>", on_select)
        # 第一层：共享名
        share_names = [s.get('共享名') for s in share_list]
        add_combo(0, share_names)
        def on_ok():
            if not combo_vars or not combo_vars[0].get():
                tk.messagebox.showerror("错误", "请选择NAS共享名！")
                return
            share = combo_vars[0].get()
            subfolders = [v.get() for v in combo_vars[1:] if v.get()]
            if subfolders:
                path = share + '/' + '/'.join(subfolders)
            else:
                path = share
            self.nas_path_var.set(path)
            try:
                self.root.unbind('<Configure>', bind_id)
            except Exception:
                pass
            win.grab_release()
            win.destroy()
        def on_cancel():
            try:
                self.root.unbind('<Configure>', bind_id)
            except Exception:
                pass
            win.grab_release()
            win.destroy()
        tk.Button(btn_frame, text="确定", command=on_ok, width=12).pack(side=tk.LEFT, padx=12)
        tk.Button(btn_frame, text="取消", command=on_cancel, width=12).pack(side=tk.LEFT, padx=12)

    def choose_local_folder(self):
        path = filedialog.askdirectory(title="请选择本地目录")
        if path:
            self.local_path_var.set(path)

    def toggle_password(self):
        if self.show_pass_var.get():
            self.nas_pass_entry.config(show="")
        else:
            self.nas_pass_entry.config(show="*")

    def show_sync_mode_menu(self):
        self.sync_mode_menu.post(self.sync_mode_btn.winfo_rootx(), self.sync_mode_btn.winfo_rooty() + self.sync_mode_btn.winfo_height())

    def set_sync_mode(self, mode):
        self.sync_mode_var.set(mode)
        self.sync_mode_btn.config(text=mode)
        self.sync_mode_menu.unpost()

    def start_sync(self):
        ip = self.device_combo.get()
        nas_user = self.nas_user_entry.get()
        nas_pass = self.nas_pass_entry.get()
        nas_folder = self.nas_path_var.get()
        local_folder = self.local_path_var.get()
        sync_mode = self.sync_mode_var.get()
        # 自动保存用户名密码
        self.cred_mgr.set_cred(ip, nas_user, nas_pass)
        # 解析NAS共享名
        if '/' in nas_folder:
            nas_share, nas_subfolder = nas_folder.split('/', 1)
        else:
            nas_share, nas_subfolder = nas_folder, ''
        # 启动同步线程
        def run_sync():
            import time
            self.sync_start_time = time.time()
            self.last_update_time = self.sync_start_time
            self.last_update_step = 0
            sync_mgr = SyncManager(ip, nas_share, nas_subfolder, nas_user, nas_pass, local_folder, self.scanner.log_path, self.update_sync_progress)
            sync_mgr.scan_local()
            sync_mgr.scan_smb()
            sync_mgr.sync(sync_mode)
            self.sync_progress['value'] = 100
            self.sync_percent_label.config(text="100%")
            self.sync_stat_label.config(text="剩余: 0/0 | 速度: 0/秒 | 剩余时间: --")
        threading.Thread(target=run_sync, daemon=True).start()

    def update_sync_progress(self, percent, current=None, total=None):
        import time
        self.sync_progress['value'] = percent
        self.sync_percent_label.config(text=f"{percent}%")
        # 统计信息
        if current is not None and total is not None:
            now = time.time()
            elapsed = now - getattr(self, 'sync_start_time', now)
            done = current
            remain = total - current
            # 速度
            speed = done / elapsed if elapsed > 0 else 0
            # 剩余时间
            remain_time = remain / speed if speed > 0 else 0
            if remain_time > 3600:
                remain_str = f"{int(remain_time//3600)}小时{int((remain_time%3600)//60)}分"
            elif remain_time > 60:
                remain_str = f"{int(remain_time//60)}分{int(remain_time%60)}秒"
            else:
                remain_str = f"{int(remain_time)}秒"
            self.sync_stat_label.config(text=f"剩余: {remain}/{total} | 速度: {speed:.1f}/秒 | 剩余时间: {remain_str}")
        else:
            self.sync_stat_label.config(text="剩余: -- | 速度: -- | 剩余时间: --")

    def on_tree_select(self, event):
        selected = self.tree.selection()
        if selected:
            item = selected[0]
            values = self.tree.item(item, "values")
            if values:
                ip = values[0]
                # 强制set并触发on_device_selected
                self.device_combo.set(ip)
                self.on_device_selected()

    def list_smb_shares(self, ip, user, passwd):
        """列出指定NAS的所有SMB共享名，保持原始大小写，并输出详细日志"""
        try:
            from smb.SMBConnection import SMBConnection
        except ImportError:
            messagebox.showerror("缺少依赖", "请先安装pysmb库: pip install pysmb")
            print("【SMB】缺少pysmb库")
            return []
        try:
            import socket
            client_name = socket.gethostname()
            server_name = ip
            passwd_masked = passwd[:2] + '*' * (len(passwd)-4) + passwd[-2:] if len(passwd) > 4 else '*'*len(passwd)
            print(f"【SMB】连接参数: user={user}, passwd={passwd_masked}, client_name={client_name}, server_name={server_name}")
            conn = SMBConnection(user, passwd, client_name, server_name, use_ntlm_v2=True)
            conn.connect(ip, 445)
            shares = conn.listShares()
            share_names = [s.name for s in shares if not s.isSpecial and s.name not in ("NETLOGON", "SYSVOL")]
            print(f"【SMB】获取到共享名列表: {share_names}")
            conn.close()
            return share_names
        except Exception as e:
            print(f"【SMB】获取共享名失败: {e}")
            traceback.print_exc()
            messagebox.showerror("SMB错误", f"获取共享名失败: {e}")
            return []

    def update_nas_preload_progress(self, device_index=1, device_total=1, device_name=None, folder_done=0, folder_total=1, all_done=False, **kwargs):
        # 优先显示文件夹递归进度
        if folder_total and folder_total > 1:
            percent = int((folder_done / folder_total) * 100)
            if percent > 100:
                percent = 100
        else:
            percent = int((device_index / device_total) * 100) if device_total else 0
        self.nas_preload_progress.set(percent)
        if device_name:
            show_name = device_name if len(device_name) <= 10 else device_name[:10] + '...'
            self.nas_preload_device_label.config(text=show_name)
            self._last_preload_device_name = show_name
        elif all_done:
            last_name = getattr(self, '_last_preload_device_name', '已完成')
            self.nas_preload_device_label.config(text=last_name)
        self.nas_preload_percent_label.config(text=f"{percent}%")
        self.nas_preload_label.config(text=f"NAS文件夹预读取进度：{device_index}/{device_total}")
        if all_done:
            self.nas_preload_status_label.config(text="预读取完成", fg="#27ae60")
        else:
            self.nas_preload_status_label.config(text="预读取进行中", fg="#e67e22")

    def save_current_cred(self):
        ip = self.device_combo.get()
        user = self.nas_user_entry.get()
        passwd = self.nas_pass_entry.get()
        if not ip or not user or not passwd:
            tk.messagebox.showerror("错误", "请填写完整的IP、用户名和密码！")
            return
        # 增量更新账号密码日志
        self.cred_mgr.creds[ip] = {"user": user, "pass": passwd}
        self.cred_mgr.save_creds()
        self.saved_cred_label.config(text=f"已保存账号：{ip} / {user}")
        tk.messagebox.showinfo("保存成功", f"账号信息已保存，可用于NAS预读取！\nIP: {ip}\n用户名: {user}")
        # 修复：保存账号后，强制补全self.scanner.devices中账号字段和设备名
        for dev in self.scanner.devices:
            if dev.get('ip') == ip:
                dev['user'] = user
                dev['pass'] = passwd
                if not dev.get('name'):
                    dev['name'] = ip
        # 保存账号后自动预读取当前设备
        dev = None
        for d in self.scanner.devices:
            if d.get('ip') == ip:
                dev = d
                break
        if dev:
            def single_preload():
                print(f"[DEBUG] 单设备预读取: ip={ip}, name={dev.get('name')}, user={dev.get('user')}, pass={'*' * len(dev.get('pass', ''))}")
                self.nas_folder_preloader._preload_device_with_progress(dev, 1, 1)
                self.update_nas_preload_progress(device_index=1, device_total=1, device_name=dev.get('name'), folder_done=1, folder_total=1, all_done=True)
            import threading
            threading.Thread(target=single_preload, daemon=True).start()


# ====== 代码配置区（可根据需要调整）======
NAS_PRELOAD_THREAD_COUNT = 8
MAX_NAS_SCAN_DEPTH = 2  # 建议2~3，0为不限制
# =======================

class NASFolderPreloader:
    def __init__(self, scanner, cred_mgr, device_list_func, progress_callback=None):
        print("[DEBUG] NASFolderPreloader: 初始化")
        self.scanner = scanner
        self.cred_mgr = cred_mgr
        self.device_list_func = device_list_func
        self.log_dir = r'D:\飞牛NAS设备记录日志\飞牛统一管理工具专用日志\NAS文件夹预读取'
        os.makedirs(self.log_dir, exist_ok=True)
        self.device_log_map = {}  # ip -> (name, json_path)
        self.progress_callback = progress_callback
        import threading
        self.log_lock = threading.Lock()

    def preload_all(self):
        import threading
        print("[DEBUG] NASFolderPreloader: preload_all 启动")
        devices = self.device_list_func()
        print(f"[DEBUG] 预读取设备列表: {devices}")
        unique_devices = []
        seen_ips = set()
        for dev in devices:
            ip = dev.get('ip')
            if not dev.get('name'):
                dev['name'] = ip
            cred = self.cred_mgr.get_cred(ip)
            user, passwd = cred.get('user'), cred.get('pass')
            user = dev.get('user', user)
            passwd = dev.get('pass', passwd)
            if ip and ip not in seen_ips and user and passwd:
                print(f"[DEBUG] 预读取入队: ip={ip}, name={dev.get('name')}, user={user}, pass={'*' * len(passwd)}")
                dev['user'] = user
                dev['pass'] = passwd
                unique_devices.append(dev)
                seen_ips.add(ip)
                safe_name = ''.join(c for c in dev['name'] if c.isalnum() or c in '-_').strip() or ip
                json_path = os.path.join(self.log_dir, f'{safe_name}.json')
                self.device_log_map[ip] = (dev['name'], json_path)
                with self.log_lock:
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump({'设备IP': ip, '设备名称': dev['name'], '共享列表': []}, f, ensure_ascii=False, indent=2)
        device_total = len(unique_devices)
        print(f"[DEBUG] 需预读取设备数: {device_total}")
        # 递归前先回调一次，进度为0
        if self.progress_callback:
            self.progress_callback(
                device_index=0,
                device_total=device_total,
                device_name='',
                folder_done=0,
                folder_total=1,
                all_done=False
            )
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor(max_workers=NAS_PRELOAD_THREAD_COUNT) as executor:
            futures = []
            for idx, dev in enumerate(unique_devices, 1):
                device_name = dev.get('name', '')
                if self.progress_callback:
                    self.progress_callback(
                        device_index=idx,
                        device_total=device_total,
                        device_name=device_name,
                        folder_done=0,
                        folder_total=1,
                        all_done=False
                    )
                futures.append(executor.submit(self._preload_device_with_progress, dev, idx, device_total))
            for idx, f in enumerate(futures, 1):
                try:
                    f.result()
                    if self.progress_callback:
                        self.progress_callback(
                            device_index=idx,
                            device_total=device_total,
                            device_name=unique_devices[idx-1].get('name', ''),
                            folder_done=1,
                            folder_total=1,
                            all_done=False
                        )
                except Exception as e:
                    print(f"[ERROR] 设备线程异常: {e}")
        # 递归全部完成后，才回调all_done=True
        if self.progress_callback:
            self.progress_callback(
                device_index=device_total,
                device_total=device_total,
                device_name=None,
                folder_done=1,
                folder_total=1,
                all_done=True
            )
        print("预读取完成")

    def _preload_device_with_progress(self, dev, device_index, device_total):
        ip = dev.get('ip')
        if not dev.get('name'):
            dev['name'] = ip
        user = dev.get('user') or self.cred_mgr.get_cred(ip).get('user')
        passwd = dev.get('pass') or self.cred_mgr.get_cred(ip).get('pass')
        print(f"[DEBUG] 预读取设备: ip={ip}, name={dev.get('name')}, user={user}, pass={'*' * len(passwd or '')}")
        if not (user and passwd):
            print(f"[DEBUG] 跳过无账号设备: {ip}")
            return
        try:
            from smb.SMBConnection import SMBConnection
            import socket
            client_name = socket.gethostname()
            server_name = ip
            conn = SMBConnection(user, passwd, client_name, server_name, use_ntlm_v2=True)
            conn.connect(ip, 445)
            shares = [s.name for s in conn.listShares() if not s.isSpecial and s.name not in ("NETLOGON", "SYSVOL")]
            print(f"[DEBUG] 设备 {ip} 共享名: {shares}")
            for share in shares:
                share_struct = {'共享名': share, '目录': []}
                # 统计文件夹总数
                def count_folders(share, parent_path, layer):
                    if MAX_NAS_SCAN_DEPTH > 0 and layer > MAX_NAS_SCAN_DEPTH:
                        return 0
                    try:
                        files = conn.listPath(share, parent_path)
                        count = 0
                        for f in files:
                            if f.filename in ('.', '..'):
                                continue
                            full_path = parent_path + '/' + f.filename if parent_path else f.filename
                            if f.isDirectory:
                                count += 1
                                count += count_folders(share, full_path, layer+1)
                        return count
                    except Exception:
                        return 0
                folder_total = count_folders(share, '', 1)
                folder_counter = {'done': 0}
                def scan_layer(share, parent_path, parent_struct, layer):
                    if MAX_NAS_SCAN_DEPTH > 0 and layer > MAX_NAS_SCAN_DEPTH:
                        return
                    try:
                        files = conn.listPath(share, parent_path)
                        changed = False
                        for f in files:
                            if f.filename in ('.', '..'):
                                continue
                            full_path = parent_path + '/' + f.filename if parent_path else f.filename
                            if f.isDirectory:
                                print(f"[NAS预读取] 设备 {ip} 共享 {share} 目录: {full_path}")
                                folder_counter['done'] += 1
                                if self.progress_callback:
                                    self.progress_callback(
                                        device_index=device_index,
                                        device_total=device_total,
                                        device_name=dev.get('name'),
                                        folder_done=folder_counter['done'],
                                        folder_total=max(folder_total, 1)
                                    )
                                exist = None
                                for d in parent_struct:
                                    if d.get('名称') == f.filename:
                                        exist = d
                                        break
                                if not exist:
                                    new_dir = {'名称': f.filename, '子目录': []}
                                    parent_struct.append(new_dir)
                                    changed = True
                                else:
                                    new_dir = exist
                                scan_layer(share, full_path, new_dir['子目录'], layer+1)
                            else:
                                print(f"[NAS预读取] 设备 {ip} 共享 {share} 文件: {full_path}")
                        if changed:
                            # 日志每次都覆盖，不合并
                            self._save_log_threadsafe_overwrite(ip, dev.get('name'), [share_struct])
                    except Exception as e:
                        print(f'[NAS预读取] 递归层{layer}失败: {e}')
                scan_layer(share, '', share_struct['目录'], 1)
                # 日志每次都覆盖，不合并
                self._save_log_threadsafe_overwrite(ip, dev.get('name'), [share_struct])
            conn.close()
        except Exception as e:
            print(f'[NAS预读取] 设备 {ip} 失败: {e}')

    # 新增：日志每次都覆盖，不合并
    def _save_log_threadsafe_overwrite(self, ip, device_name, share_structs):
        import json
        safe_name = ''.join(c for c in device_name if c.isalnum() or c in '-_').strip() or ip
        json_path = os.path.join(self.log_dir, f'{safe_name}.json')
        with self.log_lock:
            data = {'设备IP': ip, '设备名称': device_name, '共享列表': share_structs}
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    root = tk.Tk()

    # 设置Windows下更好的DPI处理
    if sys.platform.startswith('win'):
        from ctypes import windll

        windll.shcore.SetProcessDpiAwareness(1)

    app = ScannerApp(root)
    root.mainloop()