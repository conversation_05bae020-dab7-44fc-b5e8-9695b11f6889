import smbclient
import re
import os
import logging
from openpyxl import Workbook
from openpyxl.styles import Alignment
from smbclient import scandir, remove
import datetime
from collections import defaultdict

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,  # 修改为INFO级别
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置信息
config = {
    'enabled': True,
    'server_ip': '**************',
    'server_name': 'D1581-fnos-91',
    'share_name': '小说',
    'username': 'admin',
    'password': 'ly120220',
    'parent_dir': '漫画/肉漫天堂',  # 只需指定父目录
    'client_name': 'ComicDownloader'
}

CATEGORY_MAP = {
    '肉漫天堂韩漫文件夹': '韩漫',
    '肉漫天堂日漫文件夹': '日漫',
    '肉漫天堂3D漫画文件夹': '3D漫画',
    '肉漫天堂真人文件夹': '真人',
    '肉漫天堂短篇文件夹': '短篇',
}

def get_category_from_path(path, filename):
    for key, value in CATEGORY_MAP.items():
        if key in path:
            return value
    # 文件名前缀兜底
    for value in CATEGORY_MAP.values():
        if filename.startswith(value + '-'):
            return value
    return '未知'

def parse_file_info(filename):
    """解析文件名，返回主名+BOOKID和结束页码，兼容中英文括号和短横线"""
    filename = filename.strip()
    match = re.match(r'^(.*?)[-－][（(]1-(\d+)话[）)]-肉漫天堂-(\d+)', filename)
    if match:
        main_name = f"{match.group(1)}-肉漫天堂-{match.group(3)}"
        end_page = int(match.group(2))
        return main_name, end_page
    # 兜底：保留原逻辑
    base_name = filename.split('+')[0].strip()
    page_match = re.search(r'(\d+)-(\d+)(?:话)?[）)]?', filename)
    if page_match:
        return base_name, int(page_match.group(2))
    return base_name, None


def main():
    try:
        logger.info("开始处理NAS文件去重任务")
        smbclient.register_session(
            server=config['server_name'],
            username=config['username'],
            password=config['password']
        )
        # 构建父目录SMB路径
        parent_smb_path = f"\\\\{config['server_name']}\\{config['share_name']}\\{config['parent_dir'].replace('/', '\\')}"
        # 获取所有子文件夹
        subfolders = [entry.name for entry in scandir(parent_smb_path) if entry.is_dir() and entry.name in CATEGORY_MAP]
        logger.info(f"发现子文件夹: {subfolders}")
        all_log_data = []
        serial_num = 1
        category_stats = []  # 新增：用于收集所有分类统计信息
        for subfolder in subfolders:
            smb_path = f"{parent_smb_path}\\{subfolder}"
            logger.info(f"正在扫描目录: {smb_path}")
            file_groups = defaultdict(list)
            file_count = 0
            deleted_count = 0  # 新增：统计删除数量
            for entry in scandir(smb_path):
                if entry.is_file():
                    file_count += 1
                    base, _ = parse_file_info(entry.name)
                    print(f"分组key: [{base}] 文件名: [{entry.name}]")  # 调试输出
                    mtime = entry.stat().st_mtime
                    file_groups[base].append((entry.name, mtime))
            logger.info(f"扫描完成，共发现{file_count}个文件")
            # 输出多文件分组调试信息
            for base_name, files in file_groups.items():
                if len(files) > 1:
                    print(f"多文件分组: [{base_name}] 包含: {[f[0] for f in files]}")
            processed_groups = 0
            for base_name, files in file_groups.items():
                if len(files) < 2:
                    continue
                processed_groups += 1
                files_with_page = []
                for filename, mtime in files:
                    _, end_page = parse_file_info(filename)
                    files_with_page.append((filename, mtime, end_page if end_page is not None else -1))
                sorted_files = sorted(files_with_page, key=lambda x: (x[2], x[1]), reverse=True)
                max_page = sorted_files[0][2]
                max_page_files = [f for f in sorted_files if f[2] == max_page]
                max_page_files_sorted = sorted(max_page_files, key=lambda x: x[1], reverse=True)
                keep_file = max_page_files_sorted[0][0]
                category = get_category_from_path(subfolder, keep_file)
                for filename, mtime, end_page in sorted_files:
                    status = '已保留' if filename == keep_file else '已删除'
                    all_log_data.append({
                        'serial': serial_num if filename == keep_file else '',
                        'name': filename,
                        'status': status,
                        'mtime': datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        'end_page': end_page if end_page != -1 else '',
                        'category': category
                    })
                for filename, _, _ in sorted_files:
                    if filename != keep_file:
                        try:
                            logger.info(f"正在删除文件: {filename}")
                            remove(f"{smb_path}\\{filename}")
                            deleted_count += 1  # 新增：删除成功则计数
                        except Exception as e:
                            logger.error(f"删除失败 {filename}: {str(e)}")
                serial_num += 1
            logger.info(f"处理完成: {subfolder}，共处理{processed_groups}个重复文件组")
            # 新增：收集分类统计信息到列表
            category_stats.append(f"分类[{subfolder}]：扫描前文件总数={file_count}，删除总数={deleted_count}，删除后总数={file_count - deleted_count}")
        # 生成Excel报表
        logger.info("开始生成Excel报表")
        wb = Workbook()
        ws = wb.active
        ws.title = "重复文件处理结果"
        ws.append(["序号", "漫画名称", "状态", "最后修改时间", "最后章节数字", "分类"])
        ws.column_dimensions['A'].width = 10
        ws.column_dimensions['B'].width = 80
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 22
        ws.column_dimensions['E'].width = 15
        ws.column_dimensions['F'].width = 10
        for log in all_log_data:
            row = [log['serial'], log['name'], log['status'], log['mtime'], log['end_page'], log['category']]
            ws.append(row)
        for row in ws.iter_rows(min_row=2):
            row[0].alignment = Alignment(horizontal='center')
            row[2].alignment = Alignment(horizontal='center')
            row[3].alignment = Alignment(horizontal='center')
            row[4].alignment = Alignment(horizontal='center')
            row[5].alignment = Alignment(horizontal='center')
        date_str = datetime.datetime.now().strftime("%Y%m%d")
        output_dir = r'C:\Users\<USER>\Downloads'
        output_file = os.path.join(output_dir, f"D1581-肉漫天堂去重-{date_str}.xlsx")
        os.makedirs(output_dir, exist_ok=True)
        wb.save(output_file)
        logger.info(f"报表已保存至: {output_file}")
        print(f"\n处理完成，结果已保存至：{output_file}")
        # 新增：统一输出所有分类统计信息
        for stat in category_stats:
            print(stat)
    except Exception as e:
        logger.exception("程序运行出现异常:")
        raise


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
    except Exception as e:
        logger.error(f"程序异常终止: {str(e)}")