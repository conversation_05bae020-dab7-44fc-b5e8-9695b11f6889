import smbclient
import re
import os
import logging
from openpyxl import Workbook
from openpyxl.styles import Alignment
from smbclient import scandir, remove
import datetime
from collections import defaultdict

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置信息（更新路径和文件名标识）
config = {
    'enabled': True,
    'server_ip': '**************',
    'server_name': 'D1581-fnos-91',
    'share_name': '小说',
    'username': 'admin',
    'password': 'ly120220',
    'target_dir': '漫画/开心看漫画韩漫',  # 更新路径层级
    'client_name': 'ComicDownloader'
}


def parse_file_info(filename):
    """解析文件名返回基础名称和结束页码"""
    # 提取基础名称（分隔符前的内容）
    base_name = re.split(r'[+（(]', filename)[0].strip()

    # 提取结束页码
    page_match = re.search(r'(\d+)-(\d+)(?:话)?[）)]?', filename)
    if page_match:
        return base_name, int(page_match.group(2))
    return None, None


def main():
    try:
        logger.info("开始处理NAS文件去重任务")

        # 初始化SMB连接
        logger.info(f"正在连接NAS服务器: {config['server_name']}")
        smbclient.register_session(
            server=config['server_name'],
            username=config['username'],
            password=config['password']
        )

        # 构建SMB路径（包含新路径层级）
        smb_path = f"\\\\{config['server_name']}\\{config['share_name']}\\{config['target_dir'].replace('/', '\\')}"
        logger.info(f"操作目录: {smb_path}")

        # 获取文件列表
        logger.info("正在扫描目标目录...")
        file_groups = defaultdict(list)
        file_count = 0
        deleted_count = 0  # 新增：统计删除数量
        for entry in scandir(smb_path):
            if entry.is_file():
                file_count += 1
                base, page = parse_file_info(entry.name)
                if base and page:
                    file_groups[base].append((entry.name, page))
        logger.info(f"扫描完成，共发现{file_count}个文件")

        # 处理重复文件
        logger.info("开始处理重复文件")
        log_data = []
        serial_num = 1
        processed_groups = 0

        for base_name, files in file_groups.items():
            if len(files) < 2:
                continue
            processed_groups += 1

            # 按页码降序排列
            sorted_files = sorted(files, key=lambda x: x[1], reverse=True)

            # 记录日志
            for idx, (filename, page) in enumerate(sorted_files):
                status = '已保留' if idx == 0 else '已删除'
                log_data.append({
                    'serial': serial_num if idx == 0 else '',
                    'name': filename,
                    'status': status
                })

            # 执行删除操作
            for filename, _ in sorted_files[1:]:
                try:
                    logger.info(f"正在删除文件: {filename}")
                    remove(f"{smb_path}\\{filename}")
                    deleted_count += 1  # 新增：删除成功则计数
                except Exception as e:
                    logger.error(f"删除失败 {filename}: {str(e)}")

            serial_num += 1
        logger.info(f"处理完成，共处理{processed_groups}个重复文件组")

        # 生成Excel报表（更新文件名标识）
        logger.info("开始生成Excel报表")
        wb = Workbook()
        ws = wb.active
        ws.title = "重复文件处理结果"
        ws.append(["序号", "漫画名称", "状态"])

        # 设置列宽
        ws.column_dimensions['A'].width = 10
        ws.column_dimensions['B'].width = 80
        ws.column_dimensions['C'].width = 15

        # 填充数据
        for log in log_data:
            row = [log['serial'], log['name'], log['status']]
            ws.append(row)

        # 设置对齐方式
        for row in ws.iter_rows(min_row=2):
            row[0].alignment = Alignment(horizontal='center')
            row[2].alignment = Alignment(horizontal='center')

        # 生成文件名和路径（更新文件名标识）
        date_str = datetime.datetime.now().strftime("%Y%m%d")
        output_dir = r'C:\Users\<USER>\Downloads'
        output_file = os.path.join(output_dir,
                                   f"D1581-开心看漫画韩漫去重-开心看漫画-{date_str}.xlsx")  # 更新标识符

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 保存文件
        wb.save(output_file)
        logger.info(f"报表已保存至: {output_file}")
        print(f"\n处理完成，结果已保存至：{output_file}")
        # 新增：统一输出统计信息
        print(f"扫描前文件总数={file_count}，删除总数={deleted_count}，删除后总数={file_count - deleted_count}")

    except Exception as e:
        logger.exception("程序运行出现异常:")
        raise


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
    except Exception as e:
        logger.error(f"程序异常终止: {str(e)}")